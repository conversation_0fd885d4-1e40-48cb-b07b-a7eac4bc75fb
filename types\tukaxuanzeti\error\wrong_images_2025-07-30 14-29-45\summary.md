**使用模型ID：** doubao-1-5-vision-pro-32k-250115

**使用图片文件夹：** roboflow_yolo_result/images_2025-07-30_14-27-49

## 错题

- 第 7 张图片: 05153dc6d14b41b3b20412ff5ed200e5_jpg.rf.f0c9f43b8a7da3a953be41245dbed104_with_roboflow_yolo.jpg
- 第 9 张图片: 06a1a2339ec1435989b1f9bc161063a8_jpg.rf.2505f9855c6209892cd0e23c3ffebd7a_with_roboflow_yolo.jpg
- 第 19 张图片: 11e17cf9ee0647c89409cd7989675198_jpg.rf.469c6527f1f40513d857c12cfadd7c81_with_roboflow_yolo.jpg
- 第 24 张图片: 1634e6461ad54dc49cef8ab6883a8f1e_jpg.rf.f361ac5194ea21ebadb631a1d456df46_with_roboflow_yolo.jpg
- 第 38 张图片: 1d4d50cbfd474a2790647d94701e496d_jpg.rf.3055cab0ed606998a48b4382a3f33ee1_with_roboflow_yolo.jpg
- 第 45 张图片: 2163a4881a0d40b29915ea74a4bd00ba_jpg.rf.81bf8f8231633630974627fa4cf06506_with_roboflow_yolo.jpg
- 第 50 张图片: 25b9aa25e01940afa63fb72de473c1e3_jpg.rf.899e8562a248abb4db8b727478aa2327_with_roboflow_yolo.jpg
- 第 67 张图片: 30f117908cbf48679756c5db7c293eb2_jpg.rf.7b9446c40bec62baed01cb6daee5ba03_with_roboflow_yolo.jpg
- 第 81 张图片: 3a1303cd2e264385ab1b994f31d6b754_jpg.rf.c26d1c4ee7646bdb792bc74edc1913b9_with_roboflow_yolo.jpg
- 第 98 张图片: 4e7b7f1726c843d5a11c90da82935bd1_jpg.rf.862ce20b4fc9cc8776ad44183b9c3efb_with_roboflow_yolo.jpg
- 第 114 张图片: 575b2e187f3e4a7cb5f6873b85d4e5ba_jpg.rf.7d59b599ab4dc0114020f9a800cd03eb_with_roboflow_yolo.jpg
- 第 131 张图片: 66db8cfa1d354225b3df15396b6b8179_jpg.rf.61572cad2390e11c63646187bd869fbe_with_roboflow_yolo.jpg
- 第 160 张图片: 7664adfd1d614ff4ab75186a88ce351d_jpg.rf.a8d82ea30ee878af72370877d5cef2e5_with_roboflow_yolo.jpg
- 第 177 张图片: 822b4ffb83024e9c9bdfcfa70c6b5453_jpg.rf.3781a8408c5a1511aa35ab243f53de63_with_roboflow_yolo.jpg
- 第 180 张图片: 884ec69595884ad18d5b3183e2330f40_jpg.rf.ee2315ec064df492de0c87b6b463cd0f_with_roboflow_yolo.jpg
- 第 185 张图片: 8c2098c4a6cc40538e8f02b96543eccf_jpg.rf.7545b0caad317feae3ffe24e93d8e715_with_roboflow_yolo.jpg
- 第 195 张图片: 9bff6ab4083843aa9d7f5074dd84ee05_jpg.rf.92812ac2a2c3af48f0b8b3722c1be0e7_with_roboflow_yolo.jpg
- 第 196 张图片: 9c5b7d6d6a4b41ba8e7634e05ae5be48_jpg.rf.ca75a7aee8c66ce02bce133c46a84686_with_roboflow_yolo.jpg
- 第 203 张图片: a1796741becf499cb90e3c8b009d2317_jpg.rf.117e8b8c96bb8a043aa899ddc2c32899_with_roboflow_yolo.jpg
- 第 209 张图片: a44c8fa42a8e48fd92843873becd3f2d_jpg.rf.f8e9317c1f8c94811e74948e07224701_with_roboflow_yolo.jpg
- 第 222 张图片: ab15a8cac6304741af5e177052295bea_jpg.rf.b20f4513ad6ae7d106e9b5e45790737c_with_roboflow_yolo.jpg
- 第 233 张图片: b46b16dd03a64a36a8fcda25a7802a51_jpg.rf.b77720eea92534a387263ebba1baac41_with_roboflow_yolo.jpg
- 第 237 张图片: b8255453bf5b4a6992c64a639a63f576_jpg.rf.2f966af6b0eb8b7c1d8f9f7fe9327900_with_roboflow_yolo.jpg
- 第 260 张图片: c6a7e9c819b2417faf44fedc85f675df_jpg.rf.ca35d9f5fa1cd26a811130e06756e245_with_roboflow_yolo.jpg
- 第 288 张图片: dcb34c29218c4d179df79e41b0ae6b4c_jpg.rf.6c599461402417abed2796cb88a76ccc_with_roboflow_yolo.jpg
- 第 299 张图片: e626dd85e3d643ec8da2c91d98f6f077_jpg.rf.bb0122cf7b2c107dcce64e400192aa27_with_roboflow_yolo.jpg
- 第 320 张图片: f21b01f44de34a1da52f737bc38f885e_jpg.rf.55b059edb76a2f924829d6a4bb3f8700_with_roboflow_yolo.jpg
- 第 327 张图片: f6b7901438bc4655bc7cb0c13ba616b7_jpg.rf.9be5d2621c6bab2d898fd19b27124cb9_with_roboflow_yolo.jpg

## 准确率：91.69%  （(337 - 28) / 337）

# 运行时间: 2025-07-30_14-29-45


==================================================
处理第 7 张图片: 05153dc6d14b41b3b20412ff5ed200e5_jpg.rf.f0c9f43b8a7da3a953be41245dbed104_with_roboflow_yolo.jpg

==================================================
![05153dc6d14b41b3b20412ff5ed200e5_jpg.rf.f0c9f43b8a7da3a953be41245dbed104_with_roboflow_yolo.jpg](05153dc6d14b41b3b20412ff5ed200e5_jpg.rf.f0c9f43b8a7da3a953be41245dbed104_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"NAN","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "A", "题目2": "NAN", "题目3": "B", "题目4": "A", "题目5": "A"}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 981
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 9 张图片: 06a1a2339ec1435989b1f9bc161063a8_jpg.rf.2505f9855c6209892cd0e23c3ffebd7a_with_roboflow_yolo.jpg

==================================================
![06a1a2339ec1435989b1f9bc161063a8_jpg.rf.2505f9855c6209892cd0e23c3ffebd7a_with_roboflow_yolo.jpg](06a1a2339ec1435989b1f9bc161063a8_jpg.rf.2505f9855c6209892cd0e23c3ffebd7a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "B", "题目3": "A", "题目4": "B", "题目5": "A"}
```
### 响应时间：1.91秒
### token用量
- total_tokens: 980
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 19 张图片: 11e17cf9ee0647c89409cd7989675198_jpg.rf.469c6527f1f40513d857c12cfadd7c81_with_roboflow_yolo.jpg

==================================================
![11e17cf9ee0647c89409cd7989675198_jpg.rf.469c6527f1f40513d857c12cfadd7c81_with_roboflow_yolo.jpg](11e17cf9ee0647c89409cd7989675198_jpg.rf.469c6527f1f40513d857c12cfadd7c81_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"C","题目2":"A","题目3":"B","题目4":"A","题目5":"C"}
```
### 响应内容：
```json
{"题目1": "C", "题目2": "NAN", "题目3": "B", "题目4": "A", "题目5": "C"}
```
### 响应时间：2.12秒
### token用量
- total_tokens: 1208
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 24 张图片: 1634e6461ad54dc49cef8ab6883a8f1e_jpg.rf.f361ac5194ea21ebadb631a1d456df46_with_roboflow_yolo.jpg

==================================================
![1634e6461ad54dc49cef8ab6883a8f1e_jpg.rf.f361ac5194ea21ebadb631a1d456df46_with_roboflow_yolo.jpg](1634e6461ad54dc49cef8ab6883a8f1e_jpg.rf.f361ac5194ea21ebadb631a1d456df46_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"B","题目2":"A","题目3":"C","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "A", "题目3": "C", "题目4": "NAN", "题目5": "NAN"}
```
### 响应时间：2.41秒
### token用量
- total_tokens: 1284
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 38 张图片: 1d4d50cbfd474a2790647d94701e496d_jpg.rf.3055cab0ed606998a48b4382a3f33ee1_with_roboflow_yolo.jpg

==================================================
![1d4d50cbfd474a2790647d94701e496d_jpg.rf.3055cab0ed606998a48b4382a3f33ee1_with_roboflow_yolo.jpg](1d4d50cbfd474a2790647d94701e496d_jpg.rf.3055cab0ed606998a48b4382a3f33ee1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"C","题目2":"B","题目3":"A","题目4":"B","题目5":"C"}
```
### 响应内容：
```json
{"题目1": "C", "题目2": "B", "题目3": "A", "题目4": "B", "题目5": "A"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 45 张图片: 2163a4881a0d40b29915ea74a4bd00ba_jpg.rf.81bf8f8231633630974627fa4cf06506_with_roboflow_yolo.jpg

==================================================
![2163a4881a0d40b29915ea74a4bd00ba_jpg.rf.81bf8f8231633630974627fa4cf06506_with_roboflow_yolo.jpg](2163a4881a0d40b29915ea74a4bd00ba_jpg.rf.81bf8f8231633630974627fa4cf06506_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"B","题目2":"C","题目3":"D","题目4":"A"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "C", "题目3": "D", "题目4": "NAN"}
```
### 响应时间：2.02秒
### token用量
- total_tokens: 991
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 50 张图片: 25b9aa25e01940afa63fb72de473c1e3_jpg.rf.899e8562a248abb4db8b727478aa2327_with_roboflow_yolo.jpg

==================================================
![25b9aa25e01940afa63fb72de473c1e3_jpg.rf.899e8562a248abb4db8b727478aa2327_with_roboflow_yolo.jpg](25b9aa25e01940afa63fb72de473c1e3_jpg.rf.899e8562a248abb4db8b727478aa2327_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "B", "题目3": "B", "题目4": "A", "题目5": "B"}
```
### 响应时间：1.40秒
### token用量
- total_tokens: 980
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 67 张图片: 30f117908cbf48679756c5db7c293eb2_jpg.rf.7b9446c40bec62baed01cb6daee5ba03_with_roboflow_yolo.jpg

==================================================
![30f117908cbf48679756c5db7c293eb2_jpg.rf.7b9446c40bec62baed01cb6daee5ba03_with_roboflow_yolo.jpg](30f117908cbf48679756c5db7c293eb2_jpg.rf.7b9446c40bec62baed01cb6daee5ba03_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "A", "题目3": "B", "题目4": "A", "题目5": "B"}
```
### 响应时间：2.76秒
### token用量
- total_tokens: 980
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 81 张图片: 3a1303cd2e264385ab1b994f31d6b754_jpg.rf.c26d1c4ee7646bdb792bc74edc1913b9_with_roboflow_yolo.jpg

==================================================
![3a1303cd2e264385ab1b994f31d6b754_jpg.rf.c26d1c4ee7646bdb792bc74edc1913b9_with_roboflow_yolo.jpg](3a1303cd2e264385ab1b994f31d6b754_jpg.rf.c26d1c4ee7646bdb792bc74edc1913b9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"未识别到有效涂卡内容"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 773
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 98 张图片: 4e7b7f1726c843d5a11c90da82935bd1_jpg.rf.862ce20b4fc9cc8776ad44183b9c3efb_with_roboflow_yolo.jpg

==================================================
![4e7b7f1726c843d5a11c90da82935bd1_jpg.rf.862ce20b4fc9cc8776ad44183b9c3efb_with_roboflow_yolo.jpg](4e7b7f1726c843d5a11c90da82935bd1_jpg.rf.862ce20b4fc9cc8776ad44183b9c3efb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目11": "NAN", "题目12": "A", "题目13": "B", "题目14": "A", "题目15": "B"}
```
### 响应时间：2.25秒
### token用量
- total_tokens: 986
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 114 张图片: 575b2e187f3e4a7cb5f6873b85d4e5ba_jpg.rf.7d59b599ab4dc0114020f9a800cd03eb_with_roboflow_yolo.jpg

==================================================
![575b2e187f3e4a7cb5f6873b85d4e5ba_jpg.rf.7d59b599ab4dc0114020f9a800cd03eb_with_roboflow_yolo.jpg](575b2e187f3e4a7cb5f6873b85d4e5ba_jpg.rf.7d59b599ab4dc0114020f9a800cd03eb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"C","题目3":"B","题目4":"B","题目5":"A"}
```
### 响应内容：
```json
{"题目1": "A", "题目2": "C", "题目3": "B", "题目4": "B", "题目5": "A", "题目6": "NAN"}
```
### 响应时间：1.38秒
### token用量
- total_tokens: 952
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 131 张图片: 66db8cfa1d354225b3df15396b6b8179_jpg.rf.61572cad2390e11c63646187bd869fbe_with_roboflow_yolo.jpg

==================================================
![66db8cfa1d354225b3df15396b6b8179_jpg.rf.61572cad2390e11c63646187bd869fbe_with_roboflow_yolo.jpg](66db8cfa1d354225b3df15396b6b8179_jpg.rf.61572cad2390e11c63646187bd869fbe_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "A", "题目2": "NAN", "题目3": "NAN", "题目4": "A", "题目5": "B"}
```
### 响应时间：2.00秒
### token用量
- total_tokens: 982
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 160 张图片: 7664adfd1d614ff4ab75186a88ce351d_jpg.rf.a8d82ea30ee878af72370877d5cef2e5_with_roboflow_yolo.jpg

==================================================
![7664adfd1d614ff4ab75186a88ce351d_jpg.rf.a8d82ea30ee878af72370877d5cef2e5_with_roboflow_yolo.jpg](7664adfd1d614ff4ab75186a88ce351d_jpg.rf.a8d82ea30ee878af72370877d5cef2e5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "B", "题目3": "B", "题目4": "A", "题目5": "B"}
```
### 响应时间：1.50秒
### token用量
- total_tokens: 980
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 177 张图片: 822b4ffb83024e9c9bdfcfa70c6b5453_jpg.rf.3781a8408c5a1511aa35ab243f53de63_with_roboflow_yolo.jpg

==================================================
![822b4ffb83024e9c9bdfcfa70c6b5453_jpg.rf.3781a8408c5a1511aa35ab243f53de63_with_roboflow_yolo.jpg](822b4ffb83024e9c9bdfcfa70c6b5453_jpg.rf.3781a8408c5a1511aa35ab243f53de63_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"D","题目2":"B","题目3":"C","题目4":"A","题目5":"B","题目6":"D","题目7":"A","题目8":"C"}
```
### 响应内容：
```json
{"题目1": "D", "题目2": "B", "题目3": "C", "题目4": "A", "题目5": "NAN", "题目6": "NAN", "题目7": "A", "题目8": "C"}
```
### 响应时间：1.76秒
### token用量
- total_tokens: 1009
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 180 张图片: 884ec69595884ad18d5b3183e2330f40_jpg.rf.ee2315ec064df492de0c87b6b463cd0f_with_roboflow_yolo.jpg

==================================================
![884ec69595884ad18d5b3183e2330f40_jpg.rf.ee2315ec064df492de0c87b6b463cd0f_with_roboflow_yolo.jpg](884ec69595884ad18d5b3183e2330f40_jpg.rf.ee2315ec064df492de0c87b6b463cd0f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"B","题目2":"D","题目3":"C","题目4":"A","题目5":"C","题目6":"D","题目7":"B","题目8":"A"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "D", "题目3": "C", "题目4": "A", "题目5": "C", "题目6": "NAN", "题目7": "B", "题目8": "A"}
```
### 响应时间：2.44秒
### token用量
- total_tokens: 1009
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 185 张图片: 8c2098c4a6cc40538e8f02b96543eccf_jpg.rf.7545b0caad317feae3ffe24e93d8e715_with_roboflow_yolo.jpg

==================================================
![8c2098c4a6cc40538e8f02b96543eccf_jpg.rf.7545b0caad317feae3ffe24e93d8e715_with_roboflow_yolo.jpg](8c2098c4a6cc40538e8f02b96543eccf_jpg.rf.7545b0caad317feae3ffe24e93d8e715_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"C","题目2":"A","题目3":"A","题目4":"A","题目5":"C"}
```
### 响应内容：
```json
{"题目1": "C", "题目2": "A", "题目3": "A", "题目4": "A", "题目5": "NAN"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 1208
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 195 张图片: 9bff6ab4083843aa9d7f5074dd84ee05_jpg.rf.92812ac2a2c3af48f0b8b3722c1be0e7_with_roboflow_yolo.jpg

==================================================
![9bff6ab4083843aa9d7f5074dd84ee05_jpg.rf.92812ac2a2c3af48f0b8b3722c1be0e7_with_roboflow_yolo.jpg](9bff6ab4083843aa9d7f5074dd84ee05_jpg.rf.92812ac2a2c3af48f0b8b3722c1be0e7_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"D","题目2":"B","题目3":"E","题目4":"B","题目5":"C"}
```
### 响应内容：
```json
{"题目1": "D", "题目2": "B", "题目3": "NAN", "题目4": "NAN", "题目5": "NAN"}
```
### 响应时间：2.09秒
### token用量
- total_tokens: 1295
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 196 张图片: 9c5b7d6d6a4b41ba8e7634e05ae5be48_jpg.rf.ca75a7aee8c66ce02bce133c46a84686_with_roboflow_yolo.jpg

==================================================
![9c5b7d6d6a4b41ba8e7634e05ae5be48_jpg.rf.ca75a7aee8c66ce02bce133c46a84686_with_roboflow_yolo.jpg](9c5b7d6d6a4b41ba8e7634e05ae5be48_jpg.rf.ca75a7aee8c66ce02bce133c46a84686_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "A", "题目2": "B", "题目3": "B", "题目4": "A", "题目5": "B"}
```
### 响应时间：1.73秒
### token用量
- total_tokens: 980
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 203 张图片: a1796741becf499cb90e3c8b009d2317_jpg.rf.117e8b8c96bb8a043aa899ddc2c32899_with_roboflow_yolo.jpg

==================================================
![a1796741becf499cb90e3c8b009d2317_jpg.rf.117e8b8c96bb8a043aa899ddc2c32899_with_roboflow_yolo.jpg](a1796741becf499cb90e3c8b009d2317_jpg.rf.117e8b8c96bb8a043aa899ddc2c32899_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"E","题目2":"A","题目3":"E","题目4":"B","题目5":"C"}
```
### 响应内容：
```json
{"题目1": "D", "题目2": "A", "题目3": "E", "题目4": "B", "题目5": "C"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 1292
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 209 张图片: a44c8fa42a8e48fd92843873becd3f2d_jpg.rf.f8e9317c1f8c94811e74948e07224701_with_roboflow_yolo.jpg

==================================================
![a44c8fa42a8e48fd92843873becd3f2d_jpg.rf.f8e9317c1f8c94811e74948e07224701_with_roboflow_yolo.jpg](a44c8fa42a8e48fd92843873becd3f2d_jpg.rf.f8e9317c1f8c94811e74948e07224701_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"B","题目2":"B","题目3":"A","题目4":"C","题目5":"A"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "B", "题目3": "NAN", "题目4": "C", "题目5": "NAN"}
```
### 响应时间：2.47秒
### token用量
- total_tokens: 1284
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 222 张图片: ab15a8cac6304741af5e177052295bea_jpg.rf.b20f4513ad6ae7d106e9b5e45790737c_with_roboflow_yolo.jpg

==================================================
![ab15a8cac6304741af5e177052295bea_jpg.rf.b20f4513ad6ae7d106e9b5e45790737c_with_roboflow_yolo.jpg](ab15a8cac6304741af5e177052295bea_jpg.rf.b20f4513ad6ae7d106e9b5e45790737c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"C","题目2":"A","题目3":"B","题目4":"A","题目5":"C"}
```
### 响应内容：
```json
{"题目1": "C", "题目2": "A", "题目3": "错误", "题目4": "错误", "题目5": "C"}
```
### 响应时间：1.79秒
### token用量
- total_tokens: 1207
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 233 张图片: b46b16dd03a64a36a8fcda25a7802a51_jpg.rf.b77720eea92534a387263ebba1baac41_with_roboflow_yolo.jpg

==================================================
![b46b16dd03a64a36a8fcda25a7802a51_jpg.rf.b77720eea92534a387263ebba1baac41_with_roboflow_yolo.jpg](b46b16dd03a64a36a8fcda25a7802a51_jpg.rf.b77720eea92534a387263ebba1baac41_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"C","题目2":"A","题目3":"A","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "C", "题目2": "A", "题目3": "NAN", "题目4": "NAN", "题目5": "B"}
```
### 响应时间：1.75秒
### token用量
- total_tokens: 1284
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 237 张图片: b8255453bf5b4a6992c64a639a63f576_jpg.rf.2f966af6b0eb8b7c1d8f9f7fe9327900_with_roboflow_yolo.jpg

==================================================
![b8255453bf5b4a6992c64a639a63f576_jpg.rf.2f966af6b0eb8b7c1d8f9f7fe9327900_with_roboflow_yolo.jpg](b8255453bf5b4a6992c64a639a63f576_jpg.rf.2f966af6b0eb8b7c1d8f9f7fe9327900_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"B","题目2":"A B","题目3":"C","题目4":"C","题目5":"C"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "A", "题目3": "C", "题目4": "C", "题目5": "D"}
```
### 响应时间：2.03秒
### token用量
- total_tokens: 944
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 260 张图片: c6a7e9c819b2417faf44fedc85f675df_jpg.rf.ca35d9f5fa1cd26a811130e06756e245_with_roboflow_yolo.jpg

==================================================
![c6a7e9c819b2417faf44fedc85f675df_jpg.rf.ca35d9f5fa1cd26a811130e06756e245_with_roboflow_yolo.jpg](c6a7e9c819b2417faf44fedc85f675df_jpg.rf.ca35d9f5fa1cd26a811130e06756e245_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"B","题目2":"D","题目3":"C","题目4":"A"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "D", "题目3": "C", "题目4": "NAN"}
```
### 响应时间：2.43秒
### token用量
- total_tokens: 786
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 288 张图片: dcb34c29218c4d179df79e41b0ae6b4c_jpg.rf.6c599461402417abed2796cb88a76ccc_with_roboflow_yolo.jpg

==================================================
![dcb34c29218c4d179df79e41b0ae6b4c_jpg.rf.6c599461402417abed2796cb88a76ccc_with_roboflow_yolo.jpg](dcb34c29218c4d179df79e41b0ae6b4c_jpg.rf.6c599461402417abed2796cb88a76ccc_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "A", "题目2": "B", "题目3": "A", "题目4": "B", "题目5": "A"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 980
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 299 张图片: e626dd85e3d643ec8da2c91d98f6f077_jpg.rf.bb0122cf7b2c107dcce64e400192aa27_with_roboflow_yolo.jpg

==================================================
![e626dd85e3d643ec8da2c91d98f6f077_jpg.rf.bb0122cf7b2c107dcce64e400192aa27_with_roboflow_yolo.jpg](e626dd85e3d643ec8da2c91d98f6f077_jpg.rf.bb0122cf7b2c107dcce64e400192aa27_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"B","题目2":"B","题目3":"B","题目4":"B","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "B", "题目3": "B", "题目4": "B", "题目5": "B", "题目6": "B"}
```
### 响应时间：2.12秒
### token用量
- total_tokens: 951
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 320 张图片: f21b01f44de34a1da52f737bc38f885e_jpg.rf.55b059edb76a2f924829d6a4bb3f8700_with_roboflow_yolo.jpg

==================================================
![f21b01f44de34a1da52f737bc38f885e_jpg.rf.55b059edb76a2f924829d6a4bb3f8700_with_roboflow_yolo.jpg](f21b01f44de34a1da52f737bc38f885e_jpg.rf.55b059edb76a2f924829d6a4bb3f8700_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"B","题目3":"C","题目4":"D","题目5":"C","题目6":"A","题目7":"B","题目8":"D"}
```
### 响应内容：
```json
{
    "题目1": "A",
    "题目2": "B",
    "题目3": "C",
    "题目4": "D",
    "题目5": "C",
    "题目6": "NAN",
    "题目7": "B",
    "题目8": "D"
}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 1347
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 327 张图片: f6b7901438bc4655bc7cb0c13ba616b7_jpg.rf.9be5d2621c6bab2d898fd19b27124cb9_with_roboflow_yolo.jpg

==================================================
![f6b7901438bc4655bc7cb0c13ba616b7_jpg.rf.9be5d2621c6bab2d898fd19b27124cb9_with_roboflow_yolo.jpg](f6b7901438bc4655bc7cb0c13ba616b7_jpg.rf.9be5d2621c6bab2d898fd19b27124cb9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目1":"A","题目2":"A","题目3":"B","题目4":"A","题目5":"B"}
```
### 响应内容：
```json
{"题目1": "B", "题目2": "B", "题目3": "A", "题目4": "B", "题目5": "A"}
```
### 响应时间：1.98秒
### token用量
- total_tokens: 980
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
所有错题处理完成！
==================================================
