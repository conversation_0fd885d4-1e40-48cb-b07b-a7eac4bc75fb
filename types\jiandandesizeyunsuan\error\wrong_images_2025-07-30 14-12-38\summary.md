**使用模型ID：** doubao-1-5-vision-pro-32k-250115

**使用图片文件夹：** roboflow_yolo_result/images_2025-07-30_14-12-23

## 错题

- 第 1 张图片: 0225922a75514853a54396f709099952_jpg.rf.75679ec2e503aa1662ba2b52922794f5_with_roboflow_yolo.jpg
- 第 2 张图片: 03209e96deb948498e1a241c7ef4e28e_jpg.rf.8459922aa657f0432425d157e3f63dcc_with_roboflow_yolo.jpg
- 第 3 张图片: 037a8d2a77664b8caf6c032e1677e096_jpg.rf.a073630cde7938ba84e9aa018458da7b_with_roboflow_yolo.jpg
- 第 4 张图片: 03a6a879aaa74c23b04fc37b6cf2b7b5_jpg.rf.041efa982eaeca3ebf83e4f41ebe588e_with_roboflow_yolo.jpg
- 第 5 张图片: 049a6e14e83a4c3492dae37fba50459b_jpg.rf.2bf6bf09e2e9e7fcf63d4617eb463871_with_roboflow_yolo.jpg
- 第 7 张图片: 066e3de949c04685ae8b9bef93f1a52f_jpg.rf.b920bfb8668e81ebe2ea102893ced2e1_with_roboflow_yolo.jpg
- 第 8 张图片: 08abab793d18480fb5f5b71036c5ac76_jpg.rf.8b2a91340bc132d44dfc65ddbbc48676_with_roboflow_yolo.jpg
- 第 10 张图片: 0dc58b3ba20e40bf85484b710ebcc76f_jpg.rf.098efe4b62fc01e94d9ad5c1f3107d4d_with_roboflow_yolo.jpg
- 第 11 张图片: 0fd0b3e3b1a145cc9840505ca7adfb8b_jpg.rf.e5fd1083de0879ebec9fb2b94b89faf5_with_roboflow_yolo.jpg
- 第 12 张图片: 0fe3646519c5442d98c0c03a60e3ab69_jpg.rf.f0989b013ffb007ebee70f8e6e8ae5ba_with_roboflow_yolo.jpg
- 第 13 张图片: 107606add9bb47dc8b2852be9b25d10b_jpg.rf.db1d3a5ff61e883f9fef9947dd21dfa3_with_roboflow_yolo.jpg
- 第 14 张图片: 133d6135af184226b017cc1ba80afeb2_jpg.rf.bf522076bcb2c68d31849c2f66894deb_with_roboflow_yolo.jpg
- 第 15 张图片: 13a990bbdefd47ac9ebb9dc7169ee095_jpg.rf.3962931e0316ce3c679673beaf67d0c2_with_roboflow_yolo.jpg
- 第 16 张图片: 14620e1a4abc4c4a939f77d2ff688eb7_jpg.rf.5a9c6ba96c1610f5ac714415619465ef_with_roboflow_yolo.jpg
- 第 17 张图片: 15984089651d47d0b3afec82fca85195_jpg.rf.2026faac6290ee5e630277b6bcedc2ae_with_roboflow_yolo.jpg
- 第 18 张图片: 16e208765c6f46d0bc8d80f6ac01a6c2_jpg.rf.ee6ad5ce50c99c4e97ea4b8415b5f812_with_roboflow_yolo.jpg
- 第 20 张图片: 1981cec7d2c94b068d7ba6e6e987c39b_jpg.rf.7c4f5203e51dd474d9de30f3519a07c9_with_roboflow_yolo.jpg
- 第 21 张图片: 1a746dc5ea8a490ea7a933e55c85939d_jpg.rf.e58039a484d8268eb9a96849e4e99e24_with_roboflow_yolo.jpg
- 第 22 张图片: 1a8a17e034624b3ea8c10446eb11f25c_jpg.rf.7509fb6f7b3925b8f164aa14b848513f_with_roboflow_yolo.jpg
- 第 23 张图片: 1ba92a72197844e4ab2dc1def43a7087_jpg.rf.1f9dd20833b037f58617a4b92e2c1f8f_with_roboflow_yolo.jpg
- 第 25 张图片: 1d321a3fcce14d0bbdc4b9ed33cc32c6_jpg.rf.cae5a82a439a97a56532984d657f6dae_with_roboflow_yolo.jpg
- 第 28 张图片: 1e9e2c1721e342d1b60cf09eb00b40f1_jpg.rf.fc33b7881be9ee39f96b15a97e862c20_with_roboflow_yolo.jpg
- 第 29 张图片: 1eb33d5f0bea4440a80f85978330c642_jpg.rf.10007b9f4d17dda5b9bbbabfe6339ee0_with_roboflow_yolo.jpg
- 第 30 张图片: 1fb58c9c8aa3437fba540ac43ea191c4_jpg.rf.5de8b2532871b30687efa63ba04b1080_with_roboflow_yolo.jpg
- 第 31 张图片: 214ffd89f6834e578d939a118a7c1982_jpg.rf.25fab8a217608675f3cbf132c13c1f20_with_roboflow_yolo.jpg
- 第 32 张图片: 21d82e1bc0164fa493194861208c4f52_jpg.rf.f3a128623bf92f9c53ecb86b86e408b8_with_roboflow_yolo.jpg
- 第 33 张图片: 240fd91397bb4b838450e32f588acac5_jpg.rf.ddb2b7d0cd0a304ff758b7fea610fd45_with_roboflow_yolo.jpg
- 第 34 张图片: 253d6553790a471a888a2f4aa4f4e59c_jpg.rf.3a5bfcbdf7b151568b332fe9976159d2_with_roboflow_yolo.jpg
- 第 36 张图片: 28275a7ccfe84555b36ec1854bb3a14b_jpg.rf.6e95e8b7abe28bb543d6a4855a8b5624_with_roboflow_yolo.jpg
- 第 37 张图片: 2857fddd1ff74c9ba827bcc627230b23_jpg.rf.87f51fe23ea3e0a6c83b3e0639bac2b6_with_roboflow_yolo.jpg
- 第 38 张图片: 29a0f9cc1cfd4d908c027c0b484cf0af_jpg.rf.d9deff2ef998567966342abbc91bcc7d_with_roboflow_yolo.jpg
- 第 39 张图片: 2a5fa54d82284affb690558eaa49ecbf_jpg.rf.ad23c44cb22964591995f9a34b12112c_with_roboflow_yolo.jpg
- 第 40 张图片: 2bcd9d8c4ede49efa21a0ebd69c7766f_jpg.rf.8dbe7966b3406e5f00d3777da2bd6495_with_roboflow_yolo.jpg
- 第 41 张图片: 2bd364c0afea48d38a1be02e309bed16_jpg.rf.27bf7c10ad3a333e77a3661918f92cd2_with_roboflow_yolo.jpg
- 第 42 张图片: 2d4285da05ee44fa97ee92e19178c89c_jpg.rf.934cdbaa09306261cd3396ca4941ab8e_with_roboflow_yolo.jpg
- 第 44 张图片: 2e1161cea94c4bcea26587ade6d58dfd_jpg.rf.00e99ea06022b3103674f0eb965dd068_with_roboflow_yolo.jpg
- 第 45 张图片: 2f26a976da5c43df92987953cfb26e2c_jpg.rf.923dc510263b22000dbab813e1322821_with_roboflow_yolo.jpg
- 第 46 张图片: 2f61a4d5c19e432e9585ecb0559c200e_jpg.rf.01bcfcc0091e0f611444e53c6730aaf4_with_roboflow_yolo.jpg
- 第 48 张图片: 305978696b244bc1aa74b8ca4ccd8c3b_jpg.rf.eaa7cd247b87fdb29cf6ace4f37da555_with_roboflow_yolo.jpg
- 第 49 张图片: 3352be9115304b67aa815f956eaf6c43_jpg.rf.d5a52530faae3e838263caf12c284b6e_with_roboflow_yolo.jpg
- 第 50 张图片: 3508bd0ecd9e4b71a292c14d19096720_jpg.rf.e1d04b3c4c4b1f53a95151619b2d29ec_with_roboflow_yolo.jpg
- 第 51 张图片: 359829674c30477eaa60d68d622a369a_jpg.rf.02103b83f1dcc0b8a957e660a69e8337_with_roboflow_yolo.jpg
- 第 52 张图片: 378ec761313c4f499110292958c04b3c_jpg.rf.5a14bfac9cbe0f12fcebde7ee6b5314b_with_roboflow_yolo.jpg
- 第 53 张图片: 37b78e00e1f84752a72f759e40489274_jpg.rf.ada45104979b9c4c23607414e9552c19_with_roboflow_yolo.jpg
- 第 54 张图片: 3be9649d312b46c0a9087839a2796555_jpg.rf.d0fc4d91ff53fb6928bcc5c1606e61cd_with_roboflow_yolo.jpg
- 第 55 张图片: 3d9ed6db90434a2f8b57c1422d910140_jpg.rf.1867ed91ee209f77b91a27a3f6c3dd95_with_roboflow_yolo.jpg
- 第 56 张图片: 3fa6a3bb49334977b5a6441db731dc7a_jpg.rf.2ea8ad71ef7e87db2160e74769a299dd_with_roboflow_yolo.jpg
- 第 57 张图片: 408a6a4ce09b46c187fe10c1d9616a69_jpg.rf.8336ae0cc750a98cdf926092c2828860_with_roboflow_yolo.jpg
- 第 58 张图片: 431a6f802e3e45d2b97bf0700b1ee797_jpg.rf.ec56f19148e2ffd855dde2eccd35d126_with_roboflow_yolo.jpg
- 第 63 张图片: 47aaf3c73f2342cebc3dc8bdf6c4d090_jpg.rf.67dfd313b20c480fc773b03a8cc6b988_with_roboflow_yolo.jpg
- 第 64 张图片: 47b4d8662eaa452d9c8def39b7a51cb0_jpg.rf.a1927ebb0c9cffa18727e373e572b961_with_roboflow_yolo.jpg
- 第 65 张图片: 47b833f6d2fe4fc78bf4fc814aa90f9f_jpg.rf.c07e57d5c80774bb6cd76b20dd58720e_with_roboflow_yolo.jpg
- 第 67 张图片: 47fe582aa08e427d890254e90dbe026b_jpg.rf.1aec906e82b21e18f56ad85e7e28f1b7_with_roboflow_yolo.jpg
- 第 68 张图片: 48392a0f182c4342853e31879fde8bea_jpg.rf.abac1d39969a06615a042709c4e2ae4c_with_roboflow_yolo.jpg
- 第 70 张图片: 48e1127bbe354ccebb98b1b7374a0dc3_jpg.rf.afca558ed96239b6eb4d9751c7c278db_with_roboflow_yolo.jpg
- 第 71 张图片: 491bc134a0684784a6fab6be4de59980_jpg.rf.57b44546060ee0bb20afbe5013b5e1f5_with_roboflow_yolo.jpg
- 第 72 张图片: 4b6f701dfcae4c4ebaa84d0c16dd0318_jpg.rf.3c191f922824c21a241f8c7de5cc59b6_with_roboflow_yolo.jpg
- 第 73 张图片: 4d2b22c11dd34e5083d5d33ac5ef9da5_jpg.rf.60fe01fc574c51cef603c24b77e2d051_with_roboflow_yolo.jpg
- 第 74 张图片: 4dc55c3e7b4b45d6ac63b3c8a3d46620_jpg.rf.dcf507d613b8dbf28c603aca07e40cb0_with_roboflow_yolo.jpg
- 第 75 张图片: 4e254c3789a94603b9c6811c2f595ae0_jpg.rf.593010669f6c62854d7e2089b0dde8fa_with_roboflow_yolo.jpg
- 第 76 张图片: 4e5c091224a14e3bbaab103d9301dcce_jpg.rf.0312e52d910b30df445ce9acef5c1527_with_roboflow_yolo.jpg
- 第 77 张图片: 4ed9845fc306434792762a74c459def3_jpg.rf.540ffd87e39ce5487e67e9bef4102d52_with_roboflow_yolo.jpg
- 第 79 张图片: 4f9a4f2c47e94aca959cad738132e097_jpg.rf.89cc4fdb44636399255ee50a40b9dca8_with_roboflow_yolo.jpg
- 第 80 张图片: 5006e3fbdef349bba6e3583df9831378_jpg.rf.fc47fcd22885fd5a8bf1c0521bf2dc66_with_roboflow_yolo.jpg
- 第 81 张图片: 5034962277d443289fbb78ddd30ddf31_jpg.rf.28a24f18b45cb9ae8ab4039ad1518a9a_with_roboflow_yolo.jpg
- 第 82 张图片: 52acf189f9d14d28a3b4e092a0b66d8c_jpg.rf.42646802df9ed1087632079f639173bb_with_roboflow_yolo.jpg
- 第 83 张图片: 555a8b0f64974a27b8fc5a1c258c2fcc_jpg.rf.472378acc8e97d213401e19742219de2_with_roboflow_yolo.jpg
- 第 84 张图片: 56281af97c504fdda89fadae60627fc7_jpg.rf.f933c69f900fbaed54206bf0bd6316d2_with_roboflow_yolo.jpg
- 第 85 张图片: 56b4acc190634af38fcd7b89cb24376d_jpg.rf.1f5943a315e86a6b16408ab90d380928_with_roboflow_yolo.jpg
- 第 86 张图片: 57834bbdbccf4a9599b8e824e3284d45_jpg.rf.eae53f59d0d70ce2d8fc203c6c7ddf3a_with_roboflow_yolo.jpg
- 第 87 张图片: 57ec5f0d529c4542ac3ab8325bba9aa0_jpg.rf.26b68c41a4231bd028a7f6a401dfd7b8_with_roboflow_yolo.jpg
- 第 88 张图片: 5a1ae2e606d54046a3ce46b261d444e6_jpg.rf.0f75af15a938d4e9f4206784858e2d73_with_roboflow_yolo.jpg
- 第 89 张图片: 5aa12dd037fe44089bb5be3abec30569_jpg.rf.ff8bc6aba3b20df6b9d69c039041dc32_with_roboflow_yolo.jpg
- 第 90 张图片: 5afc1708f8ab44d9bd222c7f1ea9fe6a_jpg.rf.5f8d40e6371d27997737b40080b87507_with_roboflow_yolo.jpg
- 第 91 张图片: 5b9e8d7311e14684b3203eb7991cfbe6_jpg.rf.9ae50ea2dddeb1cc1996f05a4a6fd655_with_roboflow_yolo.jpg
- 第 92 张图片: 5bef4fc97a0a4cc2af3e67888a026b75_jpg.rf.45a202d4334388403c296cd097b3f51d_with_roboflow_yolo.jpg
- 第 94 张图片: 6166afd575264747825fd59bac26e338_jpg.rf.b859eb5dee6a983b0f4a665e81a4a111_with_roboflow_yolo.jpg
- 第 96 张图片: 61fbea55414d4052868733491c21af45_jpg.rf.43690edc1a340cbf57f97df233deb16c_with_roboflow_yolo.jpg
- 第 97 张图片: 620b499b8e3242769d766bb7f9dc38a4_jpg.rf.e613e69112e79258988a6cfcac8dce0a_with_roboflow_yolo.jpg
- 第 99 张图片: 63c0da2d288f4d6886068ad1569bde05_jpg.rf.b139257c01f72cf5203eba4736ae7236_with_roboflow_yolo.jpg
- 第 100 张图片: 64cba5d941ce4f96b6a6f97edc572008_jpg.rf.9ebf4f72bd4efbce1748bf5b45bc006f_with_roboflow_yolo.jpg
- 第 101 张图片: 64e3e495a199417e8a8e4620728db510_jpg.rf.6f344ec91a3697d4bf71374d628367fe_with_roboflow_yolo.jpg
- 第 102 张图片: 662f05762efd4e409e847909e1efe6f7_jpg.rf.6ea62d04ff61d66e4df28442ced7eeb5_with_roboflow_yolo.jpg
- 第 104 张图片: 6812bf8f1bcf431bbc412686e722f216_jpg.rf.d599437cdf68f1f14d52d583cb9a842b_with_roboflow_yolo.jpg
- 第 105 张图片: 697cc1232143483e803bbd2666fb18d6_jpg.rf.c58f0a71288325b3cd3dfbe053bce33b_with_roboflow_yolo.jpg
- 第 106 张图片: 6a2a73ca9d644b5488fb65a988544b12_jpg.rf.df8e1ff18589b370fda8940511c94258_with_roboflow_yolo.jpg
- 第 110 张图片: 6c8ce884048245cb8560f19edb3b0b35_jpg.rf.27764484c78d028e83b8bc3b08220347_with_roboflow_yolo.jpg
- 第 111 张图片: 6d22646fa64c42888c12fc3e1308a0dd_jpg.rf.6d0154d932c7d7f0a9f8502462d0dd05_with_roboflow_yolo.jpg
- 第 112 张图片: 6db37c7ed5f94d42814f79744088f691_jpg.rf.5816dbe69a74c86d7ff899b85a8ed46c_with_roboflow_yolo.jpg
- 第 113 张图片: 6eeefb48b1004340a7d3d74fd065e5e9_jpg.rf.3ded7549a7f306b1eb3765c5996edadf_with_roboflow_yolo.jpg
- 第 114 张图片: 6f9428b1466747c58cea1da07d56ba7a_jpg.rf.327c4fb2f8afaf71c68f6e9681546e04_with_roboflow_yolo.jpg
- 第 115 张图片: 710e9137fd424712b03de5d4aa1f7638_jpg.rf.2fe6523791f27b0a72c283a0d0dddfa8_with_roboflow_yolo.jpg
- 第 116 张图片: 71cf79c1bce44b748bd77890eb05e701_jpg.rf.8db1f3f435bf2fc858cf1a120e686a0b_with_roboflow_yolo.jpg
- 第 117 张图片: 723ecc34b5a1411191b752466ff27674_jpg.rf.fb73f583c0957309804bafa8712ff461_with_roboflow_yolo.jpg
- 第 118 张图片: 73505ed74af64f7e8c33078fa5dafcbb_jpg.rf.3bd405e7489d3f7b755d9d98d88ec512_with_roboflow_yolo.jpg
- 第 120 张图片: 787b173bb1c848a797e8972b1e6e21ab_jpg.rf.83f26a1f8e90af78589e7823791326f8_with_roboflow_yolo.jpg
- 第 121 张图片: 793ddd316bcf4c608576091beaec24fc_jpg.rf.5d2f02e5813d663f56f6e07c0896c02b_with_roboflow_yolo.jpg
- 第 122 张图片: 7961c834a9d54e3595d3f5409cb71809_jpg.rf.af1a2425a6796273ec516c2431dfd2e5_with_roboflow_yolo.jpg
- 第 123 张图片: 79633ecfa8a24da4809c6852f12fd0da_jpg.rf.75868653b880563cc7de1d22f014b758_with_roboflow_yolo.jpg
- 第 124 张图片: 79cff13f63ec4b558b6c09a1c75950a8_jpg.rf.4e11aa0be71cacf4314342717ec2ffe0_with_roboflow_yolo.jpg
- 第 125 张图片: 79d2ce7013d243e19197c8d48cd80a39_jpg.rf.2dfe76df5c6f3bd0e1468329287ab678_with_roboflow_yolo.jpg
- 第 126 张图片: 7a45180600544238a447105bca060273_jpg.rf.e66cc29244b1b46475eb387edad97a33_with_roboflow_yolo.jpg
- 第 127 张图片: 7b93dfa0205343fb9c5b1d323bea3c3d_jpg.rf.d9755eb95a2ae14ed7fe04003d872746_with_roboflow_yolo.jpg
- 第 128 张图片: 7f35d6129ae449ac8078d0b40b835a41_jpg.rf.91804e7ff94f8bb78bacc0f8e3fab057_with_roboflow_yolo.jpg
- 第 129 张图片: 7f41eae9d2f94fa2bad4dcf95af2529e_jpg.rf.d133c5bf1ac1e16daa74ae015157aab1_with_roboflow_yolo.jpg
- 第 130 张图片: 7f734a014cea4343bada6d73fa5008fc_jpg.rf.fb5209153b8fd8e237e4f7e2dec74d27_with_roboflow_yolo.jpg
- 第 132 张图片: 836ad0526d3c4bbaa90ae119f8375188_jpg.rf.6d2823e7b13759bba9a3572e5fc5f803_with_roboflow_yolo.jpg
- 第 133 张图片: 85119effdcb24215ae82a90692c42ed9_jpg.rf.57619e3b445255eb98f471b3b21ef146_with_roboflow_yolo.jpg
- 第 134 张图片: 88fc7a151a3e40ed89ff0f65bcc414da_jpg.rf.d4bec114913e41f88e33620f553e0e5d_with_roboflow_yolo.jpg
- 第 135 张图片: 89e3024dc8d143dfaeb0888e7a8dcbc6_jpg.rf.0efe717eca557b0851e6d5c705a978c6_with_roboflow_yolo.jpg
- 第 136 张图片: 8ad24a09126c4f5590ae13f4b1390cd0_jpg.rf.db27bd3b540ff22d651b8db0f8fa2563_with_roboflow_yolo.jpg
- 第 137 张图片: 8c175831356b441981d8a85dc1709861_jpg.rf.bc0f68c750389938df99e76ac14b5e9d_with_roboflow_yolo.jpg
- 第 138 张图片: 8d122665524045ab8d095ba8a680489c_jpg.rf.d0dba321c4fadc06021c7cec2cd97e19_with_roboflow_yolo.jpg
- 第 139 张图片: 8d1845863a5146448b7d7568ddd71582_jpg.rf.7a3fb04412f112492867cc8e0dacc413_with_roboflow_yolo.jpg
- 第 140 张图片: 8df94d5708174e278f7bc3fcbd9be1ef_jpg.rf.830064cd7b07e058758484c29fb43e1c_with_roboflow_yolo.jpg
- 第 141 张图片: 8ec00aee4b954fc9be8d3e649b691e2b_jpg.rf.4fc3c497c36b78df303d1d4ecb81c0b1_with_roboflow_yolo.jpg
- 第 142 张图片: 8eebf087dea24ad78b429dd51cb24e16_jpg.rf.e0dcce6f38536c301a128c26c251ae67_with_roboflow_yolo.jpg
- 第 143 张图片: 91d8a3ca9326441ca54cfc7d4bebfb53_jpg.rf.85c0a4c96e677f17552316e715eb42fd_with_roboflow_yolo.jpg
- 第 144 张图片: 9524ce80e52e48c4b10b50c5b28640fc_jpg.rf.6412ca3f9773923ca417722c903d12bd_with_roboflow_yolo.jpg
- 第 145 张图片: 955406664f3e49f587f83a4d12fdaa53_jpg.rf.d0fb507fc8ced6027034b3b6fecb05be_with_roboflow_yolo.jpg
- 第 146 张图片: 97540b962de444fa87d0ee5168e9fb03_jpg.rf.1daf57f6f4cc72a6d4c829a37a49938f_with_roboflow_yolo.jpg
- 第 147 张图片: 9963f1bce80c4fb09de9950967575088_jpg.rf.66c4eb8099678bd7e45bcd8835918d43_with_roboflow_yolo.jpg
- 第 148 张图片: 9be95136439b4e54978bb87b9c7530b0_jpg.rf.118e779e48e0d4c17181b0bd710ec3f3_with_roboflow_yolo.jpg
- 第 149 张图片: 9dc264a13f734fb89ea4c4151f4f2178_jpg.rf.0611f8f1ed3e63e234eca2e93f6b358b_with_roboflow_yolo.jpg
- 第 150 张图片: 9e73953f1ab64bc0899df8a8736aafad_jpg.rf.1755b50ab2da17856a6d3d8a4d8ae434_with_roboflow_yolo.jpg
- 第 152 张图片: a02482ff4496415a8d973baf0b9133bb_jpg.rf.3fafdde23a1d4aab529076deff0b4903_with_roboflow_yolo.jpg
- 第 153 张图片: a167a751a2b0452bbf33b4c988b715bb_jpg.rf.11cf2d396c8ce64f3b8eeef62ddfa101_with_roboflow_yolo.jpg
- 第 154 张图片: a1e4293aa6bc4e84a2ae887eb324f0b7_jpg.rf.d4629a0b593d606ff9bbf30c114110a0_with_roboflow_yolo.jpg
- 第 156 张图片: a3e053b9370e46b2af833b69f60c0f4c_jpg.rf.7286ae428eda173956fff4e10d9e81af_with_roboflow_yolo.jpg
- 第 157 张图片: a4d7be8eedea43a195a928875c921faf_jpg.rf.7e0dc30d484ff18db9383683753be4bc_with_roboflow_yolo.jpg
- 第 158 张图片: a4e14ecb8fd3477f8d852215f70a4710_jpg.rf.ab87a899b9ee41f5fd07f8596b9e6d14_with_roboflow_yolo.jpg
- 第 159 张图片: a5505f0a457a48d28ca03432d6f1b312_jpg.rf.35829f3f984674bff89e623d9d31feb5_with_roboflow_yolo.jpg
- 第 160 张图片: a5ad5df73ed4477a8a738ccf7b67b9a3_jpg.rf.087c4e91ec06b97ca4121631f7033f0e_with_roboflow_yolo.jpg
- 第 161 张图片: a699c99e4b664a7086c018f2b0c1eb03_jpg.rf.d8292d372b2a76a81fcb0ea5edec52bf_with_roboflow_yolo.jpg
- 第 162 张图片: a8206d7627804728a4fbdd3e979d9910_jpg.rf.76e7a56dad8668ee0ecd0ff554360de2_with_roboflow_yolo.jpg
- 第 163 张图片: a8b1c1480034464c857a0d00cd0443ad_jpg.rf.b50e701583b7efb07acff6d53acc8e5a_with_roboflow_yolo.jpg
- 第 164 张图片: aa5a2e472510417f977bc40a05bfd3ab_jpg.rf.d2d9a62ad1a564947d92010d7dab7e64_with_roboflow_yolo.jpg
- 第 165 张图片: aaa9c8a2cb6e43ef9dc2a3065b3bba50_jpg.rf.90d74383af1c59e7624ee528c889c926_with_roboflow_yolo.jpg
- 第 167 张图片: abc83c1366e34a87807e9307071b9e53_jpg.rf.f371a50ea95a6b73fe7367929b06eab0_with_roboflow_yolo.jpg
- 第 168 张图片: abd4e800014f4c3f95885aff7b7c2d26_jpg.rf.6c747a2e88ef05fdbb2e699608b0d929_with_roboflow_yolo.jpg
- 第 169 张图片: ac398a81ac4e4eb6b464eda2e7e7b9db_jpg.rf.9c431132889a5527a37bb5d82b19d3a0_with_roboflow_yolo.jpg
- 第 170 张图片: ac5eb24b0f0e4dd4bad2c05fc47bc334_jpg.rf.44b26a075fff06b7d9c5c91e148df229_with_roboflow_yolo.jpg
- 第 171 张图片: accbb2f5b4aa4dcfa659e97865c57650_jpg.rf.cb011cb04d24a45ba1a2a285e0406a79_with_roboflow_yolo.jpg
- 第 172 张图片: ad04e7f29b54400abb1a8187bfffcfef_jpg.rf.a43c67394c813dc41daadc6d18a32b6e_with_roboflow_yolo.jpg
- 第 174 张图片: adf68e3a57c54d41ad9b8f84ff32a1dc_jpg.rf.6ca618dcaccfc4e1d3475ed2f40649cd_with_roboflow_yolo.jpg
- 第 175 张图片: ae73f4cb4bbf4b4789688153af9ecc1f_jpg.rf.d8441db4e1c8d1f43b24246e47d19242_with_roboflow_yolo.jpg
- 第 176 张图片: aef00fd83be34f4f90a30df7698bfab2_jpg.rf.e7620327696df89b68aed1b391590c1d_with_roboflow_yolo.jpg
- 第 177 张图片: b0c29e41096645d9a7b15cc423fef656_jpg.rf.cb420779ba313fec68348f9c5ff8637e_with_roboflow_yolo.jpg
- 第 179 张图片: b1a42e27088f41ed93db6142c4164995_jpg.rf.5e73a1ae3e55446d75238da58810065e_with_roboflow_yolo.jpg
- 第 180 张图片: b29b0a3526e543a7bc3da3cc80f296db_jpg.rf.9262ad9336cecc049d4f1643175b1a8a_with_roboflow_yolo.jpg
- 第 181 张图片: b63cac27107f47c2b5b40bc3a9cdb05e_jpg.rf.8b57b54c2a6b2d5fbc5b074dd2d7bd36_with_roboflow_yolo.jpg
- 第 182 张图片: b67db8be9d2746349d44c650673295f2_jpg.rf.4a7b5ccfdeef2bcd0f648aaa522db3ee_with_roboflow_yolo.jpg
- 第 183 张图片: b839643fef9f432f92c1bae1ad517daf_jpg.rf.5040ad25cfa39e8936556799846267b6_with_roboflow_yolo.jpg
- 第 184 张图片: b8fca052cb9a414ea3e293e8a1e045b0_jpg.rf.3396952bf32b0443e9f1357d66f58fcc_with_roboflow_yolo.jpg
- 第 185 张图片: ba7da4d29fcb456e8ef47d245fba5212_jpg.rf.18e5213c393b19d396e892239aef6e7c_with_roboflow_yolo.jpg
- 第 186 张图片: bade1e40f9e942ec9cae0be9fd549761_jpg.rf.43392c9e872d59f6e8e583ff1d8dd0ea_with_roboflow_yolo.jpg
- 第 187 张图片: baf98393832849799c72f027febdfc97_jpg.rf.2235fa0c664aac9d39b39955f48709dc_with_roboflow_yolo.jpg
- 第 188 张图片: bb1499178fb946a98b42df15d9968e90_jpg.rf.4097297448dafd2917f22698d7bd188c_with_roboflow_yolo.jpg
- 第 190 张图片: bfaa7468985843948674d79fd49ddd21_jpg.rf.d5bacd5faaec55c746f778103404e464_with_roboflow_yolo.jpg
- 第 191 张图片: c192e57c3d8145bab01ef584adec6d4d_jpg.rf.88fbda76d2ce1de5d175665a6e1f5596_with_roboflow_yolo.jpg
- 第 192 张图片: c26b4c0c14ff412193da720ed99dad55_jpg.rf.c4685490c7b1a8ee6f2755a1b79c176a_with_roboflow_yolo.jpg
- 第 193 张图片: c38c3229eb5d4694a7e981f0dcb4e68d_jpg.rf.7f0a58763ed827615c04e130871b46c6_with_roboflow_yolo.jpg
- 第 195 张图片: c87f0552f77c4ec782f08a0a2159f198_jpg.rf.5ad072a5cdaf3944cc7849e75170b5ad_with_roboflow_yolo.jpg
- 第 196 张图片: c9a3d1414682402ba2c5b354c37bfc0a_jpg.rf.dc0ed7e996ae6fbd27bbb35f78086605_with_roboflow_yolo.jpg
- 第 197 张图片: ccd12c0cfac24c6d9a99e14c380b7ee8_jpg.rf.cfac3219550f3be39bb15c4fa2377f81_with_roboflow_yolo.jpg
- 第 198 张图片: cde02c7f38914237a9ad1e38f9304c24_jpg.rf.14160bf4a69477d8ca192dcf46cd7102_with_roboflow_yolo.jpg
- 第 199 张图片: cf3db06ab57e4d75acbb2d44116c9190_jpg.rf.62fadd3768973410214838d3176021e6_with_roboflow_yolo.jpg
- 第 200 张图片: cf82a17c00e347d5807d2ee1cad57f92_jpg.rf.f4336ecd2bbe4ab5f24f6b37a407d669_with_roboflow_yolo.jpg
- 第 201 张图片: d10de923f1a24802ae094d517e438031_jpg.rf.f87c5b6665b413593d51be1606bb6750_with_roboflow_yolo.jpg
- 第 202 张图片: d14c4dbfb5bc40629168fcc5a09cd436_jpg.rf.0b00697f3ce5f9a927fa3c3d2dbcc572_with_roboflow_yolo.jpg
- 第 203 张图片: d418b1d2bd0446e4bc9112449e7664ab_jpg.rf.533dbd7e5cc190a57b198c09c4e0153b_with_roboflow_yolo.jpg
- 第 204 张图片: d4544e69005e4238bf84931cb24d86b9_jpg.rf.bb8ebe1e19ccb17a2e61c71268ec59e2_with_roboflow_yolo.jpg
- 第 205 张图片: d6c39fd02c1f43b5a599dde38f1c0d89_jpg.rf.58a9b351674b08449b7f9d3d2652572b_with_roboflow_yolo.jpg
- 第 206 张图片: d90c9181b8f34f1d870dfc63d7f1f02f_jpg.rf.a0e4db268f7417d8ef7e888dc5ad1862_with_roboflow_yolo.jpg
- 第 207 张图片: dd490a80cae6434dba22cc3ac425d02b_jpg.rf.2f18775da71d20b1d11c6eb9fc6690df_with_roboflow_yolo.jpg
- 第 208 张图片: ddefcd1b40cb4593a52d4bea8781dff2_jpg.rf.3bac30c7f91d076ead698a656b72aef3_with_roboflow_yolo.jpg
- 第 209 张图片: e00670ac00b4430abc2bd3d7a6e5fc85_jpg.rf.f3f60a9b747ba267f18822e20776a3e1_with_roboflow_yolo.jpg
- 第 212 张图片: e2f1e7ae919b42c5ad2b232fd40759ca_jpg.rf.fe005e84cd3747125181655f9b902e0e_with_roboflow_yolo.jpg
- 第 213 张图片: e2f6f3922d734fdfab4c614243ff4871_jpg.rf.565e4d11b11465376e2dddac1e5ceaab_with_roboflow_yolo.jpg
- 第 214 张图片: e6761829d30e4f328f4a2a2733f86613_jpg.rf.91b97d45bb4fb0ff69952e743182a2d0_with_roboflow_yolo.jpg
- 第 215 张图片: e8eda7de49864852908e47463a1d27af_jpg.rf.ec45a22bacc0741b67bbe69ea71c1bda_with_roboflow_yolo.jpg
- 第 216 张图片: e935475e409a4511b683e8a02fa9798a_jpg.rf.b62c5fdb5907ffc98179a344ee377cea_with_roboflow_yolo.jpg
- 第 218 张图片: e9d3197bb9cc4b208783b8d160b2fe45_jpg.rf.575040173f5b3ec68c6cf2a9f2beda41_with_roboflow_yolo.jpg
- 第 219 张图片: e9e445305d5d4e20b811e91a1f9c4519_jpg.rf.9febdf7c69d6d629b77ff917924362b4_with_roboflow_yolo.jpg
- 第 220 张图片: e9feb8c0d62f44d3b8b3da84d13e9206_jpg.rf.cd486891ae01d5366f26ca3db95424b8_with_roboflow_yolo.jpg
- 第 221 张图片: ebc687553ab84bae89fc58e6e1bbf0fb_jpg.rf.b2d2815fc1c3decf43ff1b74d0cc5cd0_with_roboflow_yolo.jpg
- 第 222 张图片: ee21276da8b6457897865974d8613a92_jpg.rf.387192ad66c8497e091cd474a3e630b0_with_roboflow_yolo.jpg
- 第 223 张图片: eef978599ed24584a3617e2ad7524664_jpg.rf.b6f4fe90940232160458e01a6e14cad5_with_roboflow_yolo.jpg
- 第 224 张图片: ef9d2d23349c4856bbede25d99a5ee8a_jpg.rf.9abe6c5a0f497d77c289566c9fe11f76_with_roboflow_yolo.jpg
- 第 225 张图片: efa31758a21e4c0587d13ff854e75107_jpg.rf.70699457aabc62afee68d18fe2a57bb2_with_roboflow_yolo.jpg
- 第 226 张图片: f31c24530b61441faf634675ef9eaa32_jpg.rf.40bc5fcda02ea0735310ae2b9353a7d4_with_roboflow_yolo.jpg
- 第 227 张图片: f53b65a196c94f96ac99952e3c536554_jpg.rf.d15e84378699acc197fe03a45531a110_with_roboflow_yolo.jpg
- 第 228 张图片: f56984f2b57143748bf8615e1fe5dbd2_jpg.rf.6826f3d4ae0489ac6303f34d84d813e8_with_roboflow_yolo.jpg
- 第 229 张图片: f5bc9504654f43b88672c9a4b6a23f2e_jpg.rf.342b47666d01807493f68177c60507ca_with_roboflow_yolo.jpg
- 第 230 张图片: f862d077cfe247fcb7b27394ab6a33e5_jpg.rf.b3cf11987c5df8b0e5218655885b328b_with_roboflow_yolo.jpg
- 第 231 张图片: fae27a27abf0456295d3a165486db741_jpg.rf.5429f63997a2bad299041eb97696822f_with_roboflow_yolo.jpg
- 第 232 张图片: fbb49a62f2f9428793cef82ef406e9c2_jpg.rf.4a0a9f877ee60e3cbfa55ab4d96417bb_with_roboflow_yolo.jpg
- 第 233 张图片: fcbf00df24934943b0420f52e320bf30_jpg.rf.838fbca17aff692f5f55f980084ca1b7_with_roboflow_yolo.jpg
- 第 234 张图片: fcd2f8efc9f440d9a1d5d5c4b6a9357a_jpg.rf.539d6903bb79a4f7d610bb70d7b57da5_with_roboflow_yolo.jpg
- 第 235 张图片: fd680c54b1d9495388b2fe87f25ec3cb_jpg.rf.0b5683a6184908ca0c91c06b72f79eaa_with_roboflow_yolo.jpg
- 第 236 张图片: fe614c76d0634edaa536e57274d58617_jpg.rf.8ddf3d844b2622ddce28934386d31592_with_roboflow_yolo.jpg

## 准确率：14.83%  （(236 - 201) / 236）

# 运行时间: 2025-07-30_14-12-38


==================================================
处理第 1 张图片: 0225922a75514853a54396f709099952_jpg.rf.75679ec2e503aa1662ba2b52922794f5_with_roboflow_yolo.jpg

==================================================
![0225922a75514853a54396f709099952_jpg.rf.75679ec2e503aa1662ba2b52922794f5_with_roboflow_yolo.jpg](0225922a75514853a54396f709099952_jpg.rf.75679ec2e503aa1662ba2b52922794f5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：3.13秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 2 张图片: 03209e96deb948498e1a241c7ef4e28e_jpg.rf.8459922aa657f0432425d157e3f63dcc_with_roboflow_yolo.jpg

==================================================
![03209e96deb948498e1a241c7ef4e28e_jpg.rf.8459922aa657f0432425d157e3f63dcc_with_roboflow_yolo.jpg](03209e96deb948498e1a241c7ef4e28e_jpg.rf.8459922aa657f0432425d157e3f63dcc_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "1", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：1.90秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 3 张图片: 037a8d2a77664b8caf6c032e1677e096_jpg.rf.a073630cde7938ba84e9aa018458da7b_with_roboflow_yolo.jpg

==================================================
![037a8d2a77664b8caf6c032e1677e096_jpg.rf.a073630cde7938ba84e9aa018458da7b_with_roboflow_yolo.jpg](037a8d2a77664b8caf6c032e1677e096_jpg.rf.a073630cde7938ba84e9aa018458da7b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"20/28","题目 3":"1/2","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"1","题目 8":"1/3"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "1", "题目4": "1.75", "题目5": "0.552", "题目6": "1/4", "题目7": "1/2", "题目8": "6/25"}
```
### 响应时间：2.25秒
### token用量
- total_tokens: 781
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 4 张图片: 03a6a879aaa74c23b04fc37b6cf2b7b5_jpg.rf.041efa982eaeca3ebf83e4f41ebe588e_with_roboflow_yolo.jpg

==================================================
![03a6a879aaa74c23b04fc37b6cf2b7b5_jpg.rf.041efa982eaeca3ebf83e4f41ebe588e_with_roboflow_yolo.jpg](03a6a879aaa74c23b04fc37b6cf2b7b5_jpg.rf.041efa982eaeca3ebf83e4f41ebe588e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"2/35","题目 3":"NAN","题目 4":"1 1/9","题目 5":"NAN","题目 6":"NAN","题目 7":"NAN","题目 8":"NAN","题目 9":"7/11","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "NAN", "题目 7": "NAN", "题目 8": "NAN", "题目 9": "NAN", "题目 10": "NAN"}
```
### 响应时间：3.32秒
### token用量
- total_tokens: 797
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 5 张图片: 049a6e14e83a4c3492dae37fba50459b_jpg.rf.2bf6bf09e2e9e7fcf63d4617eb463871_with_roboflow_yolo.jpg

==================================================
![049a6e14e83a4c3492dae37fba50459b_jpg.rf.2bf6bf09e2e9e7fcf63d4617eb463871_with_roboflow_yolo.jpg](049a6e14e83a4c3492dae37fba50459b_jpg.rf.2bf6bf09e2e9e7fcf63d4617eb463871_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"23/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目1": "13/13", "题目2": "24/35", "题目3": "17/35", "题目4": "6/7", "题目5": "8/42", "题目6": "1/9", "题目7": "23/18", "题目8": "6/8", "题目9": "0.02", "题目10": "1/15", "题目11": "8/11", "题目12": "3/5"}
```
### 响应时间：3.14秒
### token用量
- total_tokens: 829
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 7 张图片: 066e3de949c04685ae8b9bef93f1a52f_jpg.rf.b920bfb8668e81ebe2ea102893ced2e1_with_roboflow_yolo.jpg

==================================================
![066e3de949c04685ae8b9bef93f1a52f_jpg.rf.b920bfb8668e81ebe2ea102893ced2e1_with_roboflow_yolo.jpg](066e3de949c04685ae8b9bef93f1a52f_jpg.rf.b920bfb8668e81ebe2ea102893ced2e1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"1.5374"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.16", "题目5": "1.5314"}
```
### 响应时间：3.16秒
### token用量
- total_tokens: 755
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 8 张图片: 08abab793d18480fb5f5b71036c5ac76_jpg.rf.8b2a91340bc132d44dfc65ddbbc48676_with_roboflow_yolo.jpg

==================================================
![08abab793d18480fb5f5b71036c5ac76_jpg.rf.8b2a91340bc132d44dfc65ddbbc48676_with_roboflow_yolo.jpg](08abab793d18480fb5f5b71036c5ac76_jpg.rf.8b2a91340bc132d44dfc65ddbbc48676_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"0.875","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4", "题目7": "9/40", "题目8": "3/10"}
```
### 响应时间：2.70秒
### token用量
- total_tokens: 782
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 10 张图片: 0dc58b3ba20e40bf85484b710ebcc76f_jpg.rf.098efe4b62fc01e94d9ad5c1f3107d4d_with_roboflow_yolo.jpg

==================================================
![0dc58b3ba20e40bf85484b710ebcc76f_jpg.rf.098efe4b62fc01e94d9ad5c1f3107d4d_with_roboflow_yolo.jpg](0dc58b3ba20e40bf85484b710ebcc76f_jpg.rf.098efe4b62fc01e94d9ad5c1f3107d4d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"60","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 749
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 11 张图片: 0fd0b3e3b1a145cc9840505ca7adfb8b_jpg.rf.e5fd1083de0879ebec9fb2b94b89faf5_with_roboflow_yolo.jpg

==================================================
![0fd0b3e3b1a145cc9840505ca7adfb8b_jpg.rf.e5fd1083de0879ebec9fb2b94b89faf5_with_roboflow_yolo.jpg](0fd0b3e3b1a145cc9840505ca7adfb8b_jpg.rf.e5fd1083de0879ebec9fb2b94b89faf5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "1", "题目4": "1.8", "题目5": "0.552", "题目6": "4", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.50秒
### token用量
- total_tokens: 780
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 12 张图片: 0fe3646519c5442d98c0c03a60e3ab69_jpg.rf.f0989b013ffb007ebee70f8e6e8ae5ba_with_roboflow_yolo.jpg

==================================================
![0fe3646519c5442d98c0c03a60e3ab69_jpg.rf.f0989b013ffb007ebee70f8e6e8ae5ba_with_roboflow_yolo.jpg](0fe3646519c5442d98c0c03a60e3ab69_jpg.rf.f0989b013ffb007ebee70f8e6e8ae5ba_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "2/8", "题目4": "1.75", "题目5": "0.138", "题目6": "4/25", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.67秒
### token用量
- total_tokens: 786
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 13 张图片: 107606add9bb47dc8b2852be9b25d10b_jpg.rf.db1d3a5ff61e883f9fef9947dd21dfa3_with_roboflow_yolo.jpg

==================================================
![107606add9bb47dc8b2852be9b25d10b_jpg.rf.db1d3a5ff61e883f9fef9947dd21dfa3_with_roboflow_yolo.jpg](107606add9bb47dc8b2852be9b25d10b_jpg.rf.db1d3a5ff61e883f9fef9947dd21dfa3_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/12","题目 3":"0.875","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"18/75"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "0.875", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 响应时间：4.14秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 14 张图片: 133d6135af184226b017cc1ba80afeb2_jpg.rf.bf522076bcb2c68d31849c2f66894deb_with_roboflow_yolo.jpg

==================================================
![133d6135af184226b017cc1ba80afeb2_jpg.rf.bf522076bcb2c68d31849c2f66894deb_with_roboflow_yolo.jpg](133d6135af184226b017cc1ba80afeb2_jpg.rf.bf522076bcb2c68d31849c2f66894deb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.1425"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.1875"}
```
### 响应时间：2.43秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 15 张图片: 13a990bbdefd47ac9ebb9dc7169ee095_jpg.rf.3962931e0316ce3c679673beaf67d0c2_with_roboflow_yolo.jpg

==================================================
![13a990bbdefd47ac9ebb9dc7169ee095_jpg.rf.3962931e0316ce3c679673beaf67d0c2_with_roboflow_yolo.jpg](13a990bbdefd47ac9ebb9dc7169ee095_jpg.rf.3962931e0316ce3c679673beaf67d0c2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3","题目 9":"3000","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "1"}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 16 张图片: 14620e1a4abc4c4a939f77d2ff688eb7_jpg.rf.5a9c6ba96c1610f5ac714415619465ef_with_roboflow_yolo.jpg

==================================================
![14620e1a4abc4c4a939f77d2ff688eb7_jpg.rf.5a9c6ba96c1610f5ac714415619465ef_with_roboflow_yolo.jpg](14620e1a4abc4c4a939f77d2ff688eb7_jpg.rf.5a9c6ba96c1610f5ac714415619465ef_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "14", "题目 3": "0.5", "题目 4": "17/35", "题目 5": "29/42", "题目 6": "11/18", "题目 7": "2/9", "题目 8": "5/18", "题目 9": "0.775", "题目 10": "3/4", "题目 11": "0.07", "题目 12": "8.5", "题目 13": "8/11", "题目 14": "3/5", "题目 15": "1/5"}
```
### 响应时间：4.79秒
### token用量
- total_tokens: 870
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 17 张图片: 15984089651d47d0b3afec82fca85195_jpg.rf.2026faac6290ee5e630277b6bcedc2ae_with_roboflow_yolo.jpg

==================================================
![15984089651d47d0b3afec82fca85195_jpg.rf.2026faac6290ee5e630277b6bcedc2ae_with_roboflow_yolo.jpg](15984089651d47d0b3afec82fca85195_jpg.rf.2026faac6290ee5e630277b6bcedc2ae_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3/7", "题目9": "3000", "题目10": "1/3"}
```
### 响应时间：2.77秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 18 张图片: 16e208765c6f46d0bc8d80f6ac01a6c2_jpg.rf.ee6ad5ce50c99c4e97ea4b8415b5f812_with_roboflow_yolo.jpg

==================================================
![16e208765c6f46d0bc8d80f6ac01a6c2_jpg.rf.ee6ad5ce50c99c4e97ea4b8415b5f812_with_roboflow_yolo.jpg](16e208765c6f46d0bc8d80f6ac01a6c2_jpg.rf.ee6ad5ce50c99c4e97ea4b8415b5f812_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3","题目 9":"300","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "NAN", "题目9": "3000", "题目10": "7"}
```
### 响应时间：2.59秒
### token用量
- total_tokens: 791
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 20 张图片: 1981cec7d2c94b068d7ba6e6e987c39b_jpg.rf.7c4f5203e51dd474d9de30f3519a07c9_with_roboflow_yolo.jpg

==================================================
![1981cec7d2c94b068d7ba6e6e987c39b_jpg.rf.7c4f5203e51dd474d9de30f3519a07c9_with_roboflow_yolo.jpg](1981cec7d2c94b068d7ba6e6e987c39b_jpg.rf.7c4f5203e51dd474d9de30f3519a07c9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4.8","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4.8", "题目7": "25/34", "题目8": "6/25"}
```
### 响应时间：2.45秒
### token用量
- total_tokens: 785
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 21 张图片: 1a746dc5ea8a490ea7a933e55c85939d_jpg.rf.e58039a484d8268eb9a96849e4e99e24_with_roboflow_yolo.jpg

==================================================
![1a746dc5ea8a490ea7a933e55c85939d_jpg.rf.e58039a484d8268eb9a96849e4e99e24_with_roboflow_yolo.jpg](1a746dc5ea8a490ea7a933e55c85939d_jpg.rf.e58039a484d8268eb9a96849e4e99e24_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "7"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 22 张图片: 1a8a17e034624b3ea8c10446eb11f25c_jpg.rf.7509fb6f7b3925b8f164aa14b848513f_with_roboflow_yolo.jpg

==================================================
![1a8a17e034624b3ea8c10446eb11f25c_jpg.rf.7509fb6f7b3925b8f164aa14b848513f_with_roboflow_yolo.jpg](1a8a17e034624b3ea8c10446eb11f25c_jpg.rf.7509fb6f7b3925b8f164aa14b848513f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "1/6", "题目2": "980", "题目3": "16", "题目4": "45"}
```
### 响应时间：3.18秒
### token用量
- total_tokens: 740
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 23 张图片: 1ba92a72197844e4ab2dc1def43a7087_jpg.rf.1f9dd20833b037f58617a4b92e2c1f8f_with_roboflow_yolo.jpg

==================================================
![1ba92a72197844e4ab2dc1def43a7087_jpg.rf.1f9dd20833b037f58617a4b92e2c1f8f_with_roboflow_yolo.jpg](1ba92a72197844e4ab2dc1def43a7087_jpg.rf.1f9dd20833b037f58617a4b92e2c1f8f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 25 张图片: 1d321a3fcce14d0bbdc4b9ed33cc32c6_jpg.rf.cae5a82a439a97a56532984d657f6dae_with_roboflow_yolo.jpg

==================================================
![1d321a3fcce14d0bbdc4b9ed33cc32c6_jpg.rf.cae5a82a439a97a56532984d657f6dae_with_roboflow_yolo.jpg](1d321a3fcce14d0bbdc4b9ed33cc32c6_jpg.rf.cae5a82a439a97a56532984d657f6dae_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "7"}
```
### 响应时间：3.36秒
### token用量
- total_tokens: 790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 28 张图片: 1e9e2c1721e342d1b60cf09eb00b40f1_jpg.rf.fc33b7881be9ee39f96b15a97e862c20_with_roboflow_yolo.jpg

==================================================
![1e9e2c1721e342d1b60cf09eb00b40f1_jpg.rf.fc33b7881be9ee39f96b15a97e862c20_with_roboflow_yolo.jpg](1e9e2c1721e342d1b60cf09eb00b40f1_jpg.rf.fc33b7881be9ee39f96b15a97e862c20_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"22/35","题目 3":"29/42","题目 4":"23/9","题目 5":"23/9","题目 6":"3/4","题目 7":"0.9","题目 8":"8/15","题目 9":"0","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "24/35", "题目3": "29/42", "题目4": "2/3", "题目5": "13/18", "题目6": "1/4", "题目7": "0.09", "题目8": "8/15", "题目9": "1", "题目10": "0.2"}
```
### 响应时间：4.10秒
### token用量
- total_tokens: 801
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 29 张图片: 1eb33d5f0bea4440a80f85978330c642_jpg.rf.10007b9f4d17dda5b9bbbabfe6339ee0_with_roboflow_yolo.jpg

==================================================
![1eb33d5f0bea4440a80f85978330c642_jpg.rf.10007b9f4d17dda5b9bbbabfe6339ee0_with_roboflow_yolo.jpg](1eb33d5f0bea4440a80f85978330c642_jpg.rf.10007b9f4d17dda5b9bbbabfe6339ee0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3","题目 9":"3000","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "50", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "3"}
```
### 响应时间：3.04秒
### token用量
- total_tokens: 790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 30 张图片: 1fb58c9c8aa3437fba540ac43ea191c4_jpg.rf.5de8b2532871b30687efa63ba04b1080_with_roboflow_yolo.jpg

==================================================
![1fb58c9c8aa3437fba540ac43ea191c4_jpg.rf.5de8b2532871b30687efa63ba04b1080_with_roboflow_yolo.jpg](1fb58c9c8aa3437fba540ac43ea191c4_jpg.rf.5de8b2532871b30687efa63ba04b1080_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"181.1","题目 2":"10","题目 3":"12","题目 4":"0.1075"}
```
### 响应内容：
```json
{"题目 1": "181.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0195"}
```
### 响应时间：2.59秒
### token用量
- total_tokens: 749
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 31 张图片: 214ffd89f6834e578d939a118a7c1982_jpg.rf.25fab8a217608675f3cbf132c13c1f20_with_roboflow_yolo.jpg

==================================================
![214ffd89f6834e578d939a118a7c1982_jpg.rf.25fab8a217608675f3cbf132c13c1f20_with_roboflow_yolo.jpg](214ffd89f6834e578d939a118a7c1982_jpg.rf.25fab8a217608675f3cbf132c13c1f20_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.09715"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：2.92秒
### token用量
- total_tokens: 745
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 32 张图片: 21d82e1bc0164fa493194861208c4f52_jpg.rf.f3a128623bf92f9c53ecb86b86e408b8_with_roboflow_yolo.jpg

==================================================
![21d82e1bc0164fa493194861208c4f52_jpg.rf.f3a128623bf92f9c53ecb86b86e408b8_with_roboflow_yolo.jpg](21d82e1bc0164fa493194861208c4f52_jpg.rf.f3a128623bf92f9c53ecb86b86e408b8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "NAN", "题目9": "3000", "题目10": "NAN"}
```
### 响应时间：3.16秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 33 张图片: 240fd91397bb4b838450e32f588acac5_jpg.rf.ddb2b7d0cd0a304ff758b7fea610fd45_with_roboflow_yolo.jpg

==================================================
![240fd91397bb4b838450e32f588acac5_jpg.rf.ddb2b7d0cd0a304ff758b7fea610fd45_with_roboflow_yolo.jpg](240fd91397bb4b838450e32f588acac5_jpg.rf.ddb2b7d0cd0a304ff758b7fea610fd45_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"98","题目 3":"40本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "1/6", "题目2": "980", "题目3": "16", "题目4": "45"}
```
### 响应时间：1.46秒
### token用量
- total_tokens: 740
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 34 张图片: 253d6553790a471a888a2f4aa4f4e59c_jpg.rf.3a5bfcbdf7b151568b332fe9976159d2_with_roboflow_yolo.jpg

==================================================
![253d6553790a471a888a2f4aa4f4e59c_jpg.rf.3a5bfcbdf7b151568b332fe9976159d2_with_roboflow_yolo.jpg](253d6553790a471a888a2f4aa4f4e59c_jpg.rf.3a5bfcbdf7b151568b332fe9976159d2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16(本)","题目 4":"45(公顷)"}
```
### 响应内容：
```json
{"题目1": "1-\\frac{1}{4}-\\frac{7}{12}", "题目2": "420\\div\\frac{3}{7}=980(kg)", "题目3": "24\\times\\frac{2}{3}=16(本)", "题目4": "75\\times\\frac{3}{5}=45(公顷)"}
```
### 响应时间：3.59秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 36 张图片: 28275a7ccfe84555b36ec1854bb3a14b_jpg.rf.6e95e8b7abe28bb543d6a4855a8b5624_with_roboflow_yolo.jpg

==================================================
![28275a7ccfe84555b36ec1854bb3a14b_jpg.rf.6e95e8b7abe28bb543d6a4855a8b5624_with_roboflow_yolo.jpg](28275a7ccfe84555b36ec1854bb3a14b_jpg.rf.6e95e8b7abe28bb543d6a4855a8b5624_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"0.594","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 37 张图片: 2857fddd1ff74c9ba827bcc627230b23_jpg.rf.87f51fe23ea3e0a6c83b3e0639bac2b6_with_roboflow_yolo.jpg

==================================================
![2857fddd1ff74c9ba827bcc627230b23_jpg.rf.87f51fe23ea3e0a6c83b3e0639bac2b6_with_roboflow_yolo.jpg](2857fddd1ff74c9ba827bcc627230b23_jpg.rf.87f51fe23ea3e0a6c83b3e0639bac2b6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "181.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.075"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 38 张图片: 29a0f9cc1cfd4d908c027c0b484cf0af_jpg.rf.d9deff2ef998567966342abbc91bcc7d_with_roboflow_yolo.jpg

==================================================
![29a0f9cc1cfd4d908c027c0b484cf0af_jpg.rf.d9deff2ef998567966342abbc91bcc7d_with_roboflow_yolo.jpg](29a0f9cc1cfd4d908c027c0b484cf0af_jpg.rf.d9deff2ef998567966342abbc91bcc7d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.15²"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.15"}
```
### 响应时间：2.49秒
### token用量
- total_tokens: 743
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 39 张图片: 2a5fa54d82284affb690558eaa49ecbf_jpg.rf.ad23c44cb22964591995f9a34b12112c_with_roboflow_yolo.jpg

==================================================
![2a5fa54d82284affb690558eaa49ecbf_jpg.rf.ad23c44cb22964591995f9a34b12112c_with_roboflow_yolo.jpg](2a5fa54d82284affb690558eaa49ecbf_jpg.rf.ad23c44cb22964591995f9a34b12112c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12.6","题目 4":"0.9775"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.11"}
```
### 响应时间：2.21秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 40 张图片: 2bcd9d8c4ede49efa21a0ebd69c7766f_jpg.rf.8dbe7966b3406e5f00d3777da2bd6495_with_roboflow_yolo.jpg

==================================================
![2bcd9d8c4ede49efa21a0ebd69c7766f_jpg.rf.8dbe7966b3406e5f00d3777da2bd6495_with_roboflow_yolo.jpg](2bcd9d8c4ede49efa21a0ebd69c7766f_jpg.rf.8dbe7966b3406e5f00d3777da2bd6495_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"0.5","题目 7":"0.027","题目 8":"8 4/15","题目 9":"8/11","题目 10":"1 6/10"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "0.5", "题目3": "2/5", "题目4": "0.027", "题目5": "2/21", "题目6": "8/15", "题目7": "2/3", "题目8": "8/11", "题目9": "5/18", "题目10": "3/10"}
```
### 响应时间：3.59秒
### token用量
- total_tokens: 802
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 41 张图片: 2bd364c0afea48d38a1be02e309bed16_jpg.rf.27bf7c10ad3a333e77a3661918f92cd2_with_roboflow_yolo.jpg

==================================================
![2bd364c0afea48d38a1be02e309bed16_jpg.rf.27bf7c10ad3a333e77a3661918f92cd2_with_roboflow_yolo.jpg](2bd364c0afea48d38a1be02e309bed16_jpg.rf.27bf7c10ad3a333e77a3661918f92cd2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3/7", "题目9": "3000", "题目10": "7/10"}
```
### 响应时间：3.06秒
### token用量
- total_tokens: 797
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 42 张图片: 2d4285da05ee44fa97ee92e19178c89c_jpg.rf.934cdbaa09306261cd3396ca4941ab8e_with_roboflow_yolo.jpg

==================================================
![2d4285da05ee44fa97ee92e19178c89c_jpg.rf.934cdbaa09306261cd3396ca4941ab8e_with_roboflow_yolo.jpg](2d4285da05ee44fa97ee92e19178c89c_jpg.rf.934cdbaa09306261cd3396ca4941ab8e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"23/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"8/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "1", "题目3": "1/42", "题目4": "1/6", "题目5": "2/3", "题目6": "3/8", "题目7": "0.01", "题目8": "8/15", "题目9": "2/11", "题目10": "1.2"}
```
### 响应时间：3.40秒
### token用量
- total_tokens: 797
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 44 张图片: 2e1161cea94c4bcea26587ade6d58dfd_jpg.rf.00e99ea06022b3103674f0eb965dd068_with_roboflow_yolo.jpg

==================================================
![2e1161cea94c4bcea26587ade6d58dfd_jpg.rf.00e99ea06022b3103674f0eb965dd068_with_roboflow_yolo.jpg](2e1161cea94c4bcea26587ade6d58dfd_jpg.rf.00e99ea06022b3103674f0eb965dd068_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 响应时间：3.30秒
### token用量
- total_tokens: 793
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 45 张图片: 2f26a976da5c43df92987953cfb26e2c_jpg.rf.923dc510263b22000dbab813e1322821_with_roboflow_yolo.jpg

==================================================
![2f26a976da5c43df92987953cfb26e2c_jpg.rf.923dc510263b22000dbab813e1322821_with_roboflow_yolo.jpg](2f26a976da5c43df92987953cfb26e2c_jpg.rf.923dc510263b22000dbab813e1322821_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980(千克)","题目 3":"16(本)","题目 4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "980(kg)", "题目 3": "16(本)", "题目 4": "45(公顷)"}
```
### 响应时间：2.19秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 46 张图片: 2f61a4d5c19e432e9585ecb0559c200e_jpg.rf.01bcfcc0091e0f611444e53c6730aaf4_with_roboflow_yolo.jpg

==================================================
![2f61a4d5c19e432e9585ecb0559c200e_jpg.rf.01bcfcc0091e0f611444e53c6730aaf4_with_roboflow_yolo.jpg](2f61a4d5c19e432e9585ecb0559c200e_jpg.rf.01bcfcc0091e0f611444e53c6730aaf4_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980","题目 3":"16本","题目 4":"51公顷"}
```
### 响应内容：
```json
{"题目 1": "1-\\frac{1}{4}-\\frac{7}{12}=\\frac{1}{6}", "题目 2": "420÷3×7 = 980", "题目 3": "24÷3×2 = 16", "题目 4": "75÷5×3 = 45"}
```
### 响应时间：4.08秒
### token用量
- total_tokens: 789
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 48 张图片: 305978696b244bc1aa74b8ca4ccd8c3b_jpg.rf.eaa7cd247b87fdb29cf6ace4f37da555_with_roboflow_yolo.jpg

==================================================
![305978696b244bc1aa74b8ca4ccd8c3b_jpg.rf.eaa7cd247b87fdb29cf6ace4f37da555_with_roboflow_yolo.jpg](305978696b244bc1aa74b8ca4ccd8c3b_jpg.rf.eaa7cd247b87fdb29cf6ace4f37da555_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.1275"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.1275"}
```
### 响应时间：2.26秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 49 张图片: 3352be9115304b67aa815f956eaf6c43_jpg.rf.d5a52530faae3e838263caf12c284b6e_with_roboflow_yolo.jpg

==================================================
![3352be9115304b67aa815f956eaf6c43_jpg.rf.d5a52530faae3e838263caf12c284b6e_with_roboflow_yolo.jpg](3352be9115304b67aa815f956eaf6c43_jpg.rf.d5a52530faae3e838263caf12c284b6e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "17/35", "题目3": "35/42", "题目4": "5/18", "题目5": "3/4", "题目6": "0.2", "题目7": "14/15", "题目8": "8/11", "题目9": "1.2"}
```
### 响应时间：4.23秒
### token用量
- total_tokens: 793
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 50 张图片: 3508bd0ecd9e4b71a292c14d19096720_jpg.rf.e1d04b3c4c4b1f53a95151619b2d29ec_with_roboflow_yolo.jpg

==================================================
![3508bd0ecd9e4b71a292c14d19096720_jpg.rf.e1d04b3c4c4b1f53a95151619b2d29ec_with_roboflow_yolo.jpg](3508bd0ecd9e4b71a292c14d19096720_jpg.rf.e1d04b3c4c4b1f53a95151619b2d29ec_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"980","题目 2":"1","题目 3":"12","题目 4":"0.975"}
```
### 响应内容：
```json
{"题目 1": "980", "题目 2": "0.1", "题目 3": "12", "题目 4": "0.4"}
```
### 响应时间：2.19秒
### token用量
- total_tokens: 745
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 51 张图片: 359829674c30477eaa60d68d622a369a_jpg.rf.02103b83f1dcc0b8a957e660a69e8337_with_roboflow_yolo.jpg

==================================================
![359829674c30477eaa60d68d622a369a_jpg.rf.02103b83f1dcc0b8a957e660a69e8337_with_roboflow_yolo.jpg](359829674c30477eaa60d68d622a369a_jpg.rf.02103b83f1dcc0b8a957e660a69e8337_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "17/25", "题目3": "29/42", "题目4": "22/9", "题目5": "5/18", "题目6": "3/4", "题目7": "0.09", "题目8": "8.9", "题目9": "8/11", "题目10": "1.2"}
```
### 响应时间：2.92秒
### token用量
- total_tokens: 803
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 52 张图片: 378ec761313c4f499110292958c04b3c_jpg.rf.5a14bfac9cbe0f12fcebde7ee6b5314b_with_roboflow_yolo.jpg

==================================================
![378ec761313c4f499110292958c04b3c_jpg.rf.5a14bfac9cbe0f12fcebde7ee6b5314b_with_roboflow_yolo.jpg](378ec761313c4f499110292958c04b3c_jpg.rf.5a14bfac9cbe0f12fcebde7ee6b5314b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"0.1","题目 3":"12","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "1", "题目 3": "12.0"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 734
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 53 张图片: 37b78e00e1f84752a72f759e40489274_jpg.rf.ada45104979b9c4c23607414e9552c19_with_roboflow_yolo.jpg

==================================================
![37b78e00e1f84752a72f759e40489274_jpg.rf.ada45104979b9c4c23607414e9552c19_with_roboflow_yolo.jpg](37b78e00e1f84752a72f759e40489274_jpg.rf.ada45104979b9c4c23607414e9552c19_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "7"}
```
### 响应时间：2.52秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 54 张图片: 3be9649d312b46c0a9087839a2796555_jpg.rf.d0fc4d91ff53fb6928bcc5c1606e61cd_with_roboflow_yolo.jpg

==================================================
![3be9649d312b46c0a9087839a2796555_jpg.rf.d0fc4d91ff53fb6928bcc5c1606e61cd_with_roboflow_yolo.jpg](3be9649d312b46c0a9087839a2796555_jpg.rf.d0fc4d91ff53fb6928bcc5c1606e61cd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0075"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.1", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：1.67秒
### token用量
- total_tokens: 746
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 55 张图片: 3d9ed6db90434a2f8b57c1422d910140_jpg.rf.1867ed91ee209f77b91a27a3f6c3dd95_with_roboflow_yolo.jpg

==================================================
![3d9ed6db90434a2f8b57c1422d910140_jpg.rf.1867ed91ee209f77b91a27a3f6c3dd95_with_roboflow_yolo.jpg](3d9ed6db90434a2f8b57c1422d910140_jpg.rf.1867ed91ee209f77b91a27a3f6c3dd95_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "180", "题目8": "3/7", "题目9": "3000", "题目10": "7/6"}
```
### 响应时间：2.73秒
### token用量
- total_tokens: 796
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 56 张图片: 3fa6a3bb49334977b5a6441db731dc7a_jpg.rf.2ea8ad71ef7e87db2160e74769a299dd_with_roboflow_yolo.jpg

==================================================
![3fa6a3bb49334977b5a6441db731dc7a_jpg.rf.2ea8ad71ef7e87db2160e74769a299dd_with_roboflow_yolo.jpg](3fa6a3bb49334977b5a6441db731dc7a_jpg.rf.2ea8ad71ef7e87db2160e74769a299dd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.1", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：1.98秒
### token用量
- total_tokens: 746
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 57 张图片: 408a6a4ce09b46c187fe10c1d9616a69_jpg.rf.8336ae0cc750a98cdf926092c2828860_with_roboflow_yolo.jpg

==================================================
![408a6a4ce09b46c187fe10c1d9616a69_jpg.rf.8336ae0cc750a98cdf926092c2828860_with_roboflow_yolo.jpg](408a6a4ce09b46c187fe10c1d9616a69_jpg.rf.8336ae0cc750a98cdf926092c2828860_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"0.845","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "0.875", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4.2", "题目 7": "25/64", "题目 8": "6/25"}
```
### 响应时间：2.64秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 58 张图片: 431a6f802e3e45d2b97bf0700b1ee797_jpg.rf.ec56f19148e2ffd855dde2eccd35d126_with_roboflow_yolo.jpg

==================================================
![431a6f802e3e45d2b97bf0700b1ee797_jpg.rf.ec56f19148e2ffd855dde2eccd35d126_with_roboflow_yolo.jpg](431a6f802e3e45d2b97bf0700b1ee797_jpg.rf.ec56f19148e2ffd855dde2eccd35d126_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"5.52","题目 6":"4.8","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4.8", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.93秒
### token用量
- total_tokens: 785
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 63 张图片: 47aaf3c73f2342cebc3dc8bdf6c4d090_jpg.rf.67dfd313b20c480fc773b03a8cc6b988_with_roboflow_yolo.jpg

==================================================
![47aaf3c73f2342cebc3dc8bdf6c4d090_jpg.rf.67dfd313b20c480fc773b03a8cc6b988_with_roboflow_yolo.jpg](47aaf3c73f2342cebc3dc8bdf6c4d090_jpg.rf.67dfd313b20c480fc773b03a8cc6b988_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"6/22","题目 2":"5/7","题目 3":"0.875","题目 4":"1.75","题目 5":"0.512","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "1", "题目4": "1.75", "题目5": "0.552", "题目6": "4 4/5", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.94秒
### token用量
- total_tokens: 785
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 64 张图片: 47b4d8662eaa452d9c8def39b7a51cb0_jpg.rf.a1927ebb0c9cffa18727e373e572b961_with_roboflow_yolo.jpg

==================================================
![47b4d8662eaa452d9c8def39b7a51cb0_jpg.rf.a1927ebb0c9cffa18727e373e572b961_with_roboflow_yolo.jpg](47b4d8662eaa452d9c8def39b7a51cb0_jpg.rf.a1927ebb0c9cffa18727e373e572b961_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"29/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 6/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1.1"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "3", "题目3": "42", "题目4": "9", "题目5": "13", "题目6": "2", "题目7": "0.027", "题目8": "8.92", "题目9": "1", "题目10": "1.2"}
```
### 响应时间：2.30秒
### token用量
- total_tokens: 788
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 65 张图片: 47b833f6d2fe4fc78bf4fc814aa90f9f_jpg.rf.c07e57d5c80774bb6cd76b20dd58720e_with_roboflow_yolo.jpg

==================================================
![47b833f6d2fe4fc78bf4fc814aa90f9f_jpg.rf.c07e57d5c80774bb6cd76b20dd58720e_with_roboflow_yolo.jpg](47b833f6d2fe4fc78bf4fc814aa90f9f_jpg.rf.c07e57d5c80774bb6cd76b20dd58720e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3","题目 9":"3000","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "114", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "1"}
```
### 响应时间：2.95秒
### token用量
- total_tokens: 791
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 67 张图片: 47fe582aa08e427d890254e90dbe026b_jpg.rf.1aec906e82b21e18f56ad85e7e28f1b7_with_roboflow_yolo.jpg

==================================================
![47fe582aa08e427d890254e90dbe026b_jpg.rf.1aec906e82b21e18f56ad85e7e28f1b7_with_roboflow_yolo.jpg](47fe582aa08e427d890254e90dbe026b_jpg.rf.1aec906e82b21e18f56ad85e7e28f1b7_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3","题目 9":"3000","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "1"}
```
### 响应时间：2.98秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 68 张图片: 48392a0f182c4342853e31879fde8bea_jpg.rf.abac1d39969a06615a042709c4e2ae4c_with_roboflow_yolo.jpg

==================================================
![48392a0f182c4342853e31879fde8bea_jpg.rf.abac1d39969a06615a042709c4e2ae4c_with_roboflow_yolo.jpg](48392a0f182c4342853e31879fde8bea_jpg.rf.abac1d39969a06615a042709c4e2ae4c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"11/35","题目 3":"29/42","题目 4":"NAN","题目 5":"NAN","题目 6":"3/4","题目 7":"NAN","题目 8":"NAN","题目 9":"NAN","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "1", "题目 3": "29 / 42", "题目 4": "2", "题目 5": "5 / 6", "题目 6": "3 / 4", "题目 7": "8", "题目 8": "10 / 11", "题目 9": "6"}
```
### 响应时间：2.75秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 70 张图片: 48e1127bbe354ccebb98b1b7374a0dc3_jpg.rf.afca558ed96239b6eb4d9751c7c278db_with_roboflow_yolo.jpg

==================================================
![48e1127bbe354ccebb98b1b7374a0dc3_jpg.rf.afca558ed96239b6eb4d9751c7c278db_with_roboflow_yolo.jpg](48e1127bbe354ccebb98b1b7374a0dc3_jpg.rf.afca558ed96239b6eb4d9751c7c278db_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"1.2","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981", "题目 2": "10", "题目 3": "12", "题目 4": "0.975"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 746
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 71 张图片: 491bc134a0684784a6fab6be4de59980_jpg.rf.57b44546060ee0bb20afbe5013b5e1f5_with_roboflow_yolo.jpg

==================================================
![491bc134a0684784a6fab6be4de59980_jpg.rf.57b44546060ee0bb20afbe5013b5e1f5_with_roboflow_yolo.jpg](491bc134a0684784a6fab6be4de59980_jpg.rf.57b44546060ee0bb20afbe5013b5e1f5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"0.5","题目 4":"NAN","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "30", "题目5": "95", "题目6": "0.5", "题目7": "180", "题目8": "3", "题目9": "3000", "题目10": "2"}
```
### 响应时间：2.67秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 72 张图片: 4b6f701dfcae4c4ebaa84d0c16dd0318_jpg.rf.3c191f922824c21a241f8c7de5cc59b6_with_roboflow_yolo.jpg

==================================================
![4b6f701dfcae4c4ebaa84d0c16dd0318_jpg.rf.3c191f922824c21a241f8c7de5cc59b6_with_roboflow_yolo.jpg](4b6f701dfcae4c4ebaa84d0c16dd0318_jpg.rf.3c191f922824c21a241f8c7de5cc59b6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980 kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "980 千克", "题目 3": "16 本", "题目 4": "45 公顷"}
```
### 响应时间：2.33秒
### token用量
- total_tokens: 750
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 73 张图片: 4d2b22c11dd34e5083d5d33ac5ef9da5_jpg.rf.60fe01fc574c51cef603c24b77e2d051_with_roboflow_yolo.jpg

==================================================
![4d2b22c11dd34e5083d5d33ac5ef9da5_jpg.rf.60fe01fc574c51cef603c24b77e2d051_with_roboflow_yolo.jpg](4d2b22c11dd34e5083d5d33ac5ef9da5_jpg.rf.60fe01fc574c51cef603c24b77e2d051_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "2", "题目9": "3000", "题目10": "2.5"}
```
### 响应时间：2.20秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 74 张图片: 4dc55c3e7b4b45d6ac63b3c8a3d46620_jpg.rf.dcf507d613b8dbf28c603aca07e40cb0_with_roboflow_yolo.jpg

==================================================
![4dc55c3e7b4b45d6ac63b3c8a3d46620_jpg.rf.dcf507d613b8dbf28c603aca07e40cb0_with_roboflow_yolo.jpg](4dc55c3e7b4b45d6ac63b3c8a3d46620_jpg.rf.dcf507d613b8dbf28c603aca07e40cb0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"11/35","题目 3":"29/42","题目 4":"1 2/9","题目 5":"2 1/18","题目 6":"0.75","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "1", "题目 3": "2942", "题目 4": "279", "题目 5": "118", "题目 6": "0.5", "题目 7": "0.04", "题目 8": "8", "题目 9": "1", "题目 10": "NAN"}
```
### 响应时间：3.96秒
### token用量
- total_tokens: 800
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 75 张图片: 4e254c3789a94603b9c6811c2f595ae0_jpg.rf.593010669f6c62854d7e2089b0dde8fa_with_roboflow_yolo.jpg

==================================================
![4e254c3789a94603b9c6811c2f595ae0_jpg.rf.593010669f6c62854d7e2089b0dde8fa_with_roboflow_yolo.jpg](4e254c3789a94603b9c6811c2f595ae0_jpg.rf.593010669f6c62854d7e2089b0dde8fa_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"1 2/9","题目 5":"1 5/18","题目 6":"0.75","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "0.7", "题目3": "0.27", "题目4": "14", "题目5": "21/40", "题目6": "8/15", "题目7": "16/9", "题目8": "5/18", "题目9": "1", "题目10": "3/5"}
```
### 响应时间：3.02秒
### token用量
- total_tokens: 798
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 76 张图片: 4e5c091224a14e3bbaab103d9301dcce_jpg.rf.0312e52d910b30df445ce9acef5c1527_with_roboflow_yolo.jpg

==================================================
![4e5c091224a14e3bbaab103d9301dcce_jpg.rf.0312e52d910b30df445ce9acef5c1527_with_roboflow_yolo.jpg](4e5c091224a14e3bbaab103d9301dcce_jpg.rf.0312e52d910b30df445ce9acef5c1527_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"22/17","题目 3":"29/42","题目 4":"1 2/9","题目 5":"1 5/18","题目 6":"6/8","题目 7":"0.009","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1.2"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "4/5", "题目3": "2/3", "题目4": "1/3", "题目5": "5/18", "题目6": "3/4", "题目7": "0.09", "题目8": "8 4/15", "题目9": "1", "题目10": "1/5"}
```
### 响应时间：2.96秒
### token用量
- total_tokens: 798
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 77 张图片: 4ed9845fc306434792762a74c459def3_jpg.rf.540ffd87e39ce5487e67e9bef4102d52_with_roboflow_yolo.jpg

==================================================
![4ed9845fc306434792762a74c459def3_jpg.rf.540ffd87e39ce5487e67e9bef4102d52_with_roboflow_yolo.jpg](4ed9845fc306434792762a74c459def3_jpg.rf.540ffd87e39ce5487e67e9bef4102d52_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "1", "题目3": "12", "题目4": "0.0175"}
```
### 响应时间：1.80秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 79 张图片: 4f9a4f2c47e94aca959cad738132e097_jpg.rf.89cc4fdb44636399255ee50a40b9dca8_with_roboflow_yolo.jpg

==================================================
![4f9a4f2c47e94aca959cad738132e097_jpg.rf.89cc4fdb44636399255ee50a40b9dca8_with_roboflow_yolo.jpg](4f9a4f2c47e94aca959cad738132e097_jpg.rf.89cc4fdb44636399255ee50a40b9dca8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"5/8","题目 6":"4 4/5","题目 7":"25/64","题目 8":"26/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "NAN", "题目6": "1/4", "题目7": "4/14", "题目8": "27/25"}
```
### 响应时间：2.88秒
### token用量
- total_tokens: 782
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 80 张图片: 5006e3fbdef349bba6e3583df9831378_jpg.rf.fc47fcd22885fd5a8bf1c0521bf2dc66_with_roboflow_yolo.jpg

==================================================
![5006e3fbdef349bba6e3583df9831378_jpg.rf.fc47fcd22885fd5a8bf1c0521bf2dc66_with_roboflow_yolo.jpg](5006e3fbdef349bba6e3583df9831378_jpg.rf.fc47fcd22885fd5a8bf1c0521bf2dc66_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 15/18","题目 6":"3/4","题目 7":"300","题目 8":"8 14/15","题目 9":"7/11","题目 10":"1.6"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "35分之29", "题目3": "42分之13", "题目4": "9分之11", "题目5": "18分之5", "题目6": "8分之3", "题目7": "0.09", "题目8": "8.8", "题目9": "1", "题目10": "1.2"}
```
### 响应时间：2.52秒
### token用量
- total_tokens: 800
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 81 张图片: 5034962277d443289fbb78ddd30ddf31_jpg.rf.28a24f18b45cb9ae8ab4039ad1518a9a_with_roboflow_yolo.jpg

==================================================
![5034962277d443289fbb78ddd30ddf31_jpg.rf.28a24f18b45cb9ae8ab4039ad1518a9a_with_roboflow_yolo.jpg](5034962277d443289fbb78ddd30ddf31_jpg.rf.28a24f18b45cb9ae8ab4039ad1518a9a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"971.1","题目 2":"1","题目 3":"12","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目1": "971.1", "题目2": "1", "题目3": "12"}
```
### 响应时间：2.06秒
### token用量
- total_tokens: 731
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 82 张图片: 52acf189f9d14d28a3b4e092a0b66d8c_jpg.rf.42646802df9ed1087632079f639173bb_with_roboflow_yolo.jpg

==================================================
![52acf189f9d14d28a3b4e092a0b66d8c_jpg.rf.42646802df9ed1087632079f639173bb_with_roboflow_yolo.jpg](52acf189f9d14d28a3b4e092a0b66d8c_jpg.rf.42646802df9ed1087632079f639173bb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "6", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 83 张图片: 555a8b0f64974a27b8fc5a1c258c2fcc_jpg.rf.472378acc8e97d213401e19742219de2_with_roboflow_yolo.jpg

==================================================
![555a8b0f64974a27b8fc5a1c258c2fcc_jpg.rf.472378acc8e97d213401e19742219de2_with_roboflow_yolo.jpg](555a8b0f64974a27b8fc5a1c258c2fcc_jpg.rf.472378acc8e97d213401e19742219de2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980千克","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "980", "题目 3": "16", "题目 4": "45"}
```
### 响应时间：1.91秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 84 张图片: 56281af97c504fdda89fadae60627fc7_jpg.rf.f933c69f900fbaed54206bf0bd6316d2_with_roboflow_yolo.jpg

==================================================
![56281af97c504fdda89fadae60627fc7_jpg.rf.f933c69f900fbaed54206bf0bd6316d2_with_roboflow_yolo.jpg](56281af97c504fdda89fadae60627fc7_jpg.rf.f933c69f900fbaed54206bf0bd6316d2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.1", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：1.49秒
### token用量
- total_tokens: 746
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 85 张图片: 56b4acc190634af38fcd7b89cb24376d_jpg.rf.1f5943a315e86a6b16408ab90d380928_with_roboflow_yolo.jpg

==================================================
![56b4acc190634af38fcd7b89cb24376d_jpg.rf.1f5943a315e86a6b16408ab90d380928_with_roboflow_yolo.jpg](56b4acc190634af38fcd7b89cb24376d_jpg.rf.1f5943a315e86a6b16408ab90d380928_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980(kg)","题目 3":"16(本)","题目 4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "980", "题目 3": "16", "题目 4": "45"}
```
### 响应时间：1.73秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 86 张图片: 57834bbdbccf4a9599b8e824e3284d45_jpg.rf.eae53f59d0d70ce2d8fc203c6c7ddf3a_with_roboflow_yolo.jpg

==================================================
![57834bbdbccf4a9599b8e824e3284d45_jpg.rf.eae53f59d0d70ce2d8fc203c6c7ddf3a_with_roboflow_yolo.jpg](57834bbdbccf4a9599b8e824e3284d45_jpg.rf.eae53f59d0d70ce2d8fc203c6c7ddf3a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"NAN","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "700", "题目8": "3", "题目9": "3000", "题目10": "7"}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 87 张图片: 57ec5f0d529c4542ac3ab8325bba9aa0_jpg.rf.26b68c41a4231bd028a7f6a401dfd7b8_with_roboflow_yolo.jpg

==================================================
![57ec5f0d529c4542ac3ab8325bba9aa0_jpg.rf.26b68c41a4231bd028a7f6a401dfd7b8_with_roboflow_yolo.jpg](57ec5f0d529c4542ac3ab8325bba9aa0_jpg.rf.26b68c41a4231bd028a7f6a401dfd7b8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "14", "题目 4": "0.0975"}
```
### 响应时间：2.18秒
### token用量
- total_tokens: 749
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 88 张图片: 5a1ae2e606d54046a3ce46b261d444e6_jpg.rf.0f75af15a938d4e9f4206784858e2d73_with_roboflow_yolo.jpg

==================================================
![5a1ae2e606d54046a3ce46b261d444e6_jpg.rf.0f75af15a938d4e9f4206784858e2d73_with_roboflow_yolo.jpg](5a1ae2e606d54046a3ce46b261d444e6_jpg.rf.0f75af15a938d4e9f4206784858e2d73_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"18/75"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4/5", "题目7": "25/64", "题目8": "18/25"}
```
### 响应时间：2.83秒
### token用量
- total_tokens: 786
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 89 张图片: 5aa12dd037fe44089bb5be3abec30569_jpg.rf.ff8bc6aba3b20df6b9d69c039041dc32_with_roboflow_yolo.jpg

==================================================
![5aa12dd037fe44089bb5be3abec30569_jpg.rf.ff8bc6aba3b20df6b9d69c039041dc32_with_roboflow_yolo.jpg](5aa12dd037fe44089bb5be3abec30569_jpg.rf.ff8bc6aba3b20df6b9d69c039041dc32_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：1.80秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 90 张图片: 5afc1708f8ab44d9bd222c7f1ea9fe6a_jpg.rf.5f8d40e6371d27997737b40080b87507_with_roboflow_yolo.jpg

==================================================
![5afc1708f8ab44d9bd222c7f1ea9fe6a_jpg.rf.5f8d40e6371d27997737b40080b87507_with_roboflow_yolo.jpg](5afc1708f8ab44d9bd222c7f1ea9fe6a_jpg.rf.5f8d40e6371d27997737b40080b87507_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"NAN","题目 3":"15","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "5/7", "题目9": "3000", "题目10": "7/9"}
```
### 响应时间：2.81秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 91 张图片: 5b9e8d7311e14684b3203eb7991cfbe6_jpg.rf.9ae50ea2dddeb1cc1996f05a4a6fd655_with_roboflow_yolo.jpg

==================================================
![5b9e8d7311e14684b3203eb7991cfbe6_jpg.rf.9ae50ea2dddeb1cc1996f05a4a6fd655_with_roboflow_yolo.jpg](5b9e8d7311e14684b3203eb7991cfbe6_jpg.rf.9ae50ea2dddeb1cc1996f05a4a6fd655_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.3","题目 7":"780","题目 8":"0","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "2", "题目9": "3000", "题目10": "2"}
```
### 响应时间：2.69秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 92 张图片: 5bef4fc97a0a4cc2af3e67888a026b75_jpg.rf.45a202d4334388403c296cd097b3f51d_with_roboflow_yolo.jpg

==================================================
![5bef4fc97a0a4cc2af3e67888a026b75_jpg.rf.45a202d4334388403c296cd097b3f51d_with_roboflow_yolo.jpg](5bef4fc97a0a4cc2af3e67888a026b75_jpg.rf.45a202d4334388403c296cd097b3f51d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4 4/5", "题目7": "25/64", "题目8": "6/5"}
```
### 响应时间：2.67秒
### token用量
- total_tokens: 786
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 94 张图片: 6166afd575264747825fd59bac26e338_jpg.rf.b859eb5dee6a983b0f4a665e81a4a111_with_roboflow_yolo.jpg

==================================================
![6166afd575264747825fd59bac26e338_jpg.rf.b859eb5dee6a983b0f4a665e81a4a111_with_roboflow_yolo.jpg](6166afd575264747825fd59bac26e338_jpg.rf.b859eb5dee6a983b0f4a665e81a4a111_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "4", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "NAN", "题目 9": "3000", "题目 10": "NAN"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 802
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 96 张图片: 61fbea55414d4052868733491c21af45_jpg.rf.43690edc1a340cbf57f97df233deb16c_with_roboflow_yolo.jpg

==================================================
![61fbea55414d4052868733491c21af45_jpg.rf.43690edc1a340cbf57f97df233deb16c_with_roboflow_yolo.jpg](61fbea55414d4052868733491c21af45_jpg.rf.43690edc1a340cbf57f97df233deb16c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "7"}
```
### 响应时间：3.07秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 97 张图片: 620b499b8e3242769d766bb7f9dc38a4_jpg.rf.e613e69112e79258988a6cfcac8dce0a_with_roboflow_yolo.jpg

==================================================
![620b499b8e3242769d766bb7f9dc38a4_jpg.rf.e613e69112e79258988a6cfcac8dce0a_with_roboflow_yolo.jpg](620b499b8e3242769d766bb7f9dc38a4_jpg.rf.e613e69112e79258988a6cfcac8dce0a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"1 2/9","题目 5":"1 5/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 1/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "3/4", "题目3": "17/35", "题目4": "29/42", "题目5": "5/6", "题目6": "23/18", "题目7": "1/2", "题目8": "8 4/15", "题目9": "11/12", "题目10": "6/5"}
```
### 响应时间：2.69秒
### token用量
- total_tokens: 806
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 99 张图片: 63c0da2d288f4d6886068ad1569bde05_jpg.rf.b139257c01f72cf5203eba4736ae7236_with_roboflow_yolo.jpg

==================================================
![63c0da2d288f4d6886068ad1569bde05_jpg.rf.b139257c01f72cf5203eba4736ae7236_with_roboflow_yolo.jpg](63c0da2d288f4d6886068ad1569bde05_jpg.rf.b139257c01f72cf5203eba4736ae7236_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "180", "题目8": "3/7", "题目9": "3000", "题目10": "1/7"}
```
### 响应时间：3.76秒
### token用量
- total_tokens: 796
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 100 张图片: 64cba5d941ce4f96b6a6f97edc572008_jpg.rf.9ebf4f72bd4efbce1748bf5b45bc006f_with_roboflow_yolo.jpg

==================================================
![64cba5d941ce4f96b6a6f97edc572008_jpg.rf.9ebf4f72bd4efbce1748bf5b45bc006f_with_roboflow_yolo.jpg](64cba5d941ce4f96b6a6f97edc572008_jpg.rf.9ebf4f72bd4efbce1748bf5b45bc006f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"1 2/9","题目 5":"1 5/18","题目 6":"6/8","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "17/35", "题目3": "29/42", "题目4": "1/9", "题目5": "1/2", "题目6": "3/4", "题目7": "0.27", "题目8": "14/15", "题目9": "1", "题目10": "3"}
```
### 响应时间：2.20秒
### token用量
- total_tokens: 798
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 101 张图片: 64e3e495a199417e8a8e4620728db510_jpg.rf.6f344ec91a3697d4bf71374d628367fe_with_roboflow_yolo.jpg

==================================================
![64e3e495a199417e8a8e4620728db510_jpg.rf.6f344ec91a3697d4bf71374d628367fe_with_roboflow_yolo.jpg](64e3e495a199417e8a8e4620728db510_jpg.rf.6f344ec91a3697d4bf71374d628367fe_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"215","题目 3":"1.5","题目 4":"80","题目 5":"545","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "NAN", "题目9": "3000", "题目10": "NAN"}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 102 张图片: 662f05762efd4e409e847909e1efe6f7_jpg.rf.6ea62d04ff61d66e4df28442ced7eeb5_with_roboflow_yolo.jpg

==================================================
![662f05762efd4e409e847909e1efe6f7_jpg.rf.6ea62d04ff61d66e4df28442ced7eeb5_with_roboflow_yolo.jpg](662f05762efd4e409e847909e1efe6f7_jpg.rf.6ea62d04ff61d66e4df28442ced7eeb5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"0.75","题目 7":"0.0027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1.8"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "35/12", "题目3": "2/3", "题目4": "1/3", "题目5": "5/18", "题目6": "0.5", "题目7": "0.027", "题目8": "25/3", "题目9": "8/11", "题目10": "0.3"}
```
### 响应时间：2.86秒
### token用量
- total_tokens: 802
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 104 张图片: 6812bf8f1bcf431bbc412686e722f216_jpg.rf.d599437cdf68f1f14d52d583cb9a842b_with_roboflow_yolo.jpg

==================================================
![6812bf8f1bcf431bbc412686e722f216_jpg.rf.d599437cdf68f1f14d52d583cb9a842b_with_roboflow_yolo.jpg](6812bf8f1bcf431bbc412686e722f216_jpg.rf.d599437cdf68f1f14d52d583cb9a842b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980千克","题目 3":"16(本)","题目 4":"45(公顷)"}
```
### 响应内容：
```json
{"题目1": "1 - \frac{1}{4} - \frac{7}{12} = \frac{1}{6}", "题目2": "420 \div \frac{3}{7} = 980(千克)=980千克", "题目3": "24\times\frac{2}{3}=16(本)", "题目4": "75\times\frac{3}{5}=45(公顷)"}
```
### 响应时间：2.96秒
### token用量
- total_tokens: 806
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 105 张图片: 697cc1232143483e803bbd2666fb18d6_jpg.rf.c58f0a71288325b3cd3dfbe053bce33b_with_roboflow_yolo.jpg

==================================================
![697cc1232143483e803bbd2666fb18d6_jpg.rf.c58f0a71288325b3cd3dfbe053bce33b_with_roboflow_yolo.jpg](697cc1232143483e803bbd2666fb18d6_jpg.rf.c58f0a71288325b3cd3dfbe053bce33b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4 4/5", "题目7": "25/16", "题目8": "6/25"}
```
### 响应时间：2.83秒
### token用量
- total_tokens: 787
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 106 张图片: 6a2a73ca9d644b5488fb65a988544b12_jpg.rf.df8e1ff18589b370fda8940511c94258_with_roboflow_yolo.jpg

==================================================
![6a2a73ca9d644b5488fb65a988544b12_jpg.rf.df8e1ff18589b370fda8940511c94258_with_roboflow_yolo.jpg](6a2a73ca9d644b5488fb65a988544b12_jpg.rf.df8e1ff18589b370fda8940511c94258_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"NAN","题目 3":"NAN","题目 4":"NAN","题目 5":"NAN","题目 6":"NAN","题目 7":"0.9","题目 8":"NAN","题目 9":"8/11","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "1/13", "题目2": "0.9", "题目3": "NAN"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 733
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 110 张图片: 6c8ce884048245cb8560f19edb3b0b35_jpg.rf.27764484c78d028e83b8bc3b08220347_with_roboflow_yolo.jpg

==================================================
![6c8ce884048245cb8560f19edb3b0b35_jpg.rf.27764484c78d028e83b8bc3b08220347_with_roboflow_yolo.jpg](6c8ce884048245cb8560f19edb3b0b35_jpg.rf.27764484c78d028e83b8bc3b08220347_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.015"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 111 张图片: 6d22646fa64c42888c12fc3e1308a0dd_jpg.rf.6d0154d932c7d7f0a9f8502462d0dd05_with_roboflow_yolo.jpg

==================================================
![6d22646fa64c42888c12fc3e1308a0dd_jpg.rf.6d0154d932c7d7f0a9f8502462d0dd05_with_roboflow_yolo.jpg](6d22646fa64c42888c12fc3e1308a0dd_jpg.rf.6d0154d932c7d7f0a9f8502462d0dd05_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "1"}
```
### 响应时间：3.24秒
### token用量
- total_tokens: 790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 112 张图片: 6db37c7ed5f94d42814f79744088f691_jpg.rf.5816dbe69a74c86d7ff899b85a8ed46c_with_roboflow_yolo.jpg

==================================================
![6db37c7ed5f94d42814f79744088f691_jpg.rf.5816dbe69a74c86d7ff899b85a8ed46c_with_roboflow_yolo.jpg](6db37c7ed5f94d42814f79744088f691_jpg.rf.5816dbe69a74c86d7ff899b85a8ed46c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980(kg)","题目 3":"16(本)","题目 4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "980", "题目 3": "16", "题目 4": "45"}
```
### 响应时间：1.90秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 113 张图片: 6eeefb48b1004340a7d3d74fd065e5e9_jpg.rf.3ded7549a7f306b1eb3765c5996edadf_with_roboflow_yolo.jpg

==================================================
![6eeefb48b1004340a7d3d74fd065e5e9_jpg.rf.3ded7549a7f306b1eb3765c5996edadf_with_roboflow_yolo.jpg](6eeefb48b1004340a7d3d74fd065e5e9_jpg.rf.3ded7549a7f306b1eb3765c5996edadf_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"NAN","题目 3":"12","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目 1": "481.1", "题目 2": "12.0", "题目 3": "NAN"}
```
### 响应时间：1.63秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 114 张图片: 6f9428b1466747c58cea1da07d56ba7a_jpg.rf.327c4fb2f8afaf71c68f6e9681546e04_with_roboflow_yolo.jpg

==================================================
![6f9428b1466747c58cea1da07d56ba7a_jpg.rf.327c4fb2f8afaf71c68f6e9681546e04_with_roboflow_yolo.jpg](6f9428b1466747c58cea1da07d56ba7a_jpg.rf.327c4fb2f8afaf71c68f6e9681546e04_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0625"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0675"}
```
### 响应时间：2.92秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 115 张图片: 710e9137fd424712b03de5d4aa1f7638_jpg.rf.2fe6523791f27b0a72c283a0d0dddfa8_with_roboflow_yolo.jpg

==================================================
![710e9137fd424712b03de5d4aa1f7638_jpg.rf.2fe6523791f27b0a72c283a0d0dddfa8_with_roboflow_yolo.jpg](710e9137fd424712b03de5d4aa1f7638_jpg.rf.2fe6523791f27b0a72c283a0d0dddfa8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "80", "题目8": "3", "题目9": "3000", "题目10": "4"}
```
### 响应时间：2.53秒
### token用量
- total_tokens: 789
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 116 张图片: 71cf79c1bce44b748bd77890eb05e701_jpg.rf.8db1f3f435bf2fc858cf1a120e686a0b_with_roboflow_yolo.jpg

==================================================
![71cf79c1bce44b748bd77890eb05e701_jpg.rf.8db1f3f435bf2fc858cf1a120e686a0b_with_roboflow_yolo.jpg](71cf79c1bce44b748bd77890eb05e701_jpg.rf.8db1f3f435bf2fc858cf1a120e686a0b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"11/3","题目 2":"28/20","题目 3":"4/1.625","题目 4":"1.75","题目 5":"0.552","题目 6":"5/4","题目 7":"0.625/1.6","题目 8":"0.6/25"}
```
### 响应内容：
```json
{"题目 1": "3 / 11", "题目 2": "5 / 7", "题目 3": "1", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 / 5", "题目 7": "25 / 64", "题目 8": "6 / 25"}
```
### 响应时间：2.58秒
### token用量
- total_tokens: 796
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 117 张图片: 723ecc34b5a1411191b752466ff27674_jpg.rf.fb73f583c0957309804bafa8712ff461_with_roboflow_yolo.jpg

==================================================
![723ecc34b5a1411191b752466ff27674_jpg.rf.fb73f583c0957309804bafa8712ff461_with_roboflow_yolo.jpg](723ecc34b5a1411191b752466ff27674_jpg.rf.fb73f583c0957309804bafa8712ff461_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"10","题目 4":"0.15"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.1", "题目3": "12", "题目4": "0.15"}
```
### 响应时间：1.97秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 118 张图片: 73505ed74af64f7e8c33078fa5dafcbb_jpg.rf.3bd405e7489d3f7b755d9d98d88ec512_with_roboflow_yolo.jpg

==================================================
![73505ed74af64f7e8c33078fa5dafcbb_jpg.rf.3bd405e7489d3f7b755d9d98d88ec512_with_roboflow_yolo.jpg](73505ed74af64f7e8c33078fa5dafcbb_jpg.rf.3bd405e7489d3f7b755d9d98d88ec512_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/13","题目 2":"34/70","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"NAN","题目 7":"NAN","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1.6"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "7/10", "题目3": "1/2", "题目4": "4", "题目5": "7/18", "题目6": "0", "题目7": "0.001", "题目8": "8 14/15", "题目9": "1 8/11", "题目10": "1.2"}
```
### 响应时间：3.75秒
### token用量
- total_tokens: 802
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 120 张图片: 787b173bb1c848a797e8972b1e6e21ab_jpg.rf.83f26a1f8e90af78589e7823791326f8_with_roboflow_yolo.jpg

==================================================
![787b173bb1c848a797e8972b1e6e21ab_jpg.rf.83f26a1f8e90af78589e7823791326f8_with_roboflow_yolo.jpg](787b173bb1c848a797e8972b1e6e21ab_jpg.rf.83f26a1f8e90af78589e7823791326f8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "5/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4/5", "题目7": "25/64", "题目8": "1/20"}
```
### 响应时间：3.28秒
### token用量
- total_tokens: 785
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 121 张图片: 793ddd316bcf4c608576091beaec24fc_jpg.rf.5d2f02e5813d663f56f6e07c0896c02b_with_roboflow_yolo.jpg

==================================================
![793ddd316bcf4c608576091beaec24fc_jpg.rf.5d2f02e5813d663f56f6e07c0896c02b_with_roboflow_yolo.jpg](793ddd316bcf4c608576091beaec24fc_jpg.rf.5d2f02e5813d663f56f6e07c0896c02b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"75"}
```
### 响应内容：
```json
{"题目1": "81.1", "题目2": "0.01", "题目3": "12", "题目4": "0.75"}
```
### 响应时间：1.97秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 122 张图片: 7961c834a9d54e3595d3f5409cb71809_jpg.rf.af1a2425a6796273ec516c2431dfd2e5_with_roboflow_yolo.jpg

==================================================
![7961c834a9d54e3595d3f5409cb71809_jpg.rf.af1a2425a6796273ec516c2431dfd2e5_with_roboflow_yolo.jpg](7961c834a9d54e3595d3f5409cb71809_jpg.rf.af1a2425a6796273ec516c2431dfd2e5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "181.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：2.36秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 123 张图片: 79633ecfa8a24da4809c6852f12fd0da_jpg.rf.75868653b880563cc7de1d22f014b758_with_roboflow_yolo.jpg

==================================================
![79633ecfa8a24da4809c6852f12fd0da_jpg.rf.75868653b880563cc7de1d22f014b758_with_roboflow_yolo.jpg](79633ecfa8a24da4809c6852f12fd0da_jpg.rf.75868653b880563cc7de1d22f014b758_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：2.09秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 124 张图片: 79cff13f63ec4b558b6c09a1c75950a8_jpg.rf.4e11aa0be71cacf4314342717ec2ffe0_with_roboflow_yolo.jpg

==================================================
![79cff13f63ec4b558b6c09a1c75950a8_jpg.rf.4e11aa0be71cacf4314342717ec2ffe0_with_roboflow_yolo.jpg](79cff13f63ec4b558b6c09a1c75950a8_jpg.rf.4e11aa0be71cacf4314342717ec2ffe0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"1260(kg)","题目 3":"16(本)","题目 4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "180(kg)", "题目 3": "1260(kg)", "题目 4": "16(本)", "题目 5": "45(公顷)"}
```
### 响应时间：2.63秒
### token用量
- total_tokens: 763
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 125 张图片: 79d2ce7013d243e19197c8d48cd80a39_jpg.rf.2dfe76df5c6f3bd0e1468329287ab678_with_roboflow_yolo.jpg

==================================================
![79d2ce7013d243e19197c8d48cd80a39_jpg.rf.2dfe76df5c6f3bd0e1468329287ab678_with_roboflow_yolo.jpg](79d2ce7013d243e19197c8d48cd80a39_jpg.rf.2dfe76df5c6f3bd0e1468329287ab678_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"31/35","题目 3":"29/42","题目 4":"11/9","题目 5":"23/18","题目 6":"3/4","题目 7":"0.0027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "31/35", "题目3": "19/42", "题目4": "1", "题目5": "23/15", "题目6": "3/4", "题目7": "0.002", "题目8": "8", "题目9": "8/11", "题目10": "13/10"}
```
### 响应时间：3.79秒
### token用量
- total_tokens: 802
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 126 张图片: 7a45180600544238a447105bca060273_jpg.rf.e66cc29244b1b46475eb387edad97a33_with_roboflow_yolo.jpg

==================================================
![7a45180600544238a447105bca060273_jpg.rf.e66cc29244b1b46475eb387edad97a33_with_roboflow_yolo.jpg](7a45180600544238a447105bca060273_jpg.rf.e66cc29244b1b46475eb387edad97a33_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.02"}
```
### 响应时间：2.30秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 127 张图片: 7b93dfa0205343fb9c5b1d323bea3c3d_jpg.rf.d9755eb95a2ae14ed7fe04003d872746_with_roboflow_yolo.jpg

==================================================
![7b93dfa0205343fb9c5b1d323bea3c3d_jpg.rf.d9755eb95a2ae14ed7fe04003d872746_with_roboflow_yolo.jpg](7b93dfa0205343fb9c5b1d323bea3c3d_jpg.rf.d9755eb95a2ae14ed7fe04003d872746_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.1075"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 749
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 128 张图片: 7f35d6129ae449ac8078d0b40b835a41_jpg.rf.91804e7ff94f8bb78bacc0f8e3fab057_with_roboflow_yolo.jpg

==================================================
![7f35d6129ae449ac8078d0b40b835a41_jpg.rf.91804e7ff94f8bb78bacc0f8e3fab057_with_roboflow_yolo.jpg](7f35d6129ae449ac8078d0b40b835a41_jpg.rf.91804e7ff94f8bb78bacc0f8e3fab057_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"5/8","题目 4":"1.75","题目 5":"0.552","题目 6":"24/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：3.20秒
### token用量
- total_tokens: 783
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 129 张图片: 7f41eae9d2f94fa2bad4dcf95af2529e_jpg.rf.d133c5bf1ac1e16daa74ae015157aab1_with_roboflow_yolo.jpg

==================================================
![7f41eae9d2f94fa2bad4dcf95af2529e_jpg.rf.d133c5bf1ac1e16daa74ae015157aab1_with_roboflow_yolo.jpg](7f41eae9d2f94fa2bad4dcf95af2529e_jpg.rf.d133c5bf1ac1e16daa74ae015157aab1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980千克","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1/4 + 7/12 = 5/6", "题目 2": "420÷3/7 = 980", "题目 3": "24÷3×2 = 16", "题目 4": "75×3/5 = 45"}
```
### 响应时间：3.16秒
### token用量
- total_tokens: 780
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 130 张图片: 7f734a014cea4343bada6d73fa5008fc_jpg.rf.fb5209153b8fd8e237e4f7e2dec74d27_with_roboflow_yolo.jpg

==================================================
![7f734a014cea4343bada6d73fa5008fc_jpg.rf.fb5209153b8fd8e237e4f7e2dec74d27_with_roboflow_yolo.jpg](7f734a014cea4343bada6d73fa5008fc_jpg.rf.fb5209153b8fd8e237e4f7e2dec74d27_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"14/35","题目 3":"29/42","题目 4":"11/9","题目 5":"23/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 1/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "3/4", "题目3": "14/53", "题目4": "0.04", "题目5": "24/42", "题目6": "14/15", "题目7": "1/9", "题目8": "8/11", "题目9": "23/18", "题目10": "1/15"}
```
### 响应时间：3.49秒
### token用量
- total_tokens: 806
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 132 张图片: 836ad0526d3c4bbaa90ae119f8375188_jpg.rf.6d2823e7b13759bba9a3572e5fc5f803_with_roboflow_yolo.jpg

==================================================
![836ad0526d3c4bbaa90ae119f8375188_jpg.rf.6d2823e7b13759bba9a3572e5fc5f803_with_roboflow_yolo.jpg](836ad0526d3c4bbaa90ae119f8375188_jpg.rf.6d2823e7b13759bba9a3572e5fc5f803_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "1-\frac{1}{4}-\frac{7}{12}=\frac{1}{6}", "题目2": "420\div\frac{2}{7}=1470kg", "题目3": "24\times(1-\frac{1}{3}) = 16本", "题目4": "75\times\frac{3}{5}=45公顷"}
```
### 响应时间：3.86秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 133 张图片: 85119effdcb24215ae82a90692c42ed9_jpg.rf.57619e3b445255eb98f471b3b21ef146_with_roboflow_yolo.jpg

==================================================
![85119effdcb24215ae82a90692c42ed9_jpg.rf.57619e3b445255eb98f471b3b21ef146_with_roboflow_yolo.jpg](85119effdcb24215ae82a90692c42ed9_jpg.rf.57619e3b445255eb98f471b3b21ef146_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/13=1","题目 2":"11/35","题目 3":"29/42","题目 4":"11/9","题目 5":"23/18","题目 6":"0.75","题目 7":"0.027","题目 8":"126/135","题目 9":"7/11","题目 10":"7/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "11/35", "题目3": "1/42", "题目4": "26/15", "题目5": "11/9", "题目6": "25/18", "题目7": "0.75", "题目8": "0.09", "题目9": "1", "题目10": "7/5"}
```
### 响应时间：3.05秒
### token用量
- total_tokens: 803
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 134 张图片: 88fc7a151a3e40ed89ff0f65bcc414da_jpg.rf.d4bec114913e41f88e33620f553e0e5d_with_roboflow_yolo.jpg

==================================================
![88fc7a151a3e40ed89ff0f65bcc414da_jpg.rf.d4bec114913e41f88e33620f553e0e5d_with_roboflow_yolo.jpg](88fc7a151a3e40ed89ff0f65bcc414da_jpg.rf.d4bec114913e41f88e33620f553e0e5d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"0","题目 10":"1 1/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "35", "题目3": "24", "题目4": "9", "题目5": "18", "题目6": "3/4", "题目7": "0.27", "题目8": "8/15", "题目9": "0", "题目10": "1"}
```
### 响应时间：2.70秒
### token用量
- total_tokens: 788
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 135 张图片: 89e3024dc8d143dfaeb0888e7a8dcbc6_jpg.rf.0efe717eca557b0851e6d5c705a978c6_with_roboflow_yolo.jpg

==================================================
![89e3024dc8d143dfaeb0888e7a8dcbc6_jpg.rf.0efe717eca557b0851e6d5c705a978c6_with_roboflow_yolo.jpg](89e3024dc8d143dfaeb0888e7a8dcbc6_jpg.rf.0efe717eca557b0851e6d5c705a978c6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "181.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 749
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 136 张图片: 8ad24a09126c4f5590ae13f4b1390cd0_jpg.rf.db27bd3b540ff22d651b8db0f8fa2563_with_roboflow_yolo.jpg

==================================================
![8ad24a09126c4f5590ae13f4b1390cd0_jpg.rf.db27bd3b540ff22d651b8db0f8fa2563_with_roboflow_yolo.jpg](8ad24a09126c4f5590ae13f4b1390cd0_jpg.rf.db27bd3b540ff22d651b8db0f8fa2563_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980(kg)","题目 3":"16(本)","题目 4":"45公顷)"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "980", "题目 3": "16", "题目 4": "45"}
```
### 响应时间：1.69秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 137 张图片: 8c175831356b441981d8a85dc1709861_jpg.rf.bc0f68c750389938df99e76ac14b5e9d_with_roboflow_yolo.jpg

==================================================
![8c175831356b441981d8a85dc1709861_jpg.rf.bc0f68c750389938df99e76ac14b5e9d_with_roboflow_yolo.jpg](8c175831356b441981d8a85dc1709861_jpg.rf.bc0f68c750389938df99e76ac14b5e9d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 1/5", "题目 7": "25/64", "题目 8": "16/25"}
```
### 响应时间：2.76秒
### token用量
- total_tokens: 796
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 138 张图片: 8d122665524045ab8d095ba8a680489c_jpg.rf.d0dba321c4fadc06021c7cec2cd97e19_with_roboflow_yolo.jpg

==================================================
![8d122665524045ab8d095ba8a680489c_jpg.rf.d0dba321c4fadc06021c7cec2cd97e19_with_roboflow_yolo.jpg](8d122665524045ab8d095ba8a680489c_jpg.rf.d0dba321c4fadc06021c7cec2cd97e19_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "3"}
```
### 响应时间：3.42秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 139 张图片: 8d1845863a5146448b7d7568ddd71582_jpg.rf.7a3fb04412f112492867cc8e0dacc413_with_roboflow_yolo.jpg

==================================================
![8d1845863a5146448b7d7568ddd71582_jpg.rf.7a3fb04412f112492867cc8e0dacc413_with_roboflow_yolo.jpg](8d1845863a5146448b7d7568ddd71582_jpg.rf.7a3fb04412f112492867cc8e0dacc413_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.01", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 140 张图片: 8df94d5708174e278f7bc3fcbd9be1ef_jpg.rf.830064cd7b07e058758484c29fb43e1c_with_roboflow_yolo.jpg

==================================================
![8df94d5708174e278f7bc3fcbd9be1ef_jpg.rf.830064cd7b07e058758484c29fb43e1c_with_roboflow_yolo.jpg](8df94d5708174e278f7bc3fcbd9be1ef_jpg.rf.830064cd7b07e058758484c29fb43e1c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"NAN"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.42秒
### token用量
- total_tokens: 783
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 141 张图片: 8ec00aee4b954fc9be8d3e649b691e2b_jpg.rf.4fc3c497c36b78df303d1d4ecb81c0b1_with_roboflow_yolo.jpg

==================================================
![8ec00aee4b954fc9be8d3e649b691e2b_jpg.rf.4fc3c497c36b78df303d1d4ecb81c0b1_with_roboflow_yolo.jpg](8ec00aee4b954fc9be8d3e649b691e2b_jpg.rf.4fc3c497c36b78df303d1d4ecb81c0b1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4 4/5", "题目7": "25/44", "题目8": "6/25"}
```
### 响应时间：3.08秒
### token用量
- total_tokens: 787
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 142 张图片: 8eebf087dea24ad78b429dd51cb24e16_jpg.rf.e0dcce6f38536c301a128c26c251ae67_with_roboflow_yolo.jpg

==================================================
![8eebf087dea24ad78b429dd51cb24e16_jpg.rf.e0dcce6f38536c301a128c26c251ae67_with_roboflow_yolo.jpg](8eebf087dea24ad78b429dd51cb24e16_jpg.rf.e0dcce6f38536c301a128c26c251ae67_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"NAN","题目 3":"NAN","题目 4":"5/11","题目 5":"NAN","题目 6":"6/8","题目 7":"NAN","题目 8":"8/12","题目 9":"0","题目 10":"10"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "0.5", "题目3": "NAN", "题目4": "NAN", "题目5": "NAN", "题目6": "NAN", "题目7": "0.75", "题目8": "0.09", "题目9": "8", "题目10": "NAN", "题目11": "NAN"}
```
### 响应时间：2.91秒
### token用量
- total_tokens: 799
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 143 张图片: 91d8a3ca9326441ca54cfc7d4bebfb53_jpg.rf.85c0a4c96e677f17552316e715eb42fd_with_roboflow_yolo.jpg

==================================================
![91d8a3ca9326441ca54cfc7d4bebfb53_jpg.rf.85c0a4c96e677f17552316e715eb42fd_with_roboflow_yolo.jpg](91d8a3ca9326441ca54cfc7d4bebfb53_jpg.rf.85c0a4c96e677f17552316e715eb42fd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "1/6", "题目2": "980", "题目3": "16", "题目4": "45"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 740
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 144 张图片: 9524ce80e52e48c4b10b50c5b28640fc_jpg.rf.6412ca3f9773923ca417722c903d12bd_with_roboflow_yolo.jpg

==================================================
![9524ce80e52e48c4b10b50c5b28640fc_jpg.rf.6412ca3f9773923ca417722c903d12bd_with_roboflow_yolo.jpg](9524ce80e52e48c4b10b50c5b28640fc_jpg.rf.6412ca3f9773923ca417722c903d12bd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4 1/5", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：3.02秒
### token用量
- total_tokens: 787
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 145 张图片: 955406664f3e49f587f83a4d12fdaa53_jpg.rf.d0fb507fc8ced6027034b3b6fecb05be_with_roboflow_yolo.jpg

==================================================
![955406664f3e49f587f83a4d12fdaa53_jpg.rf.d0fb507fc8ced6027034b3b6fecb05be_with_roboflow_yolo.jpg](955406664f3e49f587f83a4d12fdaa53_jpg.rf.d0fb507fc8ced6027034b3b6fecb05be_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "45", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "1"}
```
### 响应时间：2.83秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 146 张图片: 97540b962de444fa87d0ee5168e9fb03_jpg.rf.1daf57f6f4cc72a6d4c829a37a49938f_with_roboflow_yolo.jpg

==================================================
![97540b962de444fa87d0ee5168e9fb03_jpg.rf.1daf57f6f4cc72a6d4c829a37a49938f_with_roboflow_yolo.jpg](97540b962de444fa87d0ee5168e9fb03_jpg.rf.1daf57f6f4cc72a6d4c829a37a49938f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"NAN","题目 3":"NAN","题目 4":"NAN","题目 5":"NAN","题目 6":"NAN","题目 7":"0.27","题目 8":"NAN","题目 9":"NAN","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "0.5", "题目 3": "2/7", "题目 4": "20/9", "题目 5": "29/18", "题目 6": "3/4", "题目 7": "0.04", "题目 8": "8 14/15", "题目 9": "10/11", "题目 10": "6"}
```
### 响应时间：2.77秒
### token用量
- total_tokens: 813
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 147 张图片: 9963f1bce80c4fb09de9950967575088_jpg.rf.66c4eb8099678bd7e45bcd8835918d43_with_roboflow_yolo.jpg

==================================================
![9963f1bce80c4fb09de9950967575088_jpg.rf.66c4eb8099678bd7e45bcd8835918d43_with_roboflow_yolo.jpg](9963f1bce80c4fb09de9950967575088_jpg.rf.66c4eb8099678bd7e45bcd8835918d43_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "18.9", "题目2": "10", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 148 张图片: 9be95136439b4e54978bb87b9c7530b0_jpg.rf.118e779e48e0d4c17181b0bd710ec3f3_with_roboflow_yolo.jpg

==================================================
![9be95136439b4e54978bb87b9c7530b0_jpg.rf.118e779e48e0d4c17181b0bd710ec3f3_with_roboflow_yolo.jpg](9be95136439b4e54978bb87b9c7530b0_jpg.rf.118e779e48e0d4c17181b0bd710ec3f3_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980 kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "1/6", "题目2": "980kg", "题目3": "16本", "题目4": "45公顷"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 743
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 149 张图片: 9dc264a13f734fb89ea4c4151f4f2178_jpg.rf.0611f8f1ed3e63e234eca2e93f6b358b_with_roboflow_yolo.jpg

==================================================
![9dc264a13f734fb89ea4c4151f4f2178_jpg.rf.0611f8f1ed3e63e234eca2e93f6b358b_with_roboflow_yolo.jpg](9dc264a13f734fb89ea4c4151f4f2178_jpg.rf.0611f8f1ed3e63e234eca2e93f6b358b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980（千克）","题目 3":"16（本）","题目 4":"45（公顷）"}
```
### 响应内容：
```json
{"题目1": "1/6", "题目2": "980", "题目3": "16", "题目4": "45"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 740
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 150 张图片: 9e73953f1ab64bc0899df8a8736aafad_jpg.rf.1755b50ab2da17856a6d3d8a4d8ae434_with_roboflow_yolo.jpg

==================================================
![9e73953f1ab64bc0899df8a8736aafad_jpg.rf.1755b50ab2da17856a6d3d8a4d8ae434_with_roboflow_yolo.jpg](9e73953f1ab64bc0899df8a8736aafad_jpg.rf.1755b50ab2da17856a6d3d8a4d8ae434_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.015"}
```
### 响应时间：2.18秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 152 张图片: a02482ff4496415a8d973baf0b9133bb_jpg.rf.3fafdde23a1d4aab529076deff0b4903_with_roboflow_yolo.jpg

==================================================
![a02482ff4496415a8d973baf0b9133bb_jpg.rf.3fafdde23a1d4aab529076deff0b4903_with_roboflow_yolo.jpg](a02482ff4496415a8d973baf0b9133bb_jpg.rf.3fafdde23a1d4aab529076deff0b4903_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"NAN","题目 3":"12","题目 4":"0.225"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "NAN", "题目 3": "12", "题目 4": "0.26"}
```
### 响应时间：1.80秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 153 张图片: a167a751a2b0452bbf33b4c988b715bb_jpg.rf.11cf2d396c8ce64f3b8eeef62ddfa101_with_roboflow_yolo.jpg

==================================================
![a167a751a2b0452bbf33b4c988b715bb_jpg.rf.11cf2d396c8ce64f3b8eeef62ddfa101_with_roboflow_yolo.jpg](a167a751a2b0452bbf33b4c988b715bb_jpg.rf.11cf2d396c8ce64f3b8eeef62ddfa101_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"986.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "986.1", "题目2": "15", "题目3": "12", "题目4": "0.075"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 154 张图片: a1e4293aa6bc4e84a2ae887eb324f0b7_jpg.rf.d4629a0b593d606ff9bbf30c114110a0_with_roboflow_yolo.jpg

==================================================
![a1e4293aa6bc4e84a2ae887eb324f0b7_jpg.rf.d4629a0b593d606ff9bbf30c114110a0_with_roboflow_yolo.jpg](a1e4293aa6bc4e84a2ae887eb324f0b7_jpg.rf.d4629a0b593d606ff9bbf30c114110a0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"18/75"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "1", "题目4": "1.75", "题目5": "0.552", "题目6": "4 1/5", "题目7": "25/64", "题目8": "3/10"}
```
### 响应时间：2.69秒
### token用量
- total_tokens: 785
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 156 张图片: a3e053b9370e46b2af833b69f60c0f4c_jpg.rf.7286ae428eda173956fff4e10d9e81af_with_roboflow_yolo.jpg

==================================================
![a3e053b9370e46b2af833b69f60c0f4c_jpg.rf.7286ae428eda173956fff4e10d9e81af_with_roboflow_yolo.jpg](a3e053b9370e46b2af833b69f60c0f4c_jpg.rf.7286ae428eda173956fff4e10d9e81af_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.06", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 157 张图片: a4d7be8eedea43a195a928875c921faf_jpg.rf.7e0dc30d484ff18db9383683753be4bc_with_roboflow_yolo.jpg

==================================================
![a4d7be8eedea43a195a928875c921faf_jpg.rf.7e0dc30d484ff18db9383683753be4bc_with_roboflow_yolo.jpg](a4d7be8eedea43a195a928875c921faf_jpg.rf.7e0dc30d484ff18db9383683753be4bc_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 1/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 响应时间：3.15秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 158 张图片: a4e14ecb8fd3477f8d852215f70a4710_jpg.rf.ab87a899b9ee41f5fd07f8596b9e6d14_with_roboflow_yolo.jpg

==================================================
![a4e14ecb8fd3477f8d852215f70a4710_jpg.rf.ab87a899b9ee41f5fd07f8596b9e6d14_with_roboflow_yolo.jpg](a4e14ecb8fd3477f8d852215f70a4710_jpg.rf.ab87a899b9ee41f5fd07f8596b9e6d14_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"9.6","题目 4":"0.099"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.09"}
```
### 响应时间：1.68秒
### token用量
- total_tokens: 743
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 159 张图片: a5505f0a457a48d28ca03432d6f1b312_jpg.rf.35829f3f984674bff89e623d9d31feb5_with_roboflow_yolo.jpg

==================================================
![a5505f0a457a48d28ca03432d6f1b312_jpg.rf.35829f3f984674bff89e623d9d31feb5_with_roboflow_yolo.jpg](a5505f0a457a48d28ca03432d6f1b312_jpg.rf.35829f3f984674bff89e623d9d31feb5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.125"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.01", "题目3": "12", "题目4": "0.1875"}
```
### 响应时间：1.85秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 160 张图片: a5ad5df73ed4477a8a738ccf7b67b9a3_jpg.rf.087c4e91ec06b97ca4121631f7033f0e_with_roboflow_yolo.jpg

==================================================
![a5ad5df73ed4477a8a738ccf7b67b9a3_jpg.rf.087c4e91ec06b97ca4121631f7033f0e_with_roboflow_yolo.jpg](a5ad5df73ed4477a8a738ccf7b67b9a3_jpg.rf.087c4e91ec06b97ca4121631f7033f0e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/13","题目 2":"24/35","题目 3":"29/42","题目 4":"1 2/9","题目 5":"1 5/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1.6"}
```
### 响应内容：
```json
{"题目1": "13/13", "题目2": "3.4", "题目3": "35", "题目4": "29/42", "题目5": "1/9", "题目6": "1 5/18", "题目7": "3/4", "题目8": "0.09", "题目9": "8 14/15"}
```
### 响应时间：3.30秒
### token用量
- total_tokens: 798
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 161 张图片: a699c99e4b664a7086c018f2b0c1eb03_jpg.rf.d8292d372b2a76a81fcb0ea5edec52bf_with_roboflow_yolo.jpg

==================================================
![a699c99e4b664a7086c018f2b0c1eb03_jpg.rf.d8292d372b2a76a81fcb0ea5edec52bf_with_roboflow_yolo.jpg](a699c99e4b664a7086c018f2b0c1eb03_jpg.rf.d8292d372b2a76a81fcb0ea5edec52bf_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "1", "题目 3": "12", "题目 4": "0.019"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 162 张图片: a8206d7627804728a4fbdd3e979d9910_jpg.rf.76e7a56dad8668ee0ecd0ff554360de2_with_roboflow_yolo.jpg

==================================================
![a8206d7627804728a4fbdd3e979d9910_jpg.rf.76e7a56dad8668ee0ecd0ff554360de2_with_roboflow_yolo.jpg](a8206d7627804728a4fbdd3e979d9910_jpg.rf.76e7a56dad8668ee0ecd0ff554360de2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.097"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.01"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 743
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 163 张图片: a8b1c1480034464c857a0d00cd0443ad_jpg.rf.b50e701583b7efb07acff6d53acc8e5a_with_roboflow_yolo.jpg

==================================================
![a8b1c1480034464c857a0d00cd0443ad_jpg.rf.b50e701583b7efb07acff6d53acc8e5a_with_roboflow_yolo.jpg](a8b1c1480034464c857a0d00cd0443ad_jpg.rf.b50e701583b7efb07acff6d53acc8e5a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1 - \\frac{1}{4} - \\frac{7}{12} = \\frac{1}{6}", "题目 2": "420\\div\\frac{3}{7}=980kg", "题目 3": "24\\times\\frac{2}{3}=16本", "题目 4": "75\\times\\frac{3}{5}=45公顷"}
```
### 响应时间：2.81秒
### token用量
- total_tokens: 803
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 164 张图片: aa5a2e472510417f977bc40a05bfd3ab_jpg.rf.d2d9a62ad1a564947d92010d7dab7e64_with_roboflow_yolo.jpg

==================================================
![aa5a2e472510417f977bc40a05bfd3ab_jpg.rf.d2d9a62ad1a564947d92010d7dab7e64_with_roboflow_yolo.jpg](aa5a2e472510417f977bc40a05bfd3ab_jpg.rf.d2d9a62ad1a564947d92010d7dab7e64_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"24/42","题目 4":"1 1/3","题目 5":"1 5/18","题目 6":"6/8=3/4","题目 7":"0.0027","题目 8":"NAN","题目 9":"0","题目 10":"4/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "35.4", "题目3": "2442", "题目4": "23", "题目5": "518", "题目6": "68", "题目7": "0.00027", "题目8": "8", "题目9": "0", "题目10": "1.2"}
```
### 响应时间：3.35秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 165 张图片: aaa9c8a2cb6e43ef9dc2a3065b3bba50_jpg.rf.90d74383af1c59e7624ee528c889c926_with_roboflow_yolo.jpg

==================================================
![aaa9c8a2cb6e43ef9dc2a3065b3bba50_jpg.rf.90d74383af1c59e7624ee528c889c926_with_roboflow_yolo.jpg](aaa9c8a2cb6e43ef9dc2a3065b3bba50_jpg.rf.90d74383af1c59e7624ee528c889c926_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"35","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "1"}
```
### 响应时间：3.06秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 167 张图片: abc83c1366e34a87807e9307071b9e53_jpg.rf.f371a50ea95a6b73fe7367929b06eab0_with_roboflow_yolo.jpg

==================================================
![abc83c1366e34a87807e9307071b9e53_jpg.rf.f371a50ea95a6b73fe7367929b06eab0_with_roboflow_yolo.jpg](abc83c1366e34a87807e9307071b9e53_jpg.rf.f371a50ea95a6b73fe7367929b06eab0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0915"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：2.10秒
### token用量
- total_tokens: 745
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 168 张图片: abd4e800014f4c3f95885aff7b7c2d26_jpg.rf.6c747a2e88ef05fdbb2e699608b0d929_with_roboflow_yolo.jpg

==================================================
![abd4e800014f4c3f95885aff7b7c2d26_jpg.rf.6c747a2e88ef05fdbb2e699608b0d929_with_roboflow_yolo.jpg](abd4e800014f4c3f95885aff7b7c2d26_jpg.rf.6c747a2e88ef05fdbb2e699608b0d929_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.1225"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.125"}
```
### 响应时间：1.56秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 169 张图片: ac398a81ac4e4eb6b464eda2e7e7b9db_jpg.rf.9c431132889a5527a37bb5d82b19d3a0_with_roboflow_yolo.jpg

==================================================
![ac398a81ac4e4eb6b464eda2e7e7b9db_jpg.rf.9c431132889a5527a37bb5d82b19d3a0_with_roboflow_yolo.jpg](ac398a81ac4e4eb6b464eda2e7e7b9db_jpg.rf.9c431132889a5527a37bb5d82b19d3a0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "50", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "NAN", "题目9": "3000", "题目10": "1.2"}
```
### 响应时间：2.82秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 170 张图片: ac5eb24b0f0e4dd4bad2c05fc47bc334_jpg.rf.44b26a075fff06b7d9c5c91e148df229_with_roboflow_yolo.jpg

==================================================
![ac5eb24b0f0e4dd4bad2c05fc47bc334_jpg.rf.44b26a075fff06b7d9c5c91e148df229_with_roboflow_yolo.jpg](ac5eb24b0f0e4dd4bad2c05fc47bc334_jpg.rf.44b26a075fff06b7d9c5c91e148df229_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"NAN","题目 8":"NAN"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4/25", "题目 7": "25/64", "题目 8": "18/25"}
```
### 响应时间：2.92秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 171 张图片: accbb2f5b4aa4dcfa659e97865c57650_jpg.rf.cb011cb04d24a45ba1a2a285e0406a79_with_roboflow_yolo.jpg

==================================================
![accbb2f5b4aa4dcfa659e97865c57650_jpg.rf.cb011cb04d24a45ba1a2a285e0406a79_with_roboflow_yolo.jpg](accbb2f5b4aa4dcfa659e97865c57650_jpg.rf.cb011cb04d24a45ba1a2a285e0406a79_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"5/35","题目 3":"429/42","题目 4":"5/3","题目 5":"5/18","题目 6":"NAN","题目 7":"0.9","题目 8":"0.53","题目 9":"8/11","题目 10":"3/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "5", "题目3": "42"}
```
### 响应时间：2.03秒
### token用量
- total_tokens: 728
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 172 张图片: ad04e7f29b54400abb1a8187bfffcfef_jpg.rf.a43c67394c813dc41daadc6d18a32b6e_with_roboflow_yolo.jpg

==================================================
![ad04e7f29b54400abb1a8187bfffcfef_jpg.rf.a43c67394c813dc41daadc6d18a32b6e_with_roboflow_yolo.jpg](ad04e7f29b54400abb1a8187bfffcfef_jpg.rf.a43c67394c813dc41daadc6d18a32b6e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"31/35","题目 3":"29/42","题目 4":"11/9","题目 5":"23/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 4/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "0.5", "题目3": "1/6", "题目4": "2/3", "题目5": "1/18", "题目6": "3/4", "题目7": "0.027", "题目8": "26/3", "题目9": "8/11"}
```
### 响应时间：3.32秒
### token用量
- total_tokens: 790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 174 张图片: adf68e3a57c54d41ad9b8f84ff32a1dc_jpg.rf.6ca618dcaccfc4e1d3475ed2f40649cd_with_roboflow_yolo.jpg

==================================================
![adf68e3a57c54d41ad9b8f84ff32a1dc_jpg.rf.6ca618dcaccfc4e1d3475ed2f40649cd_with_roboflow_yolo.jpg](adf68e3a57c54d41ad9b8f84ff32a1dc_jpg.rf.6ca618dcaccfc4e1d3475ed2f40649cd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"1/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "1", "题目4": "1.75", "题目5": "0.552", "题目6": "4 1/5", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.80秒
### token用量
- total_tokens: 785
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 175 张图片: ae73f4cb4bbf4b4789688153af9ecc1f_jpg.rf.d8441db4e1c8d1f43b24246e47d19242_with_roboflow_yolo.jpg

==================================================
![ae73f4cb4bbf4b4789688153af9ecc1f_jpg.rf.d8441db4e1c8d1f43b24246e47d19242_with_roboflow_yolo.jpg](ae73f4cb4bbf4b4789688153af9ecc1f_jpg.rf.d8441db4e1c8d1f43b24246e47d19242_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"NAN","题目 4":"NAN","题目 5":"5/8","题目 6":"NAN","题目 7":"NAN","题目 8":"NAN","题目 9":"1","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "NAN", "题目 7": "NAN", "题目 8": "NAN", "题目 9": "NAN", "题目 10": "NAN"}
```
### 响应时间：2.39秒
### token用量
- total_tokens: 797
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 176 张图片: aef00fd83be34f4f90a30df7698bfab2_jpg.rf.e7620327696df89b68aed1b391590c1d_with_roboflow_yolo.jpg

==================================================
![aef00fd83be34f4f90a30df7698bfab2_jpg.rf.e7620327696df89b68aed1b391590c1d_with_roboflow_yolo.jpg](aef00fd83be34f4f90a30df7698bfab2_jpg.rf.e7620327696df89b68aed1b391590c1d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"1 1/3","题目 5":"1 5/8","题目 6":"3/4","题目 7":"0.0027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "1/15", "题目 5": "5/8", "题目 6": "3/4", "题目 7": "0.008", "题目 8": "8/15", "题目 9": "2/11"}
```
### 响应时间：2.61秒
### token用量
- total_tokens: 803
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 177 张图片: b0c29e41096645d9a7b15cc423fef656_jpg.rf.cb420779ba313fec68348f9c5ff8637e_with_roboflow_yolo.jpg

==================================================
![b0c29e41096645d9a7b15cc423fef656_jpg.rf.cb420779ba313fec68348f9c5ff8637e_with_roboflow_yolo.jpg](b0c29e41096645d9a7b15cc423fef656_jpg.rf.cb420779ba313fec68348f9c5ff8637e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"1100千克","题目 3":"16本","题目 4":"25公顷"}
```
### 响应内容：
```json
{"题目 1": "1-(1/4+7/12)=1/6", "题目 2": "420÷3=140，4×140=560", "题目 3": "24÷3=8，2×8=16", "题目 4": "75×1/3=25"}
```
### 响应时间：2.72秒
### token用量
- total_tokens: 789
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 179 张图片: b1a42e27088f41ed93db6142c4164995_jpg.rf.5e73a1ae3e55446d75238da58810065e_with_roboflow_yolo.jpg

==================================================
![b1a42e27088f41ed93db6142c4164995_jpg.rf.5e73a1ae3e55446d75238da58810065e_with_roboflow_yolo.jpg](b1a42e27088f41ed93db6142c4164995_jpg.rf.5e73a1ae3e55446d75238da58810065e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2/12","题目 2":"240kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "2/12", "题目 2": "240kg", "题目 3": "8", "题目 4": "45公顷"}
```
### 响应时间：1.97秒
### token用量
- total_tokens: 746
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 180 张图片: b29b0a3526e543a7bc3da3cc80f296db_jpg.rf.9262ad9336cecc049d4f1643175b1a8a_with_roboflow_yolo.jpg

==================================================
![b29b0a3526e543a7bc3da3cc80f296db_jpg.rf.9262ad9336cecc049d4f1643175b1a8a_with_roboflow_yolo.jpg](b29b0a3526e543a7bc3da3cc80f296db_jpg.rf.9262ad9336cecc049d4f1643175b1a8a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980(kg)","题目 3":"16(本)","题目 4":"45(公顷)"}
```
### 响应内容：
```json
{"题目1": "1/6", "题目2": "980", "题目3": "16", "题目4": "45"}
```
### 响应时间：1.48秒
### token用量
- total_tokens: 740
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 181 张图片: b63cac27107f47c2b5b40bc3a9cdb05e_jpg.rf.8b57b54c2a6b2d5fbc5b074dd2d7bd36_with_roboflow_yolo.jpg

==================================================
![b63cac27107f47c2b5b40bc3a9cdb05e_jpg.rf.8b57b54c2a6b2d5fbc5b074dd2d7bd36_with_roboflow_yolo.jpg](b63cac27107f47c2b5b40bc3a9cdb05e_jpg.rf.8b57b54c2a6b2d5fbc5b074dd2d7bd36_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"1 2/9","题目 5":"1 5/18","题目 6":"6/8","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"1.6"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "17/25", "题目3": "19/12", "题目4": "26/27", "题目5": "5/18", "题目6": "0", "题目7": "0.27", "题目8": "14/15", "题目9": "8/11", "题目10": "1.2"}
```
### 响应时间：3.59秒
### token用量
- total_tokens: 804
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 182 张图片: b67db8be9d2746349d44c650673295f2_jpg.rf.4a7b5ccfdeef2bcd0f648aaa522db3ee_with_roboflow_yolo.jpg

==================================================
![b67db8be9d2746349d44c650673295f2_jpg.rf.4a7b5ccfdeef2bcd0f648aaa522db3ee_with_roboflow_yolo.jpg](b67db8be9d2746349d44c650673295f2_jpg.rf.4a7b5ccfdeef2bcd0f648aaa522db3ee_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2/12","题目 2":"980kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "5/12", "题目2": "980", "题目3": "16", "题目4": "45"}
```
### 响应时间：1.89秒
### token用量
- total_tokens: 741
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 183 张图片: b839643fef9f432f92c1bae1ad517daf_jpg.rf.5040ad25cfa39e8936556799846267b6_with_roboflow_yolo.jpg

==================================================
![b839643fef9f432f92c1bae1ad517daf_jpg.rf.5040ad25cfa39e8936556799846267b6_with_roboflow_yolo.jpg](b839643fef9f432f92c1bae1ad517daf_jpg.rf.5040ad25cfa39e8936556799846267b6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"10","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "181.1", "题目2": "0", "题目3": "12", "题目4": "0.0075"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 184 张图片: b8fca052cb9a414ea3e293e8a1e045b0_jpg.rf.3396952bf32b0443e9f1357d66f58fcc_with_roboflow_yolo.jpg

==================================================
![b8fca052cb9a414ea3e293e8a1e045b0_jpg.rf.3396952bf32b0443e9f1357d66f58fcc_with_roboflow_yolo.jpg](b8fca052cb9a414ea3e293e8a1e045b0_jpg.rf.3396952bf32b0443e9f1357d66f58fcc_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 185 张图片: ba7da4d29fcb456e8ef47d245fba5212_jpg.rf.18e5213c393b19d396e892239aef6e7c_with_roboflow_yolo.jpg

==================================================
![ba7da4d29fcb456e8ef47d245fba5212_jpg.rf.18e5213c393b19d396e892239aef6e7c_with_roboflow_yolo.jpg](ba7da4d29fcb456e8ef47d245fba5212_jpg.rf.18e5213c393b19d396e892239aef6e7c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"0.875","题目 4":"1.66","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "0.875", "题目4": "1.75", "题目5": "0.552", "题目6": "4 1/5", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.67秒
### token用量
- total_tokens: 789
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 186 张图片: bade1e40f9e942ec9cae0be9fd549761_jpg.rf.43392c9e872d59f6e8e583ff1d8dd0ea_with_roboflow_yolo.jpg

==================================================
![bade1e40f9e942ec9cae0be9fd549761_jpg.rf.43392c9e872d59f6e8e583ff1d8dd0ea_with_roboflow_yolo.jpg](bade1e40f9e942ec9cae0be9fd549761_jpg.rf.43392c9e872d59f6e8e583ff1d8dd0ea_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"10","题目 4":"1.3375"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "10", "题目 4": "1.75"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 187 张图片: baf98393832849799c72f027febdfc97_jpg.rf.2235fa0c664aac9d39b39955f48709dc_with_roboflow_yolo.jpg

==================================================
![baf98393832849799c72f027febdfc97_jpg.rf.2235fa0c664aac9d39b39955f48709dc_with_roboflow_yolo.jpg](baf98393832849799c72f027febdfc97_jpg.rf.2235fa0c664aac9d39b39955f48709dc_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.018"}
```
### 响应时间：2.12秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 188 张图片: bb1499178fb946a98b42df15d9968e90_jpg.rf.4097297448dafd2917f22698d7bd188c_with_roboflow_yolo.jpg

==================================================
![bb1499178fb946a98b42df15d9968e90_jpg.rf.4097297448dafd2917f22698d7bd188c_with_roboflow_yolo.jpg](bb1499178fb946a98b42df15d9968e90_jpg.rf.4097297448dafd2917f22698d7bd188c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/16","题目 2":"240千克","题目 3":"16本","题目 4":"30公顷"}
```
### 响应内容：
```json
{"题目 1": "7/16", "题目 2": "240", "题目 3": "16", "题目 4": "30"}
```
### 响应时间：2.06秒
### token用量
- total_tokens: 745
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 190 张图片: bfaa7468985843948674d79fd49ddd21_jpg.rf.d5bacd5faaec55c746f778103404e464_with_roboflow_yolo.jpg

==================================================
![bfaa7468985843948674d79fd49ddd21_jpg.rf.d5bacd5faaec55c746f778103404e464_with_roboflow_yolo.jpg](bfaa7468985843948674d79fd49ddd21_jpg.rf.d5bacd5faaec55c746f778103404e464_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0925"}
```
### 响应内容：
```json
{"题目1": "981.03", "题目2": "0.01", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 191 张图片: c192e57c3d8145bab01ef584adec6d4d_jpg.rf.88fbda76d2ce1de5d175665a6e1f5596_with_roboflow_yolo.jpg

==================================================
![c192e57c3d8145bab01ef584adec6d4d_jpg.rf.88fbda76d2ce1de5d175665a6e1f5596_with_roboflow_yolo.jpg](c192e57c3d8145bab01ef584adec6d4d_jpg.rf.88fbda76d2ce1de5d175665a6e1f5596_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"1","题目 3":"12","题目 4":"0.091"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.09"}
```
### 响应时间：1.59秒
### token用量
- total_tokens: 749
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 192 张图片: c26b4c0c14ff412193da720ed99dad55_jpg.rf.c4685490c7b1a8ee6f2755a1b79c176a_with_roboflow_yolo.jpg

==================================================
![c26b4c0c14ff412193da720ed99dad55_jpg.rf.c4685490c7b1a8ee6f2755a1b79c176a_with_roboflow_yolo.jpg](c26b4c0c14ff412193da720ed99dad55_jpg.rf.c4685490c7b1a8ee6f2755a1b79c176a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "180", "题目8": "3", "题目9": "3000", "题目10": "2"}
```
### 响应时间：2.41秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 193 张图片: c38c3229eb5d4694a7e981f0dcb4e68d_jpg.rf.7f0a58763ed827615c04e130871b46c6_with_roboflow_yolo.jpg

==================================================
![c38c3229eb5d4694a7e981f0dcb4e68d_jpg.rf.7f0a58763ed827615c04e130871b46c6_with_roboflow_yolo.jpg](c38c3229eb5d4694a7e981f0dcb4e68d_jpg.rf.7f0a58763ed827615c04e130871b46c6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16本","题目 4":"45"}
```
### 响应内容：
```json
{"题目 1": "1 - (1/4 + 7/12) = 1/6\n10/12", "题目 2": "420÷3/7 = 980kg", "题目 3": "24÷3×2 = 16本", "题目 4": "75÷5×3 = 45\n15"}
```
### 响应时间：3.31秒
### token用量
- total_tokens: 795
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 195 张图片: c87f0552f77c4ec782f08a0a2159f198_jpg.rf.5ad072a5cdaf3944cc7849e75170b5ad_with_roboflow_yolo.jpg

==================================================
![c87f0552f77c4ec782f08a0a2159f198_jpg.rf.5ad072a5cdaf3944cc7849e75170b5ad_with_roboflow_yolo.jpg](c87f0552f77c4ec782f08a0a2159f198_jpg.rf.5ad072a5cdaf3944cc7849e75170b5ad_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.01", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：1.68秒
### token用量
- total_tokens: 751
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 196 张图片: c9a3d1414682402ba2c5b354c37bfc0a_jpg.rf.dc0ed7e996ae6fbd27bbb35f78086605_with_roboflow_yolo.jpg

==================================================
![c9a3d1414682402ba2c5b354c37bfc0a_jpg.rf.dc0ed7e996ae6fbd27bbb35f78086605_with_roboflow_yolo.jpg](c9a3d1414682402ba2c5b354c37bfc0a_jpg.rf.dc0ed7e996ae6fbd27bbb35f78086605_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"18/75"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/28", "题目3": "1", "题目4": "7/8", "题目5": "1.75", "题目6": "0.552", "题目7": "1/5", "题目8": "25/64", "题目9": "6/25"}
```
### 响应时间：2.91秒
### token用量
- total_tokens: 793
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 197 张图片: ccd12c0cfac24c6d9a99e14c380b7ee8_jpg.rf.cfac3219550f3be39bb15c4fa2377f81_with_roboflow_yolo.jpg

==================================================
![ccd12c0cfac24c6d9a99e14c380b7ee8_jpg.rf.cfac3219550f3be39bb15c4fa2377f81_with_roboflow_yolo.jpg](ccd12c0cfac24c6d9a99e14c380b7ee8_jpg.rf.cfac3219550f3be39bb15c4fa2377f81_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "1/6", "题目2": "980", "题目3": "16", "题目4": "45"}
```
### 响应时间：2.43秒
### token用量
- total_tokens: 740
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 198 张图片: cde02c7f38914237a9ad1e38f9304c24_jpg.rf.14160bf4a69477d8ca192dcf46cd7102_with_roboflow_yolo.jpg

==================================================
![cde02c7f38914237a9ad1e38f9304c24_jpg.rf.14160bf4a69477d8ca192dcf46cd7102_with_roboflow_yolo.jpg](cde02c7f38914237a9ad1e38f9304c24_jpg.rf.14160bf4a69477d8ca192dcf46cd7102_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"1","题目 3":"10","题目 4":"2"}
```
### 响应内容：
```json
{"题目1": "81", "题目2": "1", "题目3": "10", "题目4": "2"}
```
### 响应时间：1.59秒
### token用量
- total_tokens: 736
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 199 张图片: cf3db06ab57e4d75acbb2d44116c9190_jpg.rf.62fadd3768973410214838d3176021e6_with_roboflow_yolo.jpg

==================================================
![cf3db06ab57e4d75acbb2d44116c9190_jpg.rf.62fadd3768973410214838d3176021e6_with_roboflow_yolo.jpg](cf3db06ab57e4d75acbb2d44116c9190_jpg.rf.62fadd3768973410214838d3176021e6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"NAN","题目 3":"141/42","题目 4":"NAN","题目 5":"1 1/18","题目 6":"3/8","题目 7":"0.9","题目 8":"NAN","题目 9":"0","题目 10":"3/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "3/2", "题目3": "1/12", "题目4": "17/9", "题目5": "1/11", "题目6": "1/4", "题目7": "0.027", "题目8": "8+14/15", "题目9": "1", "题目10": "1/2"}
```
### 响应时间：3.72秒
### token用量
- total_tokens: 802
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 200 张图片: cf82a17c00e347d5807d2ee1cad57f92_jpg.rf.f4336ecd2bbe4ab5f24f6b37a407d669_with_roboflow_yolo.jpg

==================================================
![cf82a17c00e347d5807d2ee1cad57f92_jpg.rf.f4336ecd2bbe4ab5f24f6b37a407d669_with_roboflow_yolo.jpg](cf82a17c00e347d5807d2ee1cad57f92_jpg.rf.f4336ecd2bbe4ab5f24f6b37a407d669_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"0.875","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3", "题目2": "5", "题目3": "0.875", "题目4": "1.75", "题目5": "0.552", "题目6": "4\\frac{4}{5}", "题目7": "\\frac{25}{34}", "题目8": "\\frac{6}{25}"}
```
### 响应时间：2.71秒
### token用量
- total_tokens: 791
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 201 张图片: d10de923f1a24802ae094d517e438031_jpg.rf.f87c5b6665b413593d51be1606bb6750_with_roboflow_yolo.jpg

==================================================
![d10de923f1a24802ae094d517e438031_jpg.rf.f87c5b6665b413593d51be1606bb6750_with_roboflow_yolo.jpg](d10de923f1a24802ae094d517e438031_jpg.rf.f87c5b6665b413593d51be1606bb6750_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"NAN","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "7"}
```
### 响应时间：2.69秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 202 张图片: d14c4dbfb5bc40629168fcc5a09cd436_jpg.rf.0b00697f3ce5f9a927fa3c3d2dbcc572_with_roboflow_yolo.jpg

==================================================
![d14c4dbfb5bc40629168fcc5a09cd436_jpg.rf.0b00697f3ce5f9a927fa3c3d2dbcc572_with_roboflow_yolo.jpg](d14c4dbfb5bc40629168fcc5a09cd436_jpg.rf.0b00697f3ce5f9a927fa3c3d2dbcc572_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0.01", "题目3": "12", "题目4": "0.0525"}
```
### 响应时间：2.93秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 203 张图片: d418b1d2bd0446e4bc9112449e7664ab_jpg.rf.533dbd7e5cc190a57b198c09c4e0153b_with_roboflow_yolo.jpg

==================================================
![d418b1d2bd0446e4bc9112449e7664ab_jpg.rf.533dbd7e5cc190a57b198c09c4e0153b_with_roboflow_yolo.jpg](d418b1d2bd0446e4bc9112449e7664ab_jpg.rf.533dbd7e5cc190a57b198c09c4e0153b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "3", "题目9": "3000", "题目10": "7"}
```
### 响应时间：2.86秒
### token用量
- total_tokens: 790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 204 张图片: d4544e69005e4238bf84931cb24d86b9_jpg.rf.bb8ebe1e19ccb17a2e61c71268ec59e2_with_roboflow_yolo.jpg

==================================================
![d4544e69005e4238bf84931cb24d86b9_jpg.rf.bb8ebe1e19ccb17a2e61c71268ec59e2_with_roboflow_yolo.jpg](d4544e69005e4238bf84931cb24d86b9_jpg.rf.bb8ebe1e19ccb17a2e61c71268ec59e2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.097"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.09"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 205 张图片: d6c39fd02c1f43b5a599dde38f1c0d89_jpg.rf.58a9b351674b08449b7f9d3d2652572b_with_roboflow_yolo.jpg

==================================================
![d6c39fd02c1f43b5a599dde38f1c0d89_jpg.rf.58a9b351674b08449b7f9d3d2652572b_with_roboflow_yolo.jpg](d6c39fd02c1f43b5a599dde38f1c0d89_jpg.rf.58a9b351674b08449b7f9d3d2652572b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"140kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "1 - \\frac{1}{4} - \\frac{7}{12} = \\frac{1}{6}", "题目2": "420÷\\frac{3}{7}=980", "题目3": "24×\\frac{2}{3}=16", "题目4": "75×\\frac{3}{5}=45"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 793
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 206 张图片: d90c9181b8f34f1d870dfc63d7f1f02f_jpg.rf.a0e4db268f7417d8ef7e888dc5ad1862_with_roboflow_yolo.jpg

==================================================
![d90c9181b8f34f1d870dfc63d7f1f02f_jpg.rf.a0e4db268f7417d8ef7e888dc5ad1862_with_roboflow_yolo.jpg](d90c9181b8f34f1d870dfc63d7f1f02f_jpg.rf.a0e4db268f7417d8ef7e888dc5ad1862_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "7/8", "题目4": "1.75", "题目5": "0.552", "题目6": "4 1/5", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：3.20秒
### token用量
- total_tokens: 787
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 207 张图片: dd490a80cae6434dba22cc3ac425d02b_jpg.rf.2f18775da71d20b1d11c6eb9fc6690df_with_roboflow_yolo.jpg

==================================================
![dd490a80cae6434dba22cc3ac425d02b_jpg.rf.2f18775da71d20b1d11c6eb9fc6690df_with_roboflow_yolo.jpg](dd490a80cae6434dba22cc3ac425d02b_jpg.rf.2f18775da71d20b1d11c6eb9fc6690df_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "5/7", "题目9": "3000", "题目10": "7/9"}
```
### 响应时间：2.62秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 208 张图片: ddefcd1b40cb4593a52d4bea8781dff2_jpg.rf.3bac30c7f91d076ead698a656b72aef3_with_roboflow_yolo.jpg

==================================================
![ddefcd1b40cb4593a52d4bea8781dff2_jpg.rf.3bac30c7f91d076ead698a656b72aef3_with_roboflow_yolo.jpg](ddefcd1b40cb4593a52d4bea8781dff2_jpg.rf.3bac30c7f91d076ead698a656b72aef3_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "0", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：3.53秒
### token用量
- total_tokens: 744
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 209 张图片: e00670ac00b4430abc2bd3d7a6e5fc85_jpg.rf.f3f60a9b747ba267f18822e20776a3e1_with_roboflow_yolo.jpg

==================================================
![e00670ac00b4430abc2bd3d7a6e5fc85_jpg.rf.f3f60a9b747ba267f18822e20776a3e1_with_roboflow_yolo.jpg](e00670ac00b4430abc2bd3d7a6e5fc85_jpg.rf.f3f60a9b747ba267f18822e20776a3e1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"NAN","题目 4":"1.75","题目 5":"0.552","题目 6":"4 4/5","题目 7":"NAN","题目 8":"3/2"}
```
### 响应内容：
```json
{"题目1": "3/11", "题目2": "5/7", "题目3": "NAN", "题目4": "1.75", "题目5": "0.552", "题目6": "4/5", "题目7": "NAN", "题目8": "3/2"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 780
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 212 张图片: e2f1e7ae919b42c5ad2b232fd40759ca_jpg.rf.fe005e84cd3747125181655f9b902e0e_with_roboflow_yolo.jpg

==================================================
![e2f1e7ae919b42c5ad2b232fd40759ca_jpg.rf.fe005e84cd3747125181655f9b902e0e_with_roboflow_yolo.jpg](e2f1e7ae919b42c5ad2b232fd40759ca_jpg.rf.fe005e84cd3747125181655f9b902e0e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "80", "题目8": "3000", "题目9": "7"}
```
### 响应时间：2.98秒
### token用量
- total_tokens: 783
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 213 张图片: e2f6f3922d734fdfab4c614243ff4871_jpg.rf.565e4d11b11465376e2dddac1e5ceaab_with_roboflow_yolo.jpg

==================================================
![e2f6f3922d734fdfab4c614243ff4871_jpg.rf.565e4d11b11465376e2dddac1e5ceaab_with_roboflow_yolo.jpg](e2f6f3922d734fdfab4c614243ff4871_jpg.rf.565e4d11b11465376e2dddac1e5ceaab_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"481.1","题目 2":"10","题目 3":"12","题目 4":"0.09"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.1", "题目 3": "12", "题目 4": "0.09"}
```
### 响应时间：2.41秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 214 张图片: e6761829d30e4f328f4a2a2733f86613_jpg.rf.91b97d45bb4fb0ff69952e743182a2d0_with_roboflow_yolo.jpg

==================================================
![e6761829d30e4f328f4a2a2733f86613_jpg.rf.91b97d45bb4fb0ff69952e743182a2d0_with_roboflow_yolo.jpg](e6761829d30e4f328f4a2a2733f86613_jpg.rf.91b97d45bb4fb0ff69952e743182a2d0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"1/62","题目 4":"1.85","题目 5":"5.64","题目 6":"4 4/5","题目 7":"25/64","题目 8":"18/75"}
```
### 响应内容：
```json
{"题目1": "3", "题目2": "2", "题目3": "0.9", "题目4": "1.85", "题目5": "0.552", "题目6": "4.8", "题目7": "25/64", "题目8": "18/25"}
```
### 响应时间：2.55秒
### token用量
- total_tokens: 781
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 215 张图片: e8eda7de49864852908e47463a1d27af_jpg.rf.ec45a22bacc0741b67bbe69ea71c1bda_with_roboflow_yolo.jpg

==================================================
![e8eda7de49864852908e47463a1d27af_jpg.rf.ec45a22bacc0741b67bbe69ea71c1bda_with_roboflow_yolo.jpg](e8eda7de49864852908e47463a1d27af_jpg.rf.ec45a22bacc0741b67bbe69ea71c1bda_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"17/35","题目 3":"29/42","题目 4":"11/9","题目 5":"1 5/18","题目 6":"3/4","题目 7":"0.027","题目 8":"8 14/15","题目 9":"8/11","题目 10":"8/5"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "1", "题目3": "1/12", "题目4": "5/9", "题目5": "5/12", "题目6": "3/4", "题目7": "0.09", "题目8": "8 4/5", "题目9": "8/11", "题目10": "2.2"}
```
### 响应时间：3.49秒
### token用量
- total_tokens: 799
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 216 张图片: e935475e409a4511b683e8a02fa9798a_jpg.rf.b62c5fdb5907ffc98179a344ee377cea_with_roboflow_yolo.jpg

==================================================
![e935475e409a4511b683e8a02fa9798a_jpg.rf.b62c5fdb5907ffc98179a344ee377cea_with_roboflow_yolo.jpg](e935475e409a4511b683e8a02fa9798a_jpg.rf.b62c5fdb5907ffc98179a344ee377cea_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "0.1", "题目 3": "12", "题目 4": "0.0975"}
```
### 响应时间：1.43秒
### token用量
- total_tokens: 750
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 218 张图片: e9d3197bb9cc4b208783b8d160b2fe45_jpg.rf.575040173f5b3ec68c6cf2a9f2beda41_with_roboflow_yolo.jpg

==================================================
![e9d3197bb9cc4b208783b8d160b2fe45_jpg.rf.575040173f5b3ec68c6cf2a9f2beda41_with_roboflow_yolo.jpg](e9d3197bb9cc4b208783b8d160b2fe45_jpg.rf.575040173f5b3ec68c6cf2a9f2beda41_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.097"}
```
### 响应内容：
```json
{"题目1": "981.1", "题目2": "10", "题目3": "12", "题目4": "0.09"}
```
### 响应时间：1.82秒
### token用量
- total_tokens: 743
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 219 张图片: e9e445305d5d4e20b811e91a1f9c4519_jpg.rf.9febdf7c69d6d629b77ff917924362b4_with_roboflow_yolo.jpg

==================================================
![e9e445305d5d4e20b811e91a1f9c4519_jpg.rf.9febdf7c69d6d629b77ff917924362b4_with_roboflow_yolo.jpg](e9e445305d5d4e20b811e91a1f9c4519_jpg.rf.9febdf7c69d6d629b77ff917924362b4_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.075"}
```
### 响应时间：1.92秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 220 张图片: e9feb8c0d62f44d3b8b3da84d13e9206_jpg.rf.cd486891ae01d5366f26ca3db95424b8_with_roboflow_yolo.jpg

==================================================
![e9feb8c0d62f44d3b8b3da84d13e9206_jpg.rf.cd486891ae01d5366f26ca3db95424b8_with_roboflow_yolo.jpg](e9feb8c0d62f44d3b8b3da84d13e9206_jpg.rf.cd486891ae01d5366f26ca3db95424b8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0975"}
```
### 响应内容：
```json
{"题目1": "181", "题目2": "1", "题目3": "12", "题目4": "0.0975"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 742
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 221 张图片: ebc687553ab84bae89fc58e6e1bbf0fb_jpg.rf.b2d2815fc1c3decf43ff1b74d0cc5cd0_with_roboflow_yolo.jpg

==================================================
![ebc687553ab84bae89fc58e6e1bbf0fb_jpg.rf.b2d2815fc1c3decf43ff1b74d0cc5cd0_with_roboflow_yolo.jpg](ebc687553ab84bae89fc58e6e1bbf0fb_jpg.rf.b2d2815fc1c3decf43ff1b74d0cc5cd0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "6", "题目2": "980kg", "题目3": "16本", "题目4": "45公顷"}
```
### 响应时间：1.89秒
### token用量
- total_tokens: 741
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 222 张图片: ee21276da8b6457897865974d8613a92_jpg.rf.387192ad66c8497e091cd474a3e630b0_with_roboflow_yolo.jpg

==================================================
![ee21276da8b6457897865974d8613a92_jpg.rf.387192ad66c8497e091cd474a3e630b0_with_roboflow_yolo.jpg](ee21276da8b6457897865974d8613a92_jpg.rf.387192ad66c8497e091cd474a3e630b0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"85","题目 6":"0.5","题目 7":"NAN","题目 8":"NAN","题目 9":"4000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "30", "题目 5": "95", "题目 6": "0.25", "题目 7": "780", "题目 8": "3", "题目 9": "3000", "题目 10": "2.5"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 805
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 223 张图片: eef978599ed24584a3617e2ad7524664_jpg.rf.b6f4fe90940232160458e01a6e14cad5_with_roboflow_yolo.jpg

==================================================
![eef978599ed24584a3617e2ad7524664_jpg.rf.b6f4fe90940232160458e01a6e14cad5_with_roboflow_yolo.jpg](eef978599ed24584a3617e2ad7524664_jpg.rf.b6f4fe90940232160458e01a6e14cad5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980 kg","题目 3":"16本","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "980kg", "题目 3": "16本", "题目 4": "45公顷"}
```
### 响应时间：2.22秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 224 张图片: ef9d2d23349c4856bbede25d99a5ee8a_jpg.rf.9abe6c5a0f497d77c289566c9fe11f76_with_roboflow_yolo.jpg

==================================================
![ef9d2d23349c4856bbede25d99a5ee8a_jpg.rf.9abe6c5a0f497d77c289566c9fe11f76_with_roboflow_yolo.jpg](ef9d2d23349c4856bbede25d99a5ee8a_jpg.rf.9abe6c5a0f497d77c289566c9fe11f76_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2/12","题目 2":"760","题目 3":"16","题目 4":"45公顷"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "760", "题目3": "16", "题目4": "45"}
```
### 响应时间：1.80秒
### token用量
- total_tokens: 739
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 225 张图片: efa31758a21e4c0587d13ff854e75107_jpg.rf.70699457aabc62afee68d18fe2a57bb2_with_roboflow_yolo.jpg

==================================================
![efa31758a21e4c0587d13ff854e75107_jpg.rf.70699457aabc62afee68d18fe2a57bb2_with_roboflow_yolo.jpg](efa31758a21e4c0587d13ff854e75107_jpg.rf.70699457aabc62afee68d18fe2a57bb2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"26/27","题目 2":"370(kg)","题目 3":"4(本)","题目 4":"23(公顷)"}
```
### 响应内容：
```json
{"题目1": "1-(\\frac{1}{4}+\\frac{7}{12})=\\frac{1}{6}", "题目2": "420\\div(1 - \\frac{3}{7})=735(kg)", "题目3": "24\\div\\frac{3}{5}=40(本)", "题目4": "75\\div3 = 25(公顷)"}
```
### 响应时间：2.63秒
### token用量
- total_tokens: 802
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 226 张图片: f31c24530b61441faf634675ef9eaa32_jpg.rf.40bc5fcda02ea0735310ae2b9353a7d4_with_roboflow_yolo.jpg

==================================================
![f31c24530b61441faf634675ef9eaa32_jpg.rf.40bc5fcda02ea0735310ae2b9353a7d4_with_roboflow_yolo.jpg](f31c24530b61441faf634675ef9eaa32_jpg.rf.40bc5fcda02ea0735310ae2b9353a7d4_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"NAN","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.2", "题目7": "180", "题目8": "3", "题目9": "3000", "题目10": "1"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 227 张图片: f53b65a196c94f96ac99952e3c536554_jpg.rf.d15e84378699acc197fe03a45531a110_with_roboflow_yolo.jpg

==================================================
![f53b65a196c94f96ac99952e3c536554_jpg.rf.d15e84378699acc197fe03a45531a110_with_roboflow_yolo.jpg](f53b65a196c94f96ac99952e3c536554_jpg.rf.d15e84378699acc197fe03a45531a110_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "78", "题目8": "3", "题目9": "3000", "题目10": "1.8"}
```
### 响应时间：3.09秒
### token用量
- total_tokens: 791
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 228 张图片: f56984f2b57143748bf8615e1fe5dbd2_jpg.rf.6826f3d4ae0489ac6303f34d84d813e8_with_roboflow_yolo.jpg

==================================================
![f56984f2b57143748bf8615e1fe5dbd2_jpg.rf.6826f3d4ae0489ac6303f34d84d813e8_with_roboflow_yolo.jpg](f56984f2b57143748bf8615e1fe5dbd2_jpg.rf.6826f3d4ae0489ac6303f34d84d813e8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "30", "题目5": "95", "题目6": "0.5", "题目7": "80", "题目8": "52", "题目9": "3000", "题目10": "7"}
```
### 响应时间：2.43秒
### token用量
- total_tokens: 790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 229 张图片: f5bc9504654f43b88672c9a4b6a23f2e_jpg.rf.342b47666d01807493f68177c60507ca_with_roboflow_yolo.jpg

==================================================
![f5bc9504654f43b88672c9a4b6a23f2e_jpg.rf.342b47666d01807493f68177c60507ca_with_roboflow_yolo.jpg](f5bc9504654f43b88672c9a4b6a23f2e_jpg.rf.342b47666d01807493f68177c60507ca_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"NAN","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.52", "题目 6": "4/15", "题目 7": "25/24", "题目 8": "6"}
```
### 响应时间：2.70秒
### token用量
- total_tokens: 790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 230 张图片: f862d077cfe247fcb7b27394ab6a33e5_jpg.rf.b3cf11987c5df8b0e5218655885b328b_with_roboflow_yolo.jpg

==================================================
![f862d077cfe247fcb7b27394ab6a33e5_jpg.rf.b3cf11987c5df8b0e5218655885b328b_with_roboflow_yolo.jpg](f862d077cfe247fcb7b27394ab6a33e5_jpg.rf.b3cf11987c5df8b0e5218655885b328b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3/11","题目 2":"5/7","题目 3":"7/8","题目 4":"1.75","题目 5":"0.542","题目 6":"4 4/5","题目 7":"25/64","题目 8":"6/25"}
```
### 响应内容：
```json
{"题目1": "2/11", "题目2": "5/7", "题目3": "1", "题目4": "1.75", "题目5": "0.552", "题目6": "4 4/5", "题目7": "25/64", "题目8": "6/25"}
```
### 响应时间：2.19秒
### token用量
- total_tokens: 785
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 231 张图片: fae27a27abf0456295d3a165486db741_jpg.rf.5429f63997a2bad299041eb97696822f_with_roboflow_yolo.jpg

==================================================
![fae27a27abf0456295d3a165486db741_jpg.rf.5429f63997a2bad299041eb97696822f_with_roboflow_yolo.jpg](fae27a27abf0456295d3a165486db741_jpg.rf.5429f63997a2bad299041eb97696822f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/6","题目 2":"980Kg","题目 3":"16本","题目 4":"45"}
```
### 响应内容：
```json
{"题目 1": "4/126", "题目 2": "980kg", "题目 3": "16本", "题目 4": "45"}
```
### 响应时间：1.93秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 232 张图片: fbb49a62f2f9428793cef82ef406e9c2_jpg.rf.4a0a9f877ee60e3cbfa55ab4d96417bb_with_roboflow_yolo.jpg

==================================================
![fbb49a62f2f9428793cef82ef406e9c2_jpg.rf.4a0a9f877ee60e3cbfa55ab4d96417bb_with_roboflow_yolo.jpg](fbb49a62f2f9428793cef82ef406e9c2_jpg.rf.4a0a9f877ee60e3cbfa55ab4d96417bb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "780", "题目8": "6", "题目9": "3000", "题目10": "1"}
```
### 响应时间：3.05秒
### token用量
- total_tokens: 792
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 233 张图片: fcbf00df24934943b0420f52e320bf30_jpg.rf.838fbca17aff692f5f55f980084ca1b7_with_roboflow_yolo.jpg

==================================================
![fcbf00df24934943b0420f52e320bf30_jpg.rf.838fbca17aff692f5f55f980084ca1b7_with_roboflow_yolo.jpg](fcbf00df24934943b0420f52e320bf30_jpg.rf.838fbca17aff692f5f55f980084ca1b7_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"981.1","题目 2":"10","题目 3":"12","题目 4":"0.0575"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.075"}
```
### 响应时间：2.44秒
### token用量
- total_tokens: 748
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 234 张图片: fcd2f8efc9f440d9a1d5d5c4b6a9357a_jpg.rf.539d6903bb79a4f7d610bb70d7b57da5_with_roboflow_yolo.jpg

==================================================
![fcd2f8efc9f440d9a1d5d5c4b6a9357a_jpg.rf.539d6903bb79a4f7d610bb70d7b57da5_with_roboflow_yolo.jpg](fcd2f8efc9f440d9a1d5d5c4b6a9357a_jpg.rf.539d6903bb79a4f7d610bb70d7b57da5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1","题目 2":"NAN","题目 3":"29/35","题目 4":"1 3/9","题目 5":"NAN","题目 6":"NAN","题目 7":"0.9","题目 8":"NAN","题目 9":"0","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "0.9", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "NAN", "题目 7": "NAN", "题目 8": "NAN", "题目 9": "NAN", "题目 10": "NAN"}
```
### 响应时间：2.47秒
### token用量
- total_tokens: 797
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 235 张图片: fd680c54b1d9495388b2fe87f25ec3cb_jpg.rf.0b5683a6184908ca0c91c06b72f79eaa_with_roboflow_yolo.jpg

==================================================
![fd680c54b1d9495388b2fe87f25ec3cb_jpg.rf.0b5683a6184908ca0c91c06b72f79eaa_with_roboflow_yolo.jpg](fd680c54b1d9495388b2fe87f25ec3cb_jpg.rf.0b5683a6184908ca0c91c06b72f79eaa_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"780","题目 8":"3/7","题目 9":"3000","题目 10":"7/9"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "1.5", "题目4": "80", "题目5": "95", "题目6": "0.5", "题目7": "80", "题目8": "3", "题目9": "3000", "题目10": "9"}
```
### 响应时间：3.49秒
### token用量
- total_tokens: 791
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 236 张图片: fe614c76d0634edaa536e57274d58617_jpg.rf.8ddf3d844b2622ddce28934386d31592_with_roboflow_yolo.jpg

==================================================
![fe614c76d0634edaa536e57274d58617_jpg.rf.8ddf3d844b2622ddce28934386d31592_with_roboflow_yolo.jpg](fe614c76d0634edaa536e57274d58617_jpg.rf.8ddf3d844b2622ddce28934386d31592_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"120","题目 2":"115","题目 3":"1.5","题目 4":"80","题目 5":"95","题目 6":"0.5","题目 7":"NAN","题目 8":"NAN","题目 9":"3000","题目 10":"NAN"}
```
### 响应内容：
```json
{"题目1": "120", "题目2": "115", "题目3": "4", "题目4": "80", "题目5": "95", "题目6": "0.9", "题目7": "780", "题目8": "5.2", "题目9": "3000", "题目10": "2.5"}
```
### 响应时间：2.64秒
### token用量
- total_tokens: 794
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
所有错题处理完成！
==================================================
