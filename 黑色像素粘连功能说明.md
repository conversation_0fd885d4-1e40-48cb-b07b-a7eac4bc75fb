# 黑色像素粘连功能实现说明

## 概述
根据`1.py`脚本中的黑色像素粘连处理逻辑，已成功将该功能集成到`test.py`、`test3.py`、`one_stage_test.py`和`main.py`脚本中。

## 功能描述
黑色像素粘连功能用于处理图像中分离的黑色像素，通过填充"黑-白-黑"之间的白色像素来增强连通性。

### 算法逻辑
参考`1.py`中的实现：
- **横向扫描**：检测"黑-白-白-白-黑"模式，将中间的白色像素填充为黑色
- **纵向扫描**：检测"黑-白-白-白-黑"模式，将中间的白色像素填充为黑色

## 修改的文件

### 1. image_utils.py
- **enhance_marks_to_black函数**：增加`use_pixel_connection`参数
- **image_to_base64函数**：增加`use_pixel_connection`参数
- 实现了与`1.py`相同的像素粘连算法

### 2. test.py
- **run_test函数**：增加`use_pixel_connection`参数
- 在"灰度阀门与像素增强"选择后，增加"黑色像素粘连"选择
- 更新输出信息，显示像素粘连状态
- 双阶段模式中保存和传递像素粘连配置

### 3. test3.py
- **run_test3函数**：增加`use_pixel_connection`参数
- 在"灰度阀门与像素增强"选择后，增加"黑色像素粘连"选择
- 更新输出信息，显示像素粘连状态
- 从配置文件加载像素粘连参数

### 4. one_stage_test.py
- **run_one_stage_test函数**：增加`use_pixel_connection`参数
- 在"灰度阀门与像素增强"选择后，增加"黑色像素粘连"选择
- 更新输出信息，显示像素粘连状态

### 5. main.py
- **batch_configs支持**：增加"像素粘连"字段
- **默认值**：像素粘连默认值为"n"（不采用）
- **参数映射**："y"=采用像素粘连，"n"=不采用像素粘连
- 更新批处理配置输出，显示像素粘连状态

## 使用方式

### 交互模式
1. 运行任何脚本（test.py、test3.py、one_stage_test.py）
2. 选择"是否采用'灰度阀门与像素增强'处理？"
3. 如果选择"y"，会继续询问"是否采用'黑色像素粘连'处理？"
4. 选择"y"采用像素粘连，"n"不采用

### batch_configs模式
在batch_configs JSON文件中添加"像素粘连"字段：
```json
{
  "batch_configs": [
    {
      "处理模式": 1,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "y",
      "像素粘连": "y",
      "图像放大倍数": 2
    }
  ]
}
```

### 字段说明
- **像素粘连**: "y"=采用黑色像素粘连，"n"=不采用黑色像素粘连
- **默认值**: "n"（不采用像素粘连）

## 输出信息
当采用像素粘连时，输出文件顶部会显示：
- `使用'灰度阀门与像素增强'处理（含黑色像素粘连）`

当不采用像素粘连时，输出文件顶部会显示：
- `使用'灰度阀门与像素增强'处理（不含黑色像素粘连）`

## 兼容性
- 所有现有功能保持不变
- 新增的像素粘连功能为可选功能
- 默认不采用像素粘连，确保向后兼容
- 支持通过参数传递，适用于批处理和交互模式

## 示例配置文件
已创建示例配置文件：`batch_configs/2025-07-30_example.json`，展示了如何使用新的"像素粘连"字段。
