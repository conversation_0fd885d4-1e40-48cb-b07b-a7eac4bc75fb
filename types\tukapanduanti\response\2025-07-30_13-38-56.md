**使用模型ID：** doubao-1-5-vision-pro-32k-250115

**使用图片文件夹：** roboflow_yolo_result/images_2025-07-30_13-38-44

## 错题
- 第 44 张图片: 2b3439a6d08741ad8ecef23252826a1b_jpg.rf.eaf8195caef648eca7edd051d04ab03a_with_roboflow_yolo.jpg
- 第 53 张图片: 31bca7eb3b454a879f81cf976ec1d8fd_jpg.rf.af335f8fbdd462f5ec669a197c1e57a8_with_roboflow_yolo.jpg
- 第 85 张图片: 52182ba388da4e5893f9b7795b5a46f3_jpg.rf.1bb1ab1145eda8230e3027bd2997bae1_with_roboflow_yolo.jpg
- 第 99 张图片: 5e233672b1c9411cbe96b934359a2bef_jpg.rf.0b79bc14de9765311fbca7eeecbed8b0_with_roboflow_yolo.jpg
- 第 102 张图片: 5fcefebc114c46159a861d4d12d4bfa6_jpg.rf.d9ae8ca84e9b27e105dd78bdd430599d_with_roboflow_yolo.jpg
- 第 110 张图片: 6bf49678b55049f88a5489baa080317d_jpg.rf.b573bf2c4d0e990d2cdc07f8fc19face_with_roboflow_yolo.jpg
- 第 122 张图片: 7426a6d1aa304650bd9fa8f2790ffe61_jpg.rf.35173c0ae61df713aa1b51d04d97c843_with_roboflow_yolo.jpg
- 第 127 张图片: 756bc341ec524ac491334470421f2dab_jpg.rf.f70aac9f64fd16d02c39d075fc90270d_with_roboflow_yolo.jpg
- 第 130 张图片: 7aead93d5dc042b1a4469321ef7469c0_jpg.rf.9e0a8f76d74493511160612cd735962f_with_roboflow_yolo.jpg
- 第 131 张图片: 7b7ae77a6c6f426486291b763645368f_jpg.rf.4dc71e4294f4fb8da897327ae6f8cfeb_with_roboflow_yolo.jpg
- 第 143 张图片: 89c0b31a70824d5b98ba6e8a0d2a380e_jpg.rf.7ecd5fd21cf808d33fedc0413947111a_with_roboflow_yolo.jpg
- 第 164 张图片: a38cb923983c46f0bbe8d1ae49f818dd_jpg.rf.75f295c156ecfbd985830ed47a5c5247_with_roboflow_yolo.jpg
- 第 167 张图片: a81bf78c579c47c3b4846a55b42accd7_jpg.rf.ccba748b88c2ef8f295f07a8ccb25990_with_roboflow_yolo.jpg
- 第 196 张图片: cbcd463994fe4bc8a8d5aaf5c799b64d_jpg.rf.d3b359323216d10cab8696bbfd6b8bdc_with_roboflow_yolo.jpg
- 第 214 张图片: e3090ef553ae4026ac99c85f618a60d9_jpg.rf.2784747b781ef2323eb86aca84c0e703_with_roboflow_yolo.jpg
- 第 229 张图片: f62c139aa6144017bfc2389ab53a973c_jpg.rf.dd37b77fbdcfdcf42dbba86647e0f81a_with_roboflow_yolo.jpg

## 准确率：93.31%  （(239 - 16) / 239）

# 运行时间: 2025-07-30_13-38-56

## 使用模型ID: doubao-1-5-vision-pro-32k-250115

## 使用图片文件夹: roboflow_yolo_result/images_2025-07-30_13-38-44

## 图片放大倍数: 1.0

找到 239 张图片，开始逐个处理...
使用的提示词: 你的任务是查看提供的图片，识别其中学生的回答。原始题目为[√][×]，学生回答有两种可能形式，要么是 [√][■]（被涂黑[×]，未涂黑[√]），要么是 [■][×]（被涂黑[√]，未涂黑[×]）。
以下是图片：

{{IMAGE}}

当识别到[√][×]（[√]和[×]没有被涂黑）时，你需要比较[√]和[×]哪个颜色更深，颜色更深的视为被涂黑。当[√]颜色更深时，学生回答为[■][×]；当[×]颜色更深时，学生回答为[√][■]。
请以JSON格式输出结果，题号必须始终从“题目1”开始，依次递增。例如：
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]"}

==================================================
处理第 1 张图片: 01c5d27711dc4dda98b75de20bad2d55_jpg.rf.51e37379570ee0239920724c669a0abc_with_roboflow_yolo.jpg

==================================================
![01c5d27711dc4dda98b75de20bad2d55_jpg.rf.51e37379570ee0239920724c669a0abc_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/01c5d27711dc4dda98b75de20bad2d55_jpg.rf.51e37379570ee0239920724c669a0abc_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：17.74秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 2 张图片: 0265677babdc46038a886795985904e5_jpg.rf.27f9c0ed5d5b14948330e4428dda6e2e_with_roboflow_yolo.jpg

==================================================
![0265677babdc46038a886795985904e5_jpg.rf.27f9c0ed5d5b14948330e4428dda6e2e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/0265677babdc46038a886795985904e5_jpg.rf.27f9c0ed5d5b14948330e4428dda6e2e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：53.07秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 3 张图片: 02d88a70ffae49b7a877778b33cd9a00_jpg.rf.a9826df85cf5cf821140ae09624e813a_with_roboflow_yolo.jpg

==================================================
![02d88a70ffae49b7a877778b33cd9a00_jpg.rf.a9826df85cf5cf821140ae09624e813a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/02d88a70ffae49b7a877778b33cd9a00_jpg.rf.a9826df85cf5cf821140ae09624e813a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]"}
```
### 响应时间：1.41秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 4 张图片: 0435aeffb35f4264aa39b0c8d97aebd0_jpg.rf.80268cef2551840c543675f86571cc23_with_roboflow_yolo.jpg

==================================================
![0435aeffb35f4264aa39b0c8d97aebd0_jpg.rf.80268cef2551840c543675f86571cc23_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/0435aeffb35f4264aa39b0c8d97aebd0_jpg.rf.80268cef2551840c543675f86571cc23_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：18.50秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 5 张图片: 043be58d92ad40e590064818ab592201_jpg.rf.d8e07520e5254c80db71c40a76603835_with_roboflow_yolo.jpg

==================================================
![043be58d92ad40e590064818ab592201_jpg.rf.d8e07520e5254c80db71c40a76603835_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/043be58d92ad40e590064818ab592201_jpg.rf.d8e07520e5254c80db71c40a76603835_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：1.91秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 6 张图片: 067735641b80434da1c76b6484b16b35_jpg.rf.716c5b57e3dad90c4ebbca7db05e0018_with_roboflow_yolo.jpg

==================================================
![067735641b80434da1c76b6484b16b35_jpg.rf.716c5b57e3dad90c4ebbca7db05e0018_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/067735641b80434da1c76b6484b16b35_jpg.rf.716c5b57e3dad90c4ebbca7db05e0018_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：9.39秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 7 张图片: 084b0d84cb2a42a982a93d27d7616bf2_jpg.rf.2e6e431414cb94e51cc890f16df8be74_with_roboflow_yolo.jpg

==================================================
![084b0d84cb2a42a982a93d27d7616bf2_jpg.rf.2e6e431414cb94e51cc890f16df8be74_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/084b0d84cb2a42a982a93d27d7616bf2_jpg.rf.2e6e431414cb94e51cc890f16df8be74_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：17.95秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 8 张图片: 0b26a4a500144694af68ed5e39a0c2f1_jpg.rf.636f42ebcbbd22757e97eaa86ed3edba_with_roboflow_yolo.jpg

==================================================
![0b26a4a500144694af68ed5e39a0c2f1_jpg.rf.636f42ebcbbd22757e97eaa86ed3edba_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/0b26a4a500144694af68ed5e39a0c2f1_jpg.rf.636f42ebcbbd22757e97eaa86ed3edba_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.97秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 9 张图片: 0b89db697b2b4a349f7ff15e97e069dc_jpg.rf.4358305b967c85d5183bdb3cfb097fdc_with_roboflow_yolo.jpg

==================================================
![0b89db697b2b4a349f7ff15e97e069dc_jpg.rf.4358305b967c85d5183bdb3cfb097fdc_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/0b89db697b2b4a349f7ff15e97e069dc_jpg.rf.4358305b967c85d5183bdb3cfb097fdc_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 10 张图片: 0c166a701ace42ad998cd035f8074b48_jpg.rf.a066bc770ff80bdbd349edb491ee39ac_with_roboflow_yolo.jpg

==================================================
![0c166a701ace42ad998cd035f8074b48_jpg.rf.a066bc770ff80bdbd349edb491ee39ac_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/0c166a701ace42ad998cd035f8074b48_jpg.rf.a066bc770ff80bdbd349edb491ee39ac_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：17.70秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 11 张图片: 10098f8bede54eb8b70f3cc7b9e84997_jpg.rf.70873345a150588b6b6f5d7620bffcdf_with_roboflow_yolo.jpg

==================================================
![10098f8bede54eb8b70f3cc7b9e84997_jpg.rf.70873345a150588b6b6f5d7620bffcdf_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/10098f8bede54eb8b70f3cc7b9e84997_jpg.rf.70873345a150588b6b6f5d7620bffcdf_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 12 张图片: 100fc60ccd814196810976aac42ddcd0_jpg.rf.6e406adffc920a96ee791f1f1a260864_with_roboflow_yolo.jpg

==================================================
![100fc60ccd814196810976aac42ddcd0_jpg.rf.6e406adffc920a96ee791f1f1a260864_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/100fc60ccd814196810976aac42ddcd0_jpg.rf.6e406adffc920a96ee791f1f1a260864_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：17.10秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 13 张图片: 1227d6671151438986aaed63342f42c9_jpg.rf.33d1883fabb75b8584f27174181d6dc0_with_roboflow_yolo.jpg

==================================================
![1227d6671151438986aaed63342f42c9_jpg.rf.33d1883fabb75b8584f27174181d6dc0_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1227d6671151438986aaed63342f42c9_jpg.rf.33d1883fabb75b8584f27174181d6dc0_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：18.18秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 14 张图片: 1248fb5594ed4ef5b0cd39aa9d5d086d_jpg.rf.f4fac092e58fdbfdaeef7bff876d4741_with_roboflow_yolo.jpg

==================================================
![1248fb5594ed4ef5b0cd39aa9d5d086d_jpg.rf.f4fac092e58fdbfdaeef7bff876d4741_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1248fb5594ed4ef5b0cd39aa9d5d086d_jpg.rf.f4fac092e58fdbfdaeef7bff876d4741_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：24.24秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 15 张图片: 12fad4452e674de2b9d5464c7cd78dc7_jpg.rf.7fc38aa8239ddfc9d0d4475036724fdc_with_roboflow_yolo.jpg

==================================================
![12fad4452e674de2b9d5464c7cd78dc7_jpg.rf.7fc38aa8239ddfc9d0d4475036724fdc_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/12fad4452e674de2b9d5464c7cd78dc7_jpg.rf.7fc38aa8239ddfc9d0d4475036724fdc_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.56秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 16 张图片: 1368280cf59541cfbd5a3093b9250b20_jpg.rf.08effa3e9861199d32bbb5a3cdb52391_with_roboflow_yolo.jpg

==================================================
![1368280cf59541cfbd5a3093b9250b20_jpg.rf.08effa3e9861199d32bbb5a3cdb52391_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1368280cf59541cfbd5a3093b9250b20_jpg.rf.08effa3e9861199d32bbb5a3cdb52391_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：18.08秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 17 张图片: 13d53fbe2c914997a52f670d2c97351f_jpg.rf.b7871a0c0d349c802ae76fa9be57e49b_with_roboflow_yolo.jpg

==================================================
![13d53fbe2c914997a52f670d2c97351f_jpg.rf.b7871a0c0d349c802ae76fa9be57e49b_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/13d53fbe2c914997a52f670d2c97351f_jpg.rf.b7871a0c0d349c802ae76fa9be57e49b_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：24.68秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 18 张图片: 14987923b9b348fbbed4a236357e9ed8_jpg.rf.ba74e63bb5522a9c240979e302250916_with_roboflow_yolo.jpg

==================================================
![14987923b9b348fbbed4a236357e9ed8_jpg.rf.ba74e63bb5522a9c240979e302250916_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/14987923b9b348fbbed4a236357e9ed8_jpg.rf.ba74e63bb5522a9c240979e302250916_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.79秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 19 张图片: 1573cc38be9f4c6aa5578c3063785b9b_jpg.rf.78b5626303f2cafe16836988d476504a_with_roboflow_yolo.jpg

==================================================
![1573cc38be9f4c6aa5578c3063785b9b_jpg.rf.78b5626303f2cafe16836988d476504a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1573cc38be9f4c6aa5578c3063785b9b_jpg.rf.78b5626303f2cafe16836988d476504a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：18.26秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 20 张图片: 15c8d90659a940d68188e767635edc7d_jpg.rf.44e4da288a6084d6d6c39d02ecb85c90_with_roboflow_yolo.jpg

==================================================
![15c8d90659a940d68188e767635edc7d_jpg.rf.44e4da288a6084d6d6c39d02ecb85c90_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/15c8d90659a940d68188e767635edc7d_jpg.rf.44e4da288a6084d6d6c39d02ecb85c90_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：2.51秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 21 张图片: 169625eccf294c4f8df8af9f5096d8c7_jpg.rf.f32e67352797c0e631765794c13c64f9_with_roboflow_yolo.jpg

==================================================
![169625eccf294c4f8df8af9f5096d8c7_jpg.rf.f32e67352797c0e631765794c13c64f9_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/169625eccf294c4f8df8af9f5096d8c7_jpg.rf.f32e67352797c0e631765794c13c64f9_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]"}
```
### 响应时间：1.75秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 22 张图片: 18aadf7cfaa04d83ba43fde2e86710ad_jpg.rf.215b36d951fd2dc1e32233f2132d53c4_with_roboflow_yolo.jpg

==================================================
![18aadf7cfaa04d83ba43fde2e86710ad_jpg.rf.215b36d951fd2dc1e32233f2132d53c4_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/18aadf7cfaa04d83ba43fde2e86710ad_jpg.rf.215b36d951fd2dc1e32233f2132d53c4_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：18.27秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 23 张图片: 18c10676352349f9ab382660dadabc72_jpg.rf.57b0ce7fc47594d29f9037f21bd2a7f4_with_roboflow_yolo.jpg

==================================================
![18c10676352349f9ab382660dadabc72_jpg.rf.57b0ce7fc47594d29f9037f21bd2a7f4_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/18c10676352349f9ab382660dadabc72_jpg.rf.57b0ce7fc47594d29f9037f21bd2a7f4_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.39秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 24 张图片: 1a69373491ed451f88001055ec9f635b_jpg.rf.e3c633a2089468232fad2a38e420fa80_with_roboflow_yolo.jpg

==================================================
![1a69373491ed451f88001055ec9f635b_jpg.rf.e3c633a2089468232fad2a38e420fa80_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1a69373491ed451f88001055ec9f635b_jpg.rf.e3c633a2089468232fad2a38e420fa80_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.62秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 25 张图片: 1b7dfb22a52f480897dd80de426c8701_jpg.rf.2633ebc3296c5d5daa98a6ed2a278aae_with_roboflow_yolo.jpg

==================================================
![1b7dfb22a52f480897dd80de426c8701_jpg.rf.2633ebc3296c5d5daa98a6ed2a278aae_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1b7dfb22a52f480897dd80de426c8701_jpg.rf.2633ebc3296c5d5daa98a6ed2a278aae_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：18.21秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 26 张图片: 1bb86a9766fb4820aaf90e0fd8790ec9_jpg.rf.4fecbe3d84ccce1545458feade45e917_with_roboflow_yolo.jpg

==================================================
![1bb86a9766fb4820aaf90e0fd8790ec9_jpg.rf.4fecbe3d84ccce1545458feade45e917_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1bb86a9766fb4820aaf90e0fd8790ec9_jpg.rf.4fecbe3d84ccce1545458feade45e917_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.97秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 27 张图片: 1c3b9ca966f64aa48fd0b2e902cf926f_jpg.rf.e093d0e0d47cd09f8c7fca7b4625fa25_with_roboflow_yolo.jpg

==================================================
![1c3b9ca966f64aa48fd0b2e902cf926f_jpg.rf.e093d0e0d47cd09f8c7fca7b4625fa25_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1c3b9ca966f64aa48fd0b2e902cf926f_jpg.rf.e093d0e0d47cd09f8c7fca7b4625fa25_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.11秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 28 张图片: 1cfd4a21ff48453d8e3d30c8245e6e5e_jpg.rf.02a3fac93ae57b2e766ae262e4609d30_with_roboflow_yolo.jpg

==================================================
![1cfd4a21ff48453d8e3d30c8245e6e5e_jpg.rf.02a3fac93ae57b2e766ae262e4609d30_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1cfd4a21ff48453d8e3d30c8245e6e5e_jpg.rf.02a3fac93ae57b2e766ae262e4609d30_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：18.26秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 29 张图片: 1d1ba393209c48fb889bda0155de77d3_jpg.rf.084fa1c5d04f1cc653a65e87bd37473e_with_roboflow_yolo.jpg

==================================================
![1d1ba393209c48fb889bda0155de77d3_jpg.rf.084fa1c5d04f1cc653a65e87bd37473e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1d1ba393209c48fb889bda0155de77d3_jpg.rf.084fa1c5d04f1cc653a65e87bd37473e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.59秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 30 张图片: 1fd55f59681c4066a93faec8a82b4de5_jpg.rf.57f975b818cd8e70434947c923817bcd_with_roboflow_yolo.jpg

==================================================
![1fd55f59681c4066a93faec8a82b4de5_jpg.rf.57f975b818cd8e70434947c923817bcd_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1fd55f59681c4066a93faec8a82b4de5_jpg.rf.57f975b818cd8e70434947c923817bcd_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.70秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 31 张图片: 1ff9af58c15140cfaddbca89af2a34e7_jpg.rf.c04c90730d0809e630446dde92c82099_with_roboflow_yolo.jpg

==================================================
![1ff9af58c15140cfaddbca89af2a34e7_jpg.rf.c04c90730d0809e630446dde92c82099_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/1ff9af58c15140cfaddbca89af2a34e7_jpg.rf.c04c90730d0809e630446dde92c82099_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：18.43秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 32 张图片: 2054bc378c9347ea930247016dfb2088_jpg.rf.b99eeceb5fc4ae625085614625330c69_with_roboflow_yolo.jpg

==================================================
![2054bc378c9347ea930247016dfb2088_jpg.rf.b99eeceb5fc4ae625085614625330c69_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2054bc378c9347ea930247016dfb2088_jpg.rf.b99eeceb5fc4ae625085614625330c69_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.44秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 33 张图片: 220039f8bdbb408cb6d2d4a846da268b_jpg.rf.572e488acd8ed3db2546daaf2fb90fde_with_roboflow_yolo.jpg

==================================================
![220039f8bdbb408cb6d2d4a846da268b_jpg.rf.572e488acd8ed3db2546daaf2fb90fde_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/220039f8bdbb408cb6d2d4a846da268b_jpg.rf.572e488acd8ed3db2546daaf2fb90fde_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.92秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 34 张图片: 225d13be425b460286407f055e12b15e_jpg.rf.d77d57393b318ad09b22f3ab08520b65_with_roboflow_yolo.jpg

==================================================
![225d13be425b460286407f055e12b15e_jpg.rf.d77d57393b318ad09b22f3ab08520b65_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/225d13be425b460286407f055e12b15e_jpg.rf.d77d57393b318ad09b22f3ab08520b65_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：18.15秒
### token用量
- total_tokens: 609
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 35 张图片: 22abd1f7434e4562ade563a0ab13eeb8_jpg.rf.af9f5eaf0e4fff135dc1e05f96332b3b_with_roboflow_yolo.jpg

==================================================
![22abd1f7434e4562ade563a0ab13eeb8_jpg.rf.af9f5eaf0e4fff135dc1e05f96332b3b_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/22abd1f7434e4562ade563a0ab13eeb8_jpg.rf.af9f5eaf0e4fff135dc1e05f96332b3b_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]"}
```
### 响应时间：24.28秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 36 张图片: 235d135fcb264ef5b7d80b446cb02c99_jpg.rf.2df874727d19576c3386e783795a9fd6_with_roboflow_yolo.jpg

==================================================
![235d135fcb264ef5b7d80b446cb02c99_jpg.rf.2df874727d19576c3386e783795a9fd6_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/235d135fcb264ef5b7d80b446cb02c99_jpg.rf.2df874727d19576c3386e783795a9fd6_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.26秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 37 张图片: 23d442f0500340fcba8283b5b3923955_jpg.rf.dc5e57d2819d7d045b8b6a3f4a071229_with_roboflow_yolo.jpg

==================================================
![23d442f0500340fcba8283b5b3923955_jpg.rf.dc5e57d2819d7d045b8b6a3f4a071229_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/23d442f0500340fcba8283b5b3923955_jpg.rf.dc5e57d2819d7d045b8b6a3f4a071229_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.75秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 38 张图片: 23e2c297a3424fd5ade3cc392637b739_jpg.rf.40aea69699c4101182f16c4c233b9ef4_with_roboflow_yolo.jpg

==================================================
![23e2c297a3424fd5ade3cc392637b739_jpg.rf.40aea69699c4101182f16c4c233b9ef4_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/23e2c297a3424fd5ade3cc392637b739_jpg.rf.40aea69699c4101182f16c4c233b9ef4_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：16.79秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 39 张图片: 2666b361781a4cae93ad5f130226c0fe_jpg.rf.254f4f18e564b91e2a1a6993ed218360_with_roboflow_yolo.jpg

==================================================
![2666b361781a4cae93ad5f130226c0fe_jpg.rf.254f4f18e564b91e2a1a6993ed218360_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2666b361781a4cae93ad5f130226c0fe_jpg.rf.254f4f18e564b91e2a1a6993ed218360_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：17.07秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 40 张图片: 2677cc98302b44b0afc76b52931d603d_jpg.rf.a9a26f7d31891c3c540151f71554a6f0_with_roboflow_yolo.jpg

==================================================
![2677cc98302b44b0afc76b52931d603d_jpg.rf.a9a26f7d31891c3c540151f71554a6f0_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2677cc98302b44b0afc76b52931d603d_jpg.rf.a9a26f7d31891c3c540151f71554a6f0_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：18.32秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 41 张图片: 2897e06d59014476b93505c9ec1deb86_jpg.rf.6a43f93cec9660fed781a0a937c4366d_with_roboflow_yolo.jpg

==================================================
![2897e06d59014476b93505c9ec1deb86_jpg.rf.6a43f93cec9660fed781a0a937c4366d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2897e06d59014476b93505c9ec1deb86_jpg.rf.6a43f93cec9660fed781a0a937c4366d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：3.02秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 42 张图片: 2a0e8d8c4cb64cb892b6359198abd0b2_jpg.rf.3c76a78be965d76dd5671c9e4f445baf_with_roboflow_yolo.jpg

==================================================
![2a0e8d8c4cb64cb892b6359198abd0b2_jpg.rf.3c76a78be965d76dd5671c9e4f445baf_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2a0e8d8c4cb64cb892b6359198abd0b2_jpg.rf.3c76a78be965d76dd5671c9e4f445baf_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：16.81秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 43 张图片: 2af83348e2dd448dba73b0d1a34e6044_jpg.rf.ab4729c34e4113c6604c1acd06549212_with_roboflow_yolo.jpg

==================================================
![2af83348e2dd448dba73b0d1a34e6044_jpg.rf.ab4729c34e4113c6604c1acd06549212_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2af83348e2dd448dba73b0d1a34e6044_jpg.rf.ab4729c34e4113c6604c1acd06549212_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：17.56秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 44 张图片: 2b3439a6d08741ad8ecef23252826a1b_jpg.rf.eaf8195caef648eca7edd051d04ab03a_with_roboflow_yolo.jpg

==================================================
![2b3439a6d08741ad8ecef23252826a1b_jpg.rf.eaf8195caef648eca7edd051d04ab03a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2b3439a6d08741ad8ecef23252826a1b_jpg.rf.eaf8195caef648eca7edd051d04ab03a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.17秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 45 张图片: 2ba83c99369b41dfa90159df01994c01_jpg.rf.4b0daa316a8c376404836c2df85ca8bb_with_roboflow_yolo.jpg

==================================================
![2ba83c99369b41dfa90159df01994c01_jpg.rf.4b0daa316a8c376404836c2df85ca8bb_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2ba83c99369b41dfa90159df01994c01_jpg.rf.4b0daa316a8c376404836c2df85ca8bb_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：17.41秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 46 张图片: 2c0ae9e3596442d48f1f5651f59dc148_jpg.rf.8bbc2a0386cab24f076f3f200b0a3906_with_roboflow_yolo.jpg

==================================================
![2c0ae9e3596442d48f1f5651f59dc148_jpg.rf.8bbc2a0386cab24f076f3f200b0a3906_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2c0ae9e3596442d48f1f5651f59dc148_jpg.rf.8bbc2a0386cab24f076f3f200b0a3906_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：18.46秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 47 张图片: 2c22aa15648640a18f1c1a1be2281399_jpg.rf.2aab383f8e63dbcc7ada6eee1ac02a67_with_roboflow_yolo.jpg

==================================================
![2c22aa15648640a18f1c1a1be2281399_jpg.rf.2aab383f8e63dbcc7ada6eee1ac02a67_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2c22aa15648640a18f1c1a1be2281399_jpg.rf.2aab383f8e63dbcc7ada6eee1ac02a67_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.51秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 48 张图片: 2cabdce5e061462bb022de1f56dca185_jpg.rf.0466e3b0c223da268d438c6f1f30184a_with_roboflow_yolo.jpg

==================================================
![2cabdce5e061462bb022de1f56dca185_jpg.rf.0466e3b0c223da268d438c6f1f30184a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2cabdce5e061462bb022de1f56dca185_jpg.rf.0466e3b0c223da268d438c6f1f30184a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.12秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 49 张图片: 2cf233d838e84a0d8e75f8c24eaf9000_jpg.rf.669ee03528f8f013bd3b4cb7da260f09_with_roboflow_yolo.jpg

==================================================
![2cf233d838e84a0d8e75f8c24eaf9000_jpg.rf.669ee03528f8f013bd3b4cb7da260f09_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2cf233d838e84a0d8e75f8c24eaf9000_jpg.rf.669ee03528f8f013bd3b4cb7da260f09_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：17.55秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 50 张图片: 2d2aee22931f4a6d9448c5ed37dd50cf_jpg.rf.9cf8246a842cee883349a6e998362d27_with_roboflow_yolo.jpg

==================================================
![2d2aee22931f4a6d9448c5ed37dd50cf_jpg.rf.9cf8246a842cee883349a6e998362d27_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2d2aee22931f4a6d9448c5ed37dd50cf_jpg.rf.9cf8246a842cee883349a6e998362d27_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：24.73秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 51 张图片: 2dc4c6346cd9423b979bef0a2838cc49_jpg.rf.cdf396a9b0e8da74f5c22e0700e13db3_with_roboflow_yolo.jpg

==================================================
![2dc4c6346cd9423b979bef0a2838cc49_jpg.rf.cdf396a9b0e8da74f5c22e0700e13db3_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2dc4c6346cd9423b979bef0a2838cc49_jpg.rf.cdf396a9b0e8da74f5c22e0700e13db3_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]", "题目6": "[√][■]"}
```
### 响应时间：1.97秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 52 张图片: 2fa026a78d4d4eedae0826011a5ce93d_jpg.rf.d9a1884ea84898c29b0c9be1fca04ada_with_roboflow_yolo.jpg

==================================================
![2fa026a78d4d4eedae0826011a5ce93d_jpg.rf.d9a1884ea84898c29b0c9be1fca04ada_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/2fa026a78d4d4eedae0826011a5ce93d_jpg.rf.d9a1884ea84898c29b0c9be1fca04ada_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：46.47秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 53 张图片: 31bca7eb3b454a879f81cf976ec1d8fd_jpg.rf.af335f8fbdd462f5ec669a197c1e57a8_with_roboflow_yolo.jpg

==================================================
![31bca7eb3b454a879f81cf976ec1d8fd_jpg.rf.af335f8fbdd462f5ec669a197c1e57a8_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/31bca7eb3b454a879f81cf976ec1d8fd_jpg.rf.af335f8fbdd462f5ec669a197c1e57a8_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：1.72秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 54 张图片: 3261095446614ebfa02905282271ddae_jpg.rf.21540f08c367a78d46e9b41807f0596a_with_roboflow_yolo.jpg

==================================================
![3261095446614ebfa02905282271ddae_jpg.rf.21540f08c367a78d46e9b41807f0596a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/3261095446614ebfa02905282271ddae_jpg.rf.21540f08c367a78d46e9b41807f0596a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：1.61秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 55 张图片: 35a30472116f484c82938796625ca0dc_jpg.rf.f6e456d857c3fc6c44cba95f8f2e9010_with_roboflow_yolo.jpg

==================================================
![35a30472116f484c82938796625ca0dc_jpg.rf.f6e456d857c3fc6c44cba95f8f2e9010_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/35a30472116f484c82938796625ca0dc_jpg.rf.f6e456d857c3fc6c44cba95f8f2e9010_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.00秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 56 张图片: 35c3487b4b274396923ce0e7dd815e85_jpg.rf.f400b77fd79c6fc4367af29d96a0441c_with_roboflow_yolo.jpg

==================================================
![35c3487b4b274396923ce0e7dd815e85_jpg.rf.f400b77fd79c6fc4367af29d96a0441c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/35c3487b4b274396923ce0e7dd815e85_jpg.rf.f400b77fd79c6fc4367af29d96a0441c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.02秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 57 张图片: 35d538a5ab1846ae84106b9fa55f532b_jpg.rf.a84c7407e578177e34cc55c8bf260753_with_roboflow_yolo.jpg

==================================================
![35d538a5ab1846ae84106b9fa55f532b_jpg.rf.a84c7407e578177e34cc55c8bf260753_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/35d538a5ab1846ae84106b9fa55f532b_jpg.rf.a84c7407e578177e34cc55c8bf260753_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 58 张图片: 366b33bae67140acb73de25ce6bebe64_jpg.rf.d54c6c2c3d296b444e81cb869c59adeb_with_roboflow_yolo.jpg

==================================================
![366b33bae67140acb73de25ce6bebe64_jpg.rf.d54c6c2c3d296b444e81cb869c59adeb_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/366b33bae67140acb73de25ce6bebe64_jpg.rf.d54c6c2c3d296b444e81cb869c59adeb_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 59 张图片: 371e97181cb545b8bde37b934eded932_jpg.rf.c29e757fc59f2f1f39fc066d9f43a047_with_roboflow_yolo.jpg

==================================================
![371e97181cb545b8bde37b934eded932_jpg.rf.c29e757fc59f2f1f39fc066d9f43a047_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/371e97181cb545b8bde37b934eded932_jpg.rf.c29e757fc59f2f1f39fc066d9f43a047_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 60 张图片: 37b14a94f1c647ed95937e30b5611c19_jpg.rf.d5e30a9f38eaafdfa956fee9d8bec10e_with_roboflow_yolo.jpg

==================================================
![37b14a94f1c647ed95937e30b5611c19_jpg.rf.d5e30a9f38eaafdfa956fee9d8bec10e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/37b14a94f1c647ed95937e30b5611c19_jpg.rf.d5e30a9f38eaafdfa956fee9d8bec10e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 61 张图片: 389fc24b7f884367bb07eb3de3ae20ce_jpg.rf.f438d8794041f260a2135b88adbe8fdf_with_roboflow_yolo.jpg

==================================================
![389fc24b7f884367bb07eb3de3ae20ce_jpg.rf.f438d8794041f260a2135b88adbe8fdf_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/389fc24b7f884367bb07eb3de3ae20ce_jpg.rf.f438d8794041f260a2135b88adbe8fdf_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.78秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 62 张图片: 38b7dac1f7814b7f87e89a5444fbbda8_jpg.rf.2550478b27d5af734c5a8385a51dd0b0_with_roboflow_yolo.jpg

==================================================
![38b7dac1f7814b7f87e89a5444fbbda8_jpg.rf.2550478b27d5af734c5a8385a51dd0b0_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/38b7dac1f7814b7f87e89a5444fbbda8_jpg.rf.2550478b27d5af734c5a8385a51dd0b0_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：9.10秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 63 张图片: 39129714202a44d3b3831b8e77e36530_jpg.rf.535870f329fcfa5be2b179ffdcc82f6c_with_roboflow_yolo.jpg

==================================================
![39129714202a44d3b3831b8e77e36530_jpg.rf.535870f329fcfa5be2b179ffdcc82f6c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/39129714202a44d3b3831b8e77e36530_jpg.rf.535870f329fcfa5be2b179ffdcc82f6c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：17.23秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 64 张图片: 3986bca2a9bd481baeb2bdf79d70223c_jpg.rf.6030ef4f10e7e2ac3f75177c2bb7bb3b_with_roboflow_yolo.jpg

==================================================
![3986bca2a9bd481baeb2bdf79d70223c_jpg.rf.6030ef4f10e7e2ac3f75177c2bb7bb3b_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/3986bca2a9bd481baeb2bdf79d70223c_jpg.rf.6030ef4f10e7e2ac3f75177c2bb7bb3b_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：38.93秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 65 张图片: 3aa369c41f434d3e95b93f7cbeeb0676_jpg.rf.2793d5d213096c741f6174c3482a8939_with_roboflow_yolo.jpg

==================================================
![3aa369c41f434d3e95b93f7cbeeb0676_jpg.rf.2793d5d213096c741f6174c3482a8939_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/3aa369c41f434d3e95b93f7cbeeb0676_jpg.rf.2793d5d213096c741f6174c3482a8939_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：8.95秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 66 张图片: 3bc4615f6120488485b907caf79120ee_jpg.rf.dcb971b69b05a2d916c2c5a3b7ec2cfa_with_roboflow_yolo.jpg

==================================================
![3bc4615f6120488485b907caf79120ee_jpg.rf.dcb971b69b05a2d916c2c5a3b7ec2cfa_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/3bc4615f6120488485b907caf79120ee_jpg.rf.dcb971b69b05a2d916c2c5a3b7ec2cfa_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 67 张图片: 3f90ebcda15341f8b7911b236f20b346_jpg.rf.a5758e6e256a129559a42020da2c3180_with_roboflow_yolo.jpg

==================================================
![3f90ebcda15341f8b7911b236f20b346_jpg.rf.a5758e6e256a129559a42020da2c3180_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/3f90ebcda15341f8b7911b236f20b346_jpg.rf.a5758e6e256a129559a42020da2c3180_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 68 张图片: 41812a5568d94ea29ab80b144aba9c7d_jpg.rf.081dd251403d4698e6419b5db169e7a2_with_roboflow_yolo.jpg

==================================================
![41812a5568d94ea29ab80b144aba9c7d_jpg.rf.081dd251403d4698e6419b5db169e7a2_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/41812a5568d94ea29ab80b144aba9c7d_jpg.rf.081dd251403d4698e6419b5db169e7a2_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：2.10秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 69 张图片: 42d277b8465b4133ba01f4a37c391f34_jpg.rf.cf1dbda352e7b991dfe240337bd3eb6f_with_roboflow_yolo.jpg

==================================================
![42d277b8465b4133ba01f4a37c391f34_jpg.rf.cf1dbda352e7b991dfe240337bd3eb6f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/42d277b8465b4133ba01f4a37c391f34_jpg.rf.cf1dbda352e7b991dfe240337bd3eb6f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：1.61秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 70 张图片: 431d4267bd864154a3de6d8be7021851_jpg.rf.c62739e02625e1fc49236ffb4cb762d7_with_roboflow_yolo.jpg

==================================================
![431d4267bd864154a3de6d8be7021851_jpg.rf.c62739e02625e1fc49236ffb4cb762d7_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/431d4267bd864154a3de6d8be7021851_jpg.rf.c62739e02625e1fc49236ffb4cb762d7_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：8.74秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 71 张图片: 438ba213ce84465daea9ed26bfc1c8e0_jpg.rf.b86d72dfe22d509193ddaf548153317a_with_roboflow_yolo.jpg

==================================================
![438ba213ce84465daea9ed26bfc1c8e0_jpg.rf.b86d72dfe22d509193ddaf548153317a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/438ba213ce84465daea9ed26bfc1c8e0_jpg.rf.b86d72dfe22d509193ddaf548153317a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：8.83秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 72 张图片: 45054825895040a2bb3a56b8f5e82051_jpg.rf.2a39f4deddbfb9e69057928f868f3676_with_roboflow_yolo.jpg

==================================================
![45054825895040a2bb3a56b8f5e82051_jpg.rf.2a39f4deddbfb9e69057928f868f3676_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/45054825895040a2bb3a56b8f5e82051_jpg.rf.2a39f4deddbfb9e69057928f868f3676_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 73 张图片: 46b3b81a6bd74d5ea5c769bc8c01af8f_jpg.rf.4b9d2f00b1a7766bcab43983e6063d54_with_roboflow_yolo.jpg

==================================================
![46b3b81a6bd74d5ea5c769bc8c01af8f_jpg.rf.4b9d2f00b1a7766bcab43983e6063d54_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/46b3b81a6bd74d5ea5c769bc8c01af8f_jpg.rf.4b9d2f00b1a7766bcab43983e6063d54_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：9.46秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 74 张图片: 4734ff3fa8cd4a038be8d4836eedbeed_jpg.rf.8b63e7b102dd77cff04e8ad1924c96be_with_roboflow_yolo.jpg

==================================================
![4734ff3fa8cd4a038be8d4836eedbeed_jpg.rf.8b63e7b102dd77cff04e8ad1924c96be_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/4734ff3fa8cd4a038be8d4836eedbeed_jpg.rf.8b63e7b102dd77cff04e8ad1924c96be_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.79秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 75 张图片: 475243fa45e546d49648edbfdbe09265_jpg.rf.0da0fac0423bd532164848ff6fb95017_with_roboflow_yolo.jpg

==================================================
![475243fa45e546d49648edbfdbe09265_jpg.rf.0da0fac0423bd532164848ff6fb95017_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/475243fa45e546d49648edbfdbe09265_jpg.rf.0da0fac0423bd532164848ff6fb95017_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 76 张图片: 48289012f67d49528fa2f09794fb8dfe_jpg.rf.1dac678716ba5d565ffac1c85a127811_with_roboflow_yolo.jpg

==================================================
![48289012f67d49528fa2f09794fb8dfe_jpg.rf.1dac678716ba5d565ffac1c85a127811_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/48289012f67d49528fa2f09794fb8dfe_jpg.rf.1dac678716ba5d565ffac1c85a127811_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：9.75秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 77 张图片: 4a022fdfa8324df1b64ccf117c80c221_jpg.rf.76e5cf54361d60d27d1c4584dbec75fe_with_roboflow_yolo.jpg

==================================================
![4a022fdfa8324df1b64ccf117c80c221_jpg.rf.76e5cf54361d60d27d1c4584dbec75fe_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/4a022fdfa8324df1b64ccf117c80c221_jpg.rf.76e5cf54361d60d27d1c4584dbec75fe_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.65秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 78 张图片: 4afad8769e0e491bb7d5dd0fb089bea4_jpg.rf.1323c7fc73fb6b1579eb413922092946_with_roboflow_yolo.jpg

==================================================
![4afad8769e0e491bb7d5dd0fb089bea4_jpg.rf.1323c7fc73fb6b1579eb413922092946_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/4afad8769e0e491bb7d5dd0fb089bea4_jpg.rf.1323c7fc73fb6b1579eb413922092946_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.16秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 79 张图片: 4d0ff2ab63ad4385a552d33ef56fe33f_jpg.rf.98261392bc0430c987eb8833bc16a3ab_with_roboflow_yolo.jpg

==================================================
![4d0ff2ab63ad4385a552d33ef56fe33f_jpg.rf.98261392bc0430c987eb8833bc16a3ab_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/4d0ff2ab63ad4385a552d33ef56fe33f_jpg.rf.98261392bc0430c987eb8833bc16a3ab_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：8.99秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 80 张图片: 4e2835c736704d95a379e890caba08da_jpg.rf.101d5f53b7e5bb689683d216ae88ef62_with_roboflow_yolo.jpg

==================================================
![4e2835c736704d95a379e890caba08da_jpg.rf.101d5f53b7e5bb689683d216ae88ef62_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/4e2835c736704d95a379e890caba08da_jpg.rf.101d5f53b7e5bb689683d216ae88ef62_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：17.22秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 81 张图片: 4f8bb9a1fb5c461bb8bfa1f522216e58_jpg.rf.84056d25cb8cccf4403a5dcc8451148d_with_roboflow_yolo.jpg

==================================================
![4f8bb9a1fb5c461bb8bfa1f522216e58_jpg.rf.84056d25cb8cccf4403a5dcc8451148d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/4f8bb9a1fb5c461bb8bfa1f522216e58_jpg.rf.84056d25cb8cccf4403a5dcc8451148d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.00秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 82 张图片: 5062a41b66594ad386f2e2423135fc85_jpg.rf.4767aa815e94b2f5819e2e231a83798e_with_roboflow_yolo.jpg

==================================================
![5062a41b66594ad386f2e2423135fc85_jpg.rf.4767aa815e94b2f5819e2e231a83798e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5062a41b66594ad386f2e2423135fc85_jpg.rf.4767aa815e94b2f5819e2e231a83798e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.85秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 83 张图片: 510bed1474d3467e8e1feea647c55cf4_jpg.rf.51b88e5800b5a64c8ec2eca4b1806665_with_roboflow_yolo.jpg

==================================================
![510bed1474d3467e8e1feea647c55cf4_jpg.rf.51b88e5800b5a64c8ec2eca4b1806665_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/510bed1474d3467e8e1feea647c55cf4_jpg.rf.51b88e5800b5a64c8ec2eca4b1806665_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.59秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 84 张图片: 51da62629666498f8270c66042a5204d_jpg.rf.c9dddc7bfa98d78b61ecdf9cbee9829a_with_roboflow_yolo.jpg

==================================================
![51da62629666498f8270c66042a5204d_jpg.rf.c9dddc7bfa98d78b61ecdf9cbee9829a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/51da62629666498f8270c66042a5204d_jpg.rf.c9dddc7bfa98d78b61ecdf9cbee9829a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]", "题目6": "[√][■]"}
```
### 响应时间：30.65秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 85 张图片: 52182ba388da4e5893f9b7795b5a46f3_jpg.rf.1bb1ab1145eda8230e3027bd2997bae1_with_roboflow_yolo.jpg

==================================================
![52182ba388da4e5893f9b7795b5a46f3_jpg.rf.1bb1ab1145eda8230e3027bd2997bae1_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/52182ba388da4e5893f9b7795b5a46f3_jpg.rf.1bb1ab1145eda8230e3027bd2997bae1_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.38秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 86 张图片: 52278135e3064280904ad26cbf32e123_jpg.rf.851f00c84c7599277be9cd7dec1354d0_with_roboflow_yolo.jpg

==================================================
![52278135e3064280904ad26cbf32e123_jpg.rf.851f00c84c7599277be9cd7dec1354d0_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/52278135e3064280904ad26cbf32e123_jpg.rf.851f00c84c7599277be9cd7dec1354d0_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：23.38秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 87 张图片: 566a11847a7e4e11ad2f16a9c4481285_jpg.rf.40a1a9b409b320fcd357c47f630191df_with_roboflow_yolo.jpg

==================================================
![566a11847a7e4e11ad2f16a9c4481285_jpg.rf.40a1a9b409b320fcd357c47f630191df_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/566a11847a7e4e11ad2f16a9c4481285_jpg.rf.40a1a9b409b320fcd357c47f630191df_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.78秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 88 张图片: 56da1adf8a254fbb839d043e2b587a27_jpg.rf.9497f65bbe319e3270661823534a4a6a_with_roboflow_yolo.jpg

==================================================
![56da1adf8a254fbb839d043e2b587a27_jpg.rf.9497f65bbe319e3270661823534a4a6a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/56da1adf8a254fbb839d043e2b587a27_jpg.rf.9497f65bbe319e3270661823534a4a6a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.34秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 89 张图片: 573e01dc259f409ba737ed8e834820bf_jpg.rf.bb8c313d06567fd5371bea758d780738_with_roboflow_yolo.jpg

==================================================
![573e01dc259f409ba737ed8e834820bf_jpg.rf.bb8c313d06567fd5371bea758d780738_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/573e01dc259f409ba737ed8e834820bf_jpg.rf.bb8c313d06567fd5371bea758d780738_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：8.78秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 90 张图片: 587e19abd7d046e9aaae7799cf7015e1_jpg.rf.ab7ea6d0d694ecbe45d8b1d7496a30c2_with_roboflow_yolo.jpg

==================================================
![587e19abd7d046e9aaae7799cf7015e1_jpg.rf.ab7ea6d0d694ecbe45d8b1d7496a30c2_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/587e19abd7d046e9aaae7799cf7015e1_jpg.rf.ab7ea6d0d694ecbe45d8b1d7496a30c2_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.90秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 91 张图片: 5a00350985c045bab4235016d7903c5e_jpg.rf.b696ad3121ea21365a8864d869d45473_with_roboflow_yolo.jpg

==================================================
![5a00350985c045bab4235016d7903c5e_jpg.rf.b696ad3121ea21365a8864d869d45473_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5a00350985c045bab4235016d7903c5e_jpg.rf.b696ad3121ea21365a8864d869d45473_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.89秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 92 张图片: 5aafe1b12b144ea198e0d0bc17af02ee_jpg.rf.2b88e16b8d403ae640f3abbc39b6638c_with_roboflow_yolo.jpg

==================================================
![5aafe1b12b144ea198e0d0bc17af02ee_jpg.rf.2b88e16b8d403ae640f3abbc39b6638c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5aafe1b12b144ea198e0d0bc17af02ee_jpg.rf.2b88e16b8d403ae640f3abbc39b6638c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：16.76秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 93 张图片: 5ac92247fe1148c1bcbf0320ffdf0c11_jpg.rf.a10fdfa750e3fee1acf5e1f2afb00271_with_roboflow_yolo.jpg

==================================================
![5ac92247fe1148c1bcbf0320ffdf0c11_jpg.rf.a10fdfa750e3fee1acf5e1f2afb00271_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5ac92247fe1148c1bcbf0320ffdf0c11_jpg.rf.a10fdfa750e3fee1acf5e1f2afb00271_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.21秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 94 张图片: 5afb74c2e9244e329dbfa88b0af692af_jpg.rf.78d0addea13e210e387dbcd19178bd35_with_roboflow_yolo.jpg

==================================================
![5afb74c2e9244e329dbfa88b0af692af_jpg.rf.78d0addea13e210e387dbcd19178bd35_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5afb74c2e9244e329dbfa88b0af692af_jpg.rf.78d0addea13e210e387dbcd19178bd35_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：8.81秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 95 张图片: 5b08882c0afa430ba048cdc6f956257e_jpg.rf.2fc21b7d7b7ec75d7ededc260dab60b8_with_roboflow_yolo.jpg

==================================================
![5b08882c0afa430ba048cdc6f956257e_jpg.rf.2fc21b7d7b7ec75d7ededc260dab60b8_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5b08882c0afa430ba048cdc6f956257e_jpg.rf.2fc21b7d7b7ec75d7ededc260dab60b8_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：9.08秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 96 张图片: 5c4c1ea753d34dc0a4d84f66e0050bbc_jpg.rf.4183dcfa2e7e9f5362479935d355bc7b_with_roboflow_yolo.jpg

==================================================
![5c4c1ea753d34dc0a4d84f66e0050bbc_jpg.rf.4183dcfa2e7e9f5362479935d355bc7b_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5c4c1ea753d34dc0a4d84f66e0050bbc_jpg.rf.4183dcfa2e7e9f5362479935d355bc7b_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.70秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 97 张图片: 5d420657cf364a698f6804ffc8702eda_jpg.rf.d827d6fa47f67023b299f136e843c66d_with_roboflow_yolo.jpg

==================================================
![5d420657cf364a698f6804ffc8702eda_jpg.rf.d827d6fa47f67023b299f136e843c66d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5d420657cf364a698f6804ffc8702eda_jpg.rf.d827d6fa47f67023b299f136e843c66d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：16.93秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 98 张图片: 5d9c11c3f1304368819e657103640db5_jpg.rf.0a39214628eaeecd9829c4f33b0eb916_with_roboflow_yolo.jpg

==================================================
![5d9c11c3f1304368819e657103640db5_jpg.rf.0a39214628eaeecd9829c4f33b0eb916_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5d9c11c3f1304368819e657103640db5_jpg.rf.0a39214628eaeecd9829c4f33b0eb916_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.05秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 99 张图片: 5e233672b1c9411cbe96b934359a2bef_jpg.rf.0b79bc14de9765311fbca7eeecbed8b0_with_roboflow_yolo.jpg

==================================================
![5e233672b1c9411cbe96b934359a2bef_jpg.rf.0b79bc14de9765311fbca7eeecbed8b0_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5e233672b1c9411cbe96b934359a2bef_jpg.rf.0b79bc14de9765311fbca7eeecbed8b0_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][×]"}
```
### 响应时间：17.22秒
### token用量
- total_tokens: 579
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 100 张图片: 5ed62f5b40ae432b9ab9462186aed961_jpg.rf.60d1493620acfb2930b5cd8032394e79_with_roboflow_yolo.jpg

==================================================
![5ed62f5b40ae432b9ab9462186aed961_jpg.rf.60d1493620acfb2930b5cd8032394e79_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5ed62f5b40ae432b9ab9462186aed961_jpg.rf.60d1493620acfb2930b5cd8032394e79_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：1.60秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 101 张图片: 5edbbf2391634d48989806a01b56072a_jpg.rf.8ea5637f1996e5ff41f75d7bfffa2706_with_roboflow_yolo.jpg

==================================================
![5edbbf2391634d48989806a01b56072a_jpg.rf.8ea5637f1996e5ff41f75d7bfffa2706_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5edbbf2391634d48989806a01b56072a_jpg.rf.8ea5637f1996e5ff41f75d7bfffa2706_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 102 张图片: 5fcefebc114c46159a861d4d12d4bfa6_jpg.rf.d9ae8ca84e9b27e105dd78bdd430599d_with_roboflow_yolo.jpg

==================================================
![5fcefebc114c46159a861d4d12d4bfa6_jpg.rf.d9ae8ca84e9b27e105dd78bdd430599d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/5fcefebc114c46159a861d4d12d4bfa6_jpg.rf.d9ae8ca84e9b27e105dd78bdd430599d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 103 张图片: 6051c0eea20a47a098a8caf71d2857f0_jpg.rf.c3aa7429506b8c5090412939b58818ab_with_roboflow_yolo.jpg

==================================================
![6051c0eea20a47a098a8caf71d2857f0_jpg.rf.c3aa7429506b8c5090412939b58818ab_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6051c0eea20a47a098a8caf71d2857f0_jpg.rf.c3aa7429506b8c5090412939b58818ab_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：8.67秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 104 张图片: 63962f3a1d32405db20f34c12dc7a1e8_jpg.rf.bb0f44cf909a46526032e6840f4cc1e8_with_roboflow_yolo.jpg

==================================================
![63962f3a1d32405db20f34c12dc7a1e8_jpg.rf.bb0f44cf909a46526032e6840f4cc1e8_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/63962f3a1d32405db20f34c12dc7a1e8_jpg.rf.bb0f44cf909a46526032e6840f4cc1e8_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 105 张图片: 63bda77e84d64a77b101654a44c9a859_jpg.rf.817e3b4e4b0768441b84c6d8d8523abe_with_roboflow_yolo.jpg

==================================================
![63bda77e84d64a77b101654a44c9a859_jpg.rf.817e3b4e4b0768441b84c6d8d8523abe_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/63bda77e84d64a77b101654a44c9a859_jpg.rf.817e3b4e4b0768441b84c6d8d8523abe_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 106 张图片: 6819a76402cd4da19462fc9316bba4ad_jpg.rf.250c09bf83aa547dd9fb50d73540dd4d_with_roboflow_yolo.jpg

==================================================
![6819a76402cd4da19462fc9316bba4ad_jpg.rf.250c09bf83aa547dd9fb50d73540dd4d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6819a76402cd4da19462fc9316bba4ad_jpg.rf.250c09bf83aa547dd9fb50d73540dd4d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：9.09秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 107 张图片: 684b520ec0bf441cacbe77e1f01b79bd_jpg.rf.37f6e85d616d16db1f62a8851f93eb09_with_roboflow_yolo.jpg

==================================================
![684b520ec0bf441cacbe77e1f01b79bd_jpg.rf.37f6e85d616d16db1f62a8851f93eb09_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/684b520ec0bf441cacbe77e1f01b79bd_jpg.rf.37f6e85d616d16db1f62a8851f93eb09_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：8.88秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 108 张图片: 6a0e49c95a414167bc0214657c9a70b0_jpg.rf.54fedf92eb177a62732c065586daf05c_with_roboflow_yolo.jpg

==================================================
![6a0e49c95a414167bc0214657c9a70b0_jpg.rf.54fedf92eb177a62732c065586daf05c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6a0e49c95a414167bc0214657c9a70b0_jpg.rf.54fedf92eb177a62732c065586daf05c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.51秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 109 张图片: 6b0f8edfe0f449198fe03b631e0dbde5_jpg.rf.df5a5d21fe9a2701e09db93469f7c809_with_roboflow_yolo.jpg

==================================================
![6b0f8edfe0f449198fe03b631e0dbde5_jpg.rf.df5a5d21fe9a2701e09db93469f7c809_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6b0f8edfe0f449198fe03b631e0dbde5_jpg.rf.df5a5d21fe9a2701e09db93469f7c809_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：23.73秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 110 张图片: 6bf49678b55049f88a5489baa080317d_jpg.rf.b573bf2c4d0e990d2cdc07f8fc19face_with_roboflow_yolo.jpg

==================================================
![6bf49678b55049f88a5489baa080317d_jpg.rf.b573bf2c4d0e990d2cdc07f8fc19face_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6bf49678b55049f88a5489baa080317d_jpg.rf.b573bf2c4d0e990d2cdc07f8fc19face_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][×]"}
```
### 响应时间：8.42秒
### token用量
- total_tokens: 563
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 111 张图片: 6d86804967a846a194cf3c9397663f0b_jpg.rf.8f6bc6de4924b86c2d1f94d223023009_with_roboflow_yolo.jpg

==================================================
![6d86804967a846a194cf3c9397663f0b_jpg.rf.8f6bc6de4924b86c2d1f94d223023009_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6d86804967a846a194cf3c9397663f0b_jpg.rf.8f6bc6de4924b86c2d1f94d223023009_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.26秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 112 张图片: 6e31853188e349a59dc3b0be3bb73b19_jpg.rf.20c9246407f42f7a1912b650954cd9b4_with_roboflow_yolo.jpg

==================================================
![6e31853188e349a59dc3b0be3bb73b19_jpg.rf.20c9246407f42f7a1912b650954cd9b4_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6e31853188e349a59dc3b0be3bb73b19_jpg.rf.20c9246407f42f7a1912b650954cd9b4_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.38秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 113 张图片: 6eb3ee6fabf54cfd8ec997f6125bba7f_jpg.rf.a70e88b6e439229024462ea65f9a5b79_with_roboflow_yolo.jpg

==================================================
![6eb3ee6fabf54cfd8ec997f6125bba7f_jpg.rf.a70e88b6e439229024462ea65f9a5b79_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6eb3ee6fabf54cfd8ec997f6125bba7f_jpg.rf.a70e88b6e439229024462ea65f9a5b79_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：9.24秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 114 张图片: 6fbb5539f7b84296bc496290c1f77a22_jpg.rf.08c00e3c610a21eadda37da567f9ef21_with_roboflow_yolo.jpg

==================================================
![6fbb5539f7b84296bc496290c1f77a22_jpg.rf.08c00e3c610a21eadda37da567f9ef21_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/6fbb5539f7b84296bc496290c1f77a22_jpg.rf.08c00e3c610a21eadda37da567f9ef21_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.85秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 115 张图片: 70ab5a62030842d6828bfa9df1a3bf96_jpg.rf.1fa5b7dfaa76b61da569a63b926eb12c_with_roboflow_yolo.jpg

==================================================
![70ab5a62030842d6828bfa9df1a3bf96_jpg.rf.1fa5b7dfaa76b61da569a63b926eb12c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/70ab5a62030842d6828bfa9df1a3bf96_jpg.rf.1fa5b7dfaa76b61da569a63b926eb12c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.69秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 116 张图片: 70db0730f46449829d9253b2776b9915_jpg.rf.7b8b55a886d2f3c5771b494e2900540f_with_roboflow_yolo.jpg

==================================================
![70db0730f46449829d9253b2776b9915_jpg.rf.7b8b55a886d2f3c5771b494e2900540f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/70db0730f46449829d9253b2776b9915_jpg.rf.7b8b55a886d2f3c5771b494e2900540f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：25.56秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 117 张图片: 70ee90af5d1f474a9d32fc9b6237b297_jpg.rf.2c0fab145bd4786c3276798bb72e4d83_with_roboflow_yolo.jpg

==================================================
![70ee90af5d1f474a9d32fc9b6237b297_jpg.rf.2c0fab145bd4786c3276798bb72e4d83_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/70ee90af5d1f474a9d32fc9b6237b297_jpg.rf.2c0fab145bd4786c3276798bb72e4d83_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.89秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 118 张图片: 71ec9b2e4e9140079dbb6723ff9eaddc_jpg.rf.7b6fc11dee38c5497e25853bcbb1811d_with_roboflow_yolo.jpg

==================================================
![71ec9b2e4e9140079dbb6723ff9eaddc_jpg.rf.7b6fc11dee38c5497e25853bcbb1811d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/71ec9b2e4e9140079dbb6723ff9eaddc_jpg.rf.7b6fc11dee38c5497e25853bcbb1811d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：8.96秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 119 张图片: 72050910b0a045d2bedeca94d31b1152_jpg.rf.738426095ff27ca7ba95cd937d0452e2_with_roboflow_yolo.jpg

==================================================
![72050910b0a045d2bedeca94d31b1152_jpg.rf.738426095ff27ca7ba95cd937d0452e2_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/72050910b0a045d2bedeca94d31b1152_jpg.rf.738426095ff27ca7ba95cd937d0452e2_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：24.44秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 120 张图片: 724d9a7138e5432c82580e9226887636_jpg.rf.32e73d061b7d52a6900245b3e33a0740_with_roboflow_yolo.jpg

==================================================
![724d9a7138e5432c82580e9226887636_jpg.rf.32e73d061b7d52a6900245b3e33a0740_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/724d9a7138e5432c82580e9226887636_jpg.rf.32e73d061b7d52a6900245b3e33a0740_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.49秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 121 张图片: 733884f1bc3348a8aceb1a85608e7529_jpg.rf.37fe657e4b282e9e7233b2455209e7c1_with_roboflow_yolo.jpg

==================================================
![733884f1bc3348a8aceb1a85608e7529_jpg.rf.37fe657e4b282e9e7233b2455209e7c1_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/733884f1bc3348a8aceb1a85608e7529_jpg.rf.37fe657e4b282e9e7233b2455209e7c1_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：9.24秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 122 张图片: 7426a6d1aa304650bd9fa8f2790ffe61_jpg.rf.35173c0ae61df713aa1b51d04d97c843_with_roboflow_yolo.jpg

==================================================
![7426a6d1aa304650bd9fa8f2790ffe61_jpg.rf.35173c0ae61df713aa1b51d04d97c843_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7426a6d1aa304650bd9fa8f2790ffe61_jpg.rf.35173c0ae61df713aa1b51d04d97c843_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]", "题目6": "[■][×]"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 123 张图片: 743abc1235de414fbd6eab42f8813e6b_jpg.rf.cfa5b5b7efb39e87424d3a4f0a6bb72b_with_roboflow_yolo.jpg

==================================================
![743abc1235de414fbd6eab42f8813e6b_jpg.rf.cfa5b5b7efb39e87424d3a4f0a6bb72b_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/743abc1235de414fbd6eab42f8813e6b_jpg.rf.cfa5b5b7efb39e87424d3a4f0a6bb72b_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.52秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 124 张图片: 74f075a94f8a4b40abf5a0f79f6519b3_jpg.rf.44a513b7f835bd2cc398cfaed3125748_with_roboflow_yolo.jpg

==================================================
![74f075a94f8a4b40abf5a0f79f6519b3_jpg.rf.44a513b7f835bd2cc398cfaed3125748_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/74f075a94f8a4b40abf5a0f79f6519b3_jpg.rf.44a513b7f835bd2cc398cfaed3125748_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.78秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 125 张图片: 751188cd9c88418684f62e2d6c94f669_jpg.rf.085e0296b61b1da5c4009f4b62c0582c_with_roboflow_yolo.jpg

==================================================
![751188cd9c88418684f62e2d6c94f669_jpg.rf.085e0296b61b1da5c4009f4b62c0582c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/751188cd9c88418684f62e2d6c94f669_jpg.rf.085e0296b61b1da5c4009f4b62c0582c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：8.60秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 126 张图片: 754dda75f6084b478eec6975f7343e70_jpg.rf.986696c40467519fee9e80c6125af3bb_with_roboflow_yolo.jpg

==================================================
![754dda75f6084b478eec6975f7343e70_jpg.rf.986696c40467519fee9e80c6125af3bb_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/754dda75f6084b478eec6975f7343e70_jpg.rf.986696c40467519fee9e80c6125af3bb_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：16.69秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 127 张图片: 756bc341ec524ac491334470421f2dab_jpg.rf.f70aac9f64fd16d02c39d075fc90270d_with_roboflow_yolo.jpg

==================================================
![756bc341ec524ac491334470421f2dab_jpg.rf.f70aac9f64fd16d02c39d075fc90270d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/756bc341ec524ac491334470421f2dab_jpg.rf.f70aac9f64fd16d02c39d075fc90270d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": ""}
```
### 响应时间：1.24秒
### token用量
- total_tokens: 533
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 128 张图片: 7801b0be4db048d893f1615343bc28f1_jpg.rf.0458204f7034e354d5776dcb3c6cf4c7_with_roboflow_yolo.jpg

==================================================
![7801b0be4db048d893f1615343bc28f1_jpg.rf.0458204f7034e354d5776dcb3c6cf4c7_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7801b0be4db048d893f1615343bc28f1_jpg.rf.0458204f7034e354d5776dcb3c6cf4c7_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：9.35秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 129 张图片: 7957e76ff2ed4080a5618ad0c4df35a0_jpg.rf.e83e6cd878509afabf2c46be02b7e9e0_with_roboflow_yolo.jpg

==================================================
![7957e76ff2ed4080a5618ad0c4df35a0_jpg.rf.e83e6cd878509afabf2c46be02b7e9e0_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7957e76ff2ed4080a5618ad0c4df35a0_jpg.rf.e83e6cd878509afabf2c46be02b7e9e0_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：16.96秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 130 张图片: 7aead93d5dc042b1a4469321ef7469c0_jpg.rf.9e0a8f76d74493511160612cd735962f_with_roboflow_yolo.jpg

==================================================
![7aead93d5dc042b1a4469321ef7469c0_jpg.rf.9e0a8f76d74493511160612cd735962f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7aead93d5dc042b1a4469321ef7469c0_jpg.rf.9e0a8f76d74493511160612cd735962f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][×]", "题目3": "[√][■]", "题目4": "[√][×]", "题目5": "[√][×]", "题目6": "[√][■]"}
```
### 响应时间：8.93秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 131 张图片: 7b7ae77a6c6f426486291b763645368f_jpg.rf.4dc71e4294f4fb8da897327ae6f8cfeb_with_roboflow_yolo.jpg

==================================================
![7b7ae77a6c6f426486291b763645368f_jpg.rf.4dc71e4294f4fb8da897327ae6f8cfeb_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7b7ae77a6c6f426486291b763645368f_jpg.rf.4dc71e4294f4fb8da897327ae6f8cfeb_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：17.03秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 132 张图片: 7bc783c2bb624a908fa2da36c93b3fe4_jpg.rf.fd8533f971b75e9c264c28d705af66b5_with_roboflow_yolo.jpg

==================================================
![7bc783c2bb624a908fa2da36c93b3fe4_jpg.rf.fd8533f971b75e9c264c28d705af66b5_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7bc783c2bb624a908fa2da36c93b3fe4_jpg.rf.fd8533f971b75e9c264c28d705af66b5_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：1.90秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 133 张图片: 7cde81eaeba44f8a88e1680a232f3bbb_jpg.rf.0fb5bf56bce32c430629094582994bff_with_roboflow_yolo.jpg

==================================================
![7cde81eaeba44f8a88e1680a232f3bbb_jpg.rf.0fb5bf56bce32c430629094582994bff_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7cde81eaeba44f8a88e1680a232f3bbb_jpg.rf.0fb5bf56bce32c430629094582994bff_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：9.42秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 134 张图片: 7d410e072075456ea50422502463d83e_jpg.rf.89799b938265417ffc823e82affb13f1_with_roboflow_yolo.jpg

==================================================
![7d410e072075456ea50422502463d83e_jpg.rf.89799b938265417ffc823e82affb13f1_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7d410e072075456ea50422502463d83e_jpg.rf.89799b938265417ffc823e82affb13f1_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 135 张图片: 7d913f7fd726477ab9e11d0729ba4861_jpg.rf.81bda3f6ebf648457480b38a813e23c3_with_roboflow_yolo.jpg

==================================================
![7d913f7fd726477ab9e11d0729ba4861_jpg.rf.81bda3f6ebf648457480b38a813e23c3_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7d913f7fd726477ab9e11d0729ba4861_jpg.rf.81bda3f6ebf648457480b38a813e23c3_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 136 张图片: 7f0d2af146fa499a9146d0c36bdf1f4d_jpg.rf.f5b053018c7f2e1d7fd640a9b4b06f77_with_roboflow_yolo.jpg

==================================================
![7f0d2af146fa499a9146d0c36bdf1f4d_jpg.rf.f5b053018c7f2e1d7fd640a9b4b06f77_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/7f0d2af146fa499a9146d0c36bdf1f4d_jpg.rf.f5b053018c7f2e1d7fd640a9b4b06f77_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]"}
```
### 响应时间：1.76秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 137 张图片: 80afe4bd97f3460b85636926eae3e3db_jpg.rf.828b2ed4392ab619d902e57b4b4efdf5_with_roboflow_yolo.jpg

==================================================
![80afe4bd97f3460b85636926eae3e3db_jpg.rf.828b2ed4392ab619d902e57b4b4efdf5_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/80afe4bd97f3460b85636926eae3e3db_jpg.rf.828b2ed4392ab619d902e57b4b4efdf5_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.68秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 138 张图片: 81336f3bc1c74234bc22ea3574c7baf4_jpg.rf.d2e368658b301ae87cc7928248b396ed_with_roboflow_yolo.jpg

==================================================
![81336f3bc1c74234bc22ea3574c7baf4_jpg.rf.d2e368658b301ae87cc7928248b396ed_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/81336f3bc1c74234bc22ea3574c7baf4_jpg.rf.d2e368658b301ae87cc7928248b396ed_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]"}
```
### 响应时间：1.68秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 139 张图片: 8290820733444f49b5f7f23468f47e09_jpg.rf.eedf3dd91e577cdebd69bc8244f5ba79_with_roboflow_yolo.jpg

==================================================
![8290820733444f49b5f7f23468f47e09_jpg.rf.eedf3dd91e577cdebd69bc8244f5ba79_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/8290820733444f49b5f7f23468f47e09_jpg.rf.eedf3dd91e577cdebd69bc8244f5ba79_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：2.28秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 140 张图片: 856888df2e414a6484b3760e68dfc339_jpg.rf.ee774857f02350249fc0b08b011a185d_with_roboflow_yolo.jpg

==================================================
![856888df2e414a6484b3760e68dfc339_jpg.rf.ee774857f02350249fc0b08b011a185d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/856888df2e414a6484b3760e68dfc339_jpg.rf.ee774857f02350249fc0b08b011a185d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.48秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 141 张图片: 86a0cf6c10764e4990827f22d3435e10_jpg.rf.40141cebd98b2e450839ed52d13c2e68_with_roboflow_yolo.jpg

==================================================
![86a0cf6c10764e4990827f22d3435e10_jpg.rf.40141cebd98b2e450839ed52d13c2e68_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/86a0cf6c10764e4990827f22d3435e10_jpg.rf.40141cebd98b2e450839ed52d13c2e68_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：17.26秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 142 张图片: 879e0862e2fc46ee9dba1e48ab2f80d8_jpg.rf.23a83c6312d0089534a50855c6dd122c_with_roboflow_yolo.jpg

==================================================
![879e0862e2fc46ee9dba1e48ab2f80d8_jpg.rf.23a83c6312d0089534a50855c6dd122c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/879e0862e2fc46ee9dba1e48ab2f80d8_jpg.rf.23a83c6312d0089534a50855c6dd122c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.57秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 143 张图片: 89c0b31a70824d5b98ba6e8a0d2a380e_jpg.rf.7ecd5fd21cf808d33fedc0413947111a_with_roboflow_yolo.jpg

==================================================
![89c0b31a70824d5b98ba6e8a0d2a380e_jpg.rf.7ecd5fd21cf808d33fedc0413947111a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/89c0b31a70824d5b98ba6e8a0d2a380e_jpg.rf.7ecd5fd21cf808d33fedc0413947111a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：1.76秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 144 张图片: 8b9500f9d50943c5a45bd31327b719a9_jpg.rf.43937631d589692748ea1c0bdc45583d_with_roboflow_yolo.jpg

==================================================
![8b9500f9d50943c5a45bd31327b719a9_jpg.rf.43937631d589692748ea1c0bdc45583d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/8b9500f9d50943c5a45bd31327b719a9_jpg.rf.43937631d589692748ea1c0bdc45583d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：17.33秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 145 张图片: 8c6f9f42ccf642f6980786504bc7b405_jpg.rf.9295038514f869de22c347d1050acc75_with_roboflow_yolo.jpg

==================================================
![8c6f9f42ccf642f6980786504bc7b405_jpg.rf.9295038514f869de22c347d1050acc75_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/8c6f9f42ccf642f6980786504bc7b405_jpg.rf.9295038514f869de22c347d1050acc75_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.75秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 146 张图片: 8c7d53e58da345dd84467aec0719b7f3_jpg.rf.8768699dfa9a42f98b406567d1031768_with_roboflow_yolo.jpg

==================================================
![8c7d53e58da345dd84467aec0719b7f3_jpg.rf.8768699dfa9a42f98b406567d1031768_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/8c7d53e58da345dd84467aec0719b7f3_jpg.rf.8768699dfa9a42f98b406567d1031768_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.78秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 147 张图片: 919256ec2b06454288321721183f9e63_jpg.rf.d40c7cd3949a556b6bfac8deb3911478_with_roboflow_yolo.jpg

==================================================
![919256ec2b06454288321721183f9e63_jpg.rf.d40c7cd3949a556b6bfac8deb3911478_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/919256ec2b06454288321721183f9e63_jpg.rf.d40c7cd3949a556b6bfac8deb3911478_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：17.43秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 148 张图片: 925966b5a175404dbe1e833195eede8a_jpg.rf.da084340cc227ada93a63e2575e55e69_with_roboflow_yolo.jpg

==================================================
![925966b5a175404dbe1e833195eede8a_jpg.rf.da084340cc227ada93a63e2575e55e69_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/925966b5a175404dbe1e833195eede8a_jpg.rf.da084340cc227ada93a63e2575e55e69_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.06秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 149 张图片: 92c54a626d0f44ec979cf6814259e171_jpg.rf.aa3611d68978fa832f70154647608f45_with_roboflow_yolo.jpg

==================================================
![92c54a626d0f44ec979cf6814259e171_jpg.rf.aa3611d68978fa832f70154647608f45_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/92c54a626d0f44ec979cf6814259e171_jpg.rf.aa3611d68978fa832f70154647608f45_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 150 张图片: 93884f6bd2d2484197e7617089b875d9_jpg.rf.72b6cf28488320b5cb4e2606cd77091c_with_roboflow_yolo.jpg

==================================================
![93884f6bd2d2484197e7617089b875d9_jpg.rf.72b6cf28488320b5cb4e2606cd77091c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/93884f6bd2d2484197e7617089b875d9_jpg.rf.72b6cf28488320b5cb4e2606cd77091c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：1.75秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 151 张图片: 941dc715240546e49615dcaaa3abeff9_jpg.rf.03d172d6a3e93565b3458fdc703f60f6_with_roboflow_yolo.jpg

==================================================
![941dc715240546e49615dcaaa3abeff9_jpg.rf.03d172d6a3e93565b3458fdc703f60f6_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/941dc715240546e49615dcaaa3abeff9_jpg.rf.03d172d6a3e93565b3458fdc703f60f6_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：2.02秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 152 张图片: 94c4752ee5fe492ebc473e65c6521406_jpg.rf.78be108e10371d418e2409cce932a6eb_with_roboflow_yolo.jpg

==================================================
![94c4752ee5fe492ebc473e65c6521406_jpg.rf.78be108e10371d418e2409cce932a6eb_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/94c4752ee5fe492ebc473e65c6521406_jpg.rf.78be108e10371d418e2409cce932a6eb_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：17.47秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 153 张图片: 94d247dcc62d4646bd0e886787e9351b_jpg.rf.ce83c4167f4c6292af1f35165403ca0f_with_roboflow_yolo.jpg

==================================================
![94d247dcc62d4646bd0e886787e9351b_jpg.rf.ce83c4167f4c6292af1f35165403ca0f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/94d247dcc62d4646bd0e886787e9351b_jpg.rf.ce83c4167f4c6292af1f35165403ca0f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.75秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 154 张图片: 96875f2295b647d5a7fe5b950356e698_jpg.rf.2b988ee99a8445bf873c2814f0f846fd_with_roboflow_yolo.jpg

==================================================
![96875f2295b647d5a7fe5b950356e698_jpg.rf.2b988ee99a8445bf873c2814f0f846fd_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/96875f2295b647d5a7fe5b950356e698_jpg.rf.2b988ee99a8445bf873c2814f0f846fd_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：17.42秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 155 张图片: 96c9f32136954690bc4840ed8a10ac68_jpg.rf.8f7843531a57034f4ab7f2e6b4aaa47d_with_roboflow_yolo.jpg

==================================================
![96c9f32136954690bc4840ed8a10ac68_jpg.rf.8f7843531a57034f4ab7f2e6b4aaa47d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/96c9f32136954690bc4840ed8a10ac68_jpg.rf.8f7843531a57034f4ab7f2e6b4aaa47d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.19秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 156 张图片: 96fb3aa727604e529628b6dbb085883d_jpg.rf.5943e811a0e42e8be4f66857fbf373a6_with_roboflow_yolo.jpg

==================================================
![96fb3aa727604e529628b6dbb085883d_jpg.rf.5943e811a0e42e8be4f66857fbf373a6_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/96fb3aa727604e529628b6dbb085883d_jpg.rf.5943e811a0e42e8be4f66857fbf373a6_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 157 张图片: 9759d8481beb4fdbb1a9c16b0e5b2de8_jpg.rf.d539cd47e2db4a6a49821a1dbd32ab6c_with_roboflow_yolo.jpg

==================================================
![9759d8481beb4fdbb1a9c16b0e5b2de8_jpg.rf.d539cd47e2db4a6a49821a1dbd32ab6c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/9759d8481beb4fdbb1a9c16b0e5b2de8_jpg.rf.d539cd47e2db4a6a49821a1dbd32ab6c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：17.36秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 158 张图片: 97d034c30feb41618645e4f9815ef5cf_jpg.rf.2aecedae6749322b5360b69155eb8e0c_with_roboflow_yolo.jpg

==================================================
![97d034c30feb41618645e4f9815ef5cf_jpg.rf.2aecedae6749322b5360b69155eb8e0c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/97d034c30feb41618645e4f9815ef5cf_jpg.rf.2aecedae6749322b5360b69155eb8e0c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][×]"}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 159 张图片: 9c27a24c2d0e4c3087bfcf0c70044370_jpg.rf.b7d30b8907e2e6c80613b391d4939df5_with_roboflow_yolo.jpg

==================================================
![9c27a24c2d0e4c3087bfcf0c70044370_jpg.rf.b7d30b8907e2e6c80613b391d4939df5_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/9c27a24c2d0e4c3087bfcf0c70044370_jpg.rf.b7d30b8907e2e6c80613b391d4939df5_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 160 张图片: 9c4b6cb76d6c4a598cac25776bd86bcc_jpg.rf.2e8901c939dd06b3c1e96bfe2db28584_with_roboflow_yolo.jpg

==================================================
![9c4b6cb76d6c4a598cac25776bd86bcc_jpg.rf.2e8901c939dd06b3c1e96bfe2db28584_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/9c4b6cb76d6c4a598cac25776bd86bcc_jpg.rf.2e8901c939dd06b3c1e96bfe2db28584_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.92秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 161 张图片: 9c6ea595f12c477599c34eaefd25fed9_jpg.rf.a0b23fc920ccfabd1a80dcf883d26eaa_with_roboflow_yolo.jpg

==================================================
![9c6ea595f12c477599c34eaefd25fed9_jpg.rf.a0b23fc920ccfabd1a80dcf883d26eaa_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/9c6ea595f12c477599c34eaefd25fed9_jpg.rf.a0b23fc920ccfabd1a80dcf883d26eaa_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.65秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 162 张图片: 9c9e9fff0dae4908a9a6721155fc5dd2_jpg.rf.709e7bcff72f35064cd0ed6329ac5499_with_roboflow_yolo.jpg

==================================================
![9c9e9fff0dae4908a9a6721155fc5dd2_jpg.rf.709e7bcff72f35064cd0ed6329ac5499_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/9c9e9fff0dae4908a9a6721155fc5dd2_jpg.rf.709e7bcff72f35064cd0ed6329ac5499_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：8.03秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 163 张图片: 9e20bf0d5065460b96aadaed5768b65c_jpg.rf.bb615aabc8376aa6c2e13fb34b4b132c_with_roboflow_yolo.jpg

==================================================
![9e20bf0d5065460b96aadaed5768b65c_jpg.rf.bb615aabc8376aa6c2e13fb34b4b132c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/9e20bf0d5065460b96aadaed5768b65c_jpg.rf.bb615aabc8376aa6c2e13fb34b4b132c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：16.86秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 164 张图片: a38cb923983c46f0bbe8d1ae49f818dd_jpg.rf.75f295c156ecfbd985830ed47a5c5247_with_roboflow_yolo.jpg

==================================================
![a38cb923983c46f0bbe8d1ae49f818dd_jpg.rf.75f295c156ecfbd985830ed47a5c5247_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/a38cb923983c46f0bbe8d1ae49f818dd_jpg.rf.75f295c156ecfbd985830ed47a5c5247_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 165 张图片: a63a2435c3ff4b63918a7d2a64052ec5_jpg.rf.9bbf545ebfd411526bc8014a17ad1765_with_roboflow_yolo.jpg

==================================================
![a63a2435c3ff4b63918a7d2a64052ec5_jpg.rf.9bbf545ebfd411526bc8014a17ad1765_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/a63a2435c3ff4b63918a7d2a64052ec5_jpg.rf.9bbf545ebfd411526bc8014a17ad1765_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：2.00秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 166 张图片: a77472aa7d534c1da4f58aa1da08c324_jpg.rf.73774c370c111bfab59d6ec370c745ee_with_roboflow_yolo.jpg

==================================================
![a77472aa7d534c1da4f58aa1da08c324_jpg.rf.73774c370c111bfab59d6ec370c745ee_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/a77472aa7d534c1da4f58aa1da08c324_jpg.rf.73774c370c111bfab59d6ec370c745ee_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：17.04秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 167 张图片: a81bf78c579c47c3b4846a55b42accd7_jpg.rf.ccba748b88c2ef8f295f07a8ccb25990_with_roboflow_yolo.jpg

==================================================
![a81bf78c579c47c3b4846a55b42accd7_jpg.rf.ccba748b88c2ef8f295f07a8ccb25990_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/a81bf78c579c47c3b4846a55b42accd7_jpg.rf.ccba748b88c2ef8f295f07a8ccb25990_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]"}
```
### 响应时间：2.17秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 168 张图片: ab2bca4053df4c84837f467a830de581_jpg.rf.d477caf5050fb39f971b75c609619c68_with_roboflow_yolo.jpg

==================================================
![ab2bca4053df4c84837f467a830de581_jpg.rf.d477caf5050fb39f971b75c609619c68_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/ab2bca4053df4c84837f467a830de581_jpg.rf.d477caf5050fb39f971b75c609619c68_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]", "题目6": "[■][×]"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 169 张图片: b3198a2816d141858d71cf2de759cabb_jpg.rf.b67951dfc8a409dd1dbb26ad5b1e6a7a_with_roboflow_yolo.jpg

==================================================
![b3198a2816d141858d71cf2de759cabb_jpg.rf.b67951dfc8a409dd1dbb26ad5b1e6a7a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b3198a2816d141858d71cf2de759cabb_jpg.rf.b67951dfc8a409dd1dbb26ad5b1e6a7a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.00秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 170 张图片: b46c27ba45e54f99a2f65c6d2a866c04_jpg.rf.17df2a71a2b88ad12ee05709836912ae_with_roboflow_yolo.jpg

==================================================
![b46c27ba45e54f99a2f65c6d2a866c04_jpg.rf.17df2a71a2b88ad12ee05709836912ae_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b46c27ba45e54f99a2f65c6d2a866c04_jpg.rf.17df2a71a2b88ad12ee05709836912ae_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：9.10秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 171 张图片: b4b06cf367a24fc48a926f300e94da6b_jpg.rf.433952a4b7d76c34bca263d7a5b5bf28_with_roboflow_yolo.jpg

==================================================
![b4b06cf367a24fc48a926f300e94da6b_jpg.rf.433952a4b7d76c34bca263d7a5b5bf28_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b4b06cf367a24fc48a926f300e94da6b_jpg.rf.433952a4b7d76c34bca263d7a5b5bf28_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 172 张图片: b4d2df609fac4536b2f66df7b79165d7_jpg.rf.5ad6b6982d9980eaf6933daa93653297_with_roboflow_yolo.jpg

==================================================
![b4d2df609fac4536b2f66df7b79165d7_jpg.rf.5ad6b6982d9980eaf6933daa93653297_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b4d2df609fac4536b2f66df7b79165d7_jpg.rf.5ad6b6982d9980eaf6933daa93653297_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.30秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 173 张图片: b4e565e90ab44af0b38ea557303d0aa6_jpg.rf.8691836f381ef95547a3a9c805e02308_with_roboflow_yolo.jpg

==================================================
![b4e565e90ab44af0b38ea557303d0aa6_jpg.rf.8691836f381ef95547a3a9c805e02308_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b4e565e90ab44af0b38ea557303d0aa6_jpg.rf.8691836f381ef95547a3a9c805e02308_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：8.38秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 174 张图片: b61552bf01de49668392093cbf3a8ed0_jpg.rf.f1d25535e487d7143991404fb3b7ac00_with_roboflow_yolo.jpg

==================================================
![b61552bf01de49668392093cbf3a8ed0_jpg.rf.f1d25535e487d7143991404fb3b7ac00_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b61552bf01de49668392093cbf3a8ed0_jpg.rf.f1d25535e487d7143991404fb3b7ac00_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.75秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 175 张图片: b72d3e843ded4ad095962727a95f9c50_jpg.rf.224ef486c705a3be9d0f49de24d510ed_with_roboflow_yolo.jpg

==================================================
![b72d3e843ded4ad095962727a95f9c50_jpg.rf.224ef486c705a3be9d0f49de24d510ed_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b72d3e843ded4ad095962727a95f9c50_jpg.rf.224ef486c705a3be9d0f49de24d510ed_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：17.26秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 176 张图片: b73b4377846640cf8bfbeb0a652b46c3_jpg.rf.cdc2dafbe648a1448994319823ca0215_with_roboflow_yolo.jpg

==================================================
![b73b4377846640cf8bfbeb0a652b46c3_jpg.rf.cdc2dafbe648a1448994319823ca0215_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b73b4377846640cf8bfbeb0a652b46c3_jpg.rf.cdc2dafbe648a1448994319823ca0215_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.25秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 177 张图片: b7938463f8c1489fbce957dd083e5a3b_jpg.rf.a735b3b2cbddcfaac185be8738ea57e3_with_roboflow_yolo.jpg

==================================================
![b7938463f8c1489fbce957dd083e5a3b_jpg.rf.a735b3b2cbddcfaac185be8738ea57e3_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b7938463f8c1489fbce957dd083e5a3b_jpg.rf.a735b3b2cbddcfaac185be8738ea57e3_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.93秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 178 张图片: b7abc764555a41caab557b3b1a8f805e_jpg.rf.ebeff6f8b3119fe8fc76bd9c7d32e31a_with_roboflow_yolo.jpg

==================================================
![b7abc764555a41caab557b3b1a8f805e_jpg.rf.ebeff6f8b3119fe8fc76bd9c7d32e31a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b7abc764555a41caab557b3b1a8f805e_jpg.rf.ebeff6f8b3119fe8fc76bd9c7d32e31a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 179 张图片: b9cc6a5a5d7546f08561a5f68f1684fa_jpg.rf.003c1b7ab60320bce461dd2aeefac6a3_with_roboflow_yolo.jpg

==================================================
![b9cc6a5a5d7546f08561a5f68f1684fa_jpg.rf.003c1b7ab60320bce461dd2aeefac6a3_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/b9cc6a5a5d7546f08561a5f68f1684fa_jpg.rf.003c1b7ab60320bce461dd2aeefac6a3_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.19秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 180 张图片: ba4e49b664a94ad38e053ff50479ff90_jpg.rf.7d97300562f7ab50d3c0f7ff3c67ed0e_with_roboflow_yolo.jpg

==================================================
![ba4e49b664a94ad38e053ff50479ff90_jpg.rf.7d97300562f7ab50d3c0f7ff3c67ed0e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/ba4e49b664a94ad38e053ff50479ff90_jpg.rf.7d97300562f7ab50d3c0f7ff3c67ed0e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 181 张图片: bac867b38cae47808f7e9dad868d4c1a_jpg.rf.127e6c38cc6a7f332b68f70f6398731e_with_roboflow_yolo.jpg

==================================================
![bac867b38cae47808f7e9dad868d4c1a_jpg.rf.127e6c38cc6a7f332b68f70f6398731e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/bac867b38cae47808f7e9dad868d4c1a_jpg.rf.127e6c38cc6a7f332b68f70f6398731e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 182 张图片: bb42e15f3d4e4f699bd6da8d06704190_jpg.rf.fbe76aa8497a12ccfcc1e8189d0bec9f_with_roboflow_yolo.jpg

==================================================
![bb42e15f3d4e4f699bd6da8d06704190_jpg.rf.fbe76aa8497a12ccfcc1e8189d0bec9f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/bb42e15f3d4e4f699bd6da8d06704190_jpg.rf.fbe76aa8497a12ccfcc1e8189d0bec9f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 183 张图片: be42c206ecd04ac1bce46f64ff7ab026_jpg.rf.60c9d399e18b10f3877e514c5dd2546e_with_roboflow_yolo.jpg

==================================================
![be42c206ecd04ac1bce46f64ff7ab026_jpg.rf.60c9d399e18b10f3877e514c5dd2546e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/be42c206ecd04ac1bce46f64ff7ab026_jpg.rf.60c9d399e18b10f3877e514c5dd2546e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.47秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 184 张图片: bf5827e97f504213a37a25550f50cf94_jpg.rf.9f5991166b8f54d831c337a179251a2f_with_roboflow_yolo.jpg

==================================================
![bf5827e97f504213a37a25550f50cf94_jpg.rf.9f5991166b8f54d831c337a179251a2f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/bf5827e97f504213a37a25550f50cf94_jpg.rf.9f5991166b8f54d831c337a179251a2f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]", "题目6": "[√][■]"}
```
### 响应时间：1.94秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 185 张图片: bf6efc268cdd4c9d80e79a01a53011c1_jpg.rf.af94fcbac363faa203748b1eaed6f6e7_with_roboflow_yolo.jpg

==================================================
![bf6efc268cdd4c9d80e79a01a53011c1_jpg.rf.af94fcbac363faa203748b1eaed6f6e7_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/bf6efc268cdd4c9d80e79a01a53011c1_jpg.rf.af94fcbac363faa203748b1eaed6f6e7_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：2.80秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 186 张图片: c1fde056fffe468dbeb13043fcac9efb_jpg.rf.032f25c655f90f2939912c3211f48977_with_roboflow_yolo.jpg

==================================================
![c1fde056fffe468dbeb13043fcac9efb_jpg.rf.032f25c655f90f2939912c3211f48977_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c1fde056fffe468dbeb13043fcac9efb_jpg.rf.032f25c655f90f2939912c3211f48977_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 187 张图片: c32da7989d2c4075a603615de16e6f69_jpg.rf.6b98369ad46c076eebbbb525f6d8d41e_with_roboflow_yolo.jpg

==================================================
![c32da7989d2c4075a603615de16e6f69_jpg.rf.6b98369ad46c076eebbbb525f6d8d41e_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c32da7989d2c4075a603615de16e6f69_jpg.rf.6b98369ad46c076eebbbb525f6d8d41e_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.60秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 188 张图片: c37303b8a9ad467eb6847ada01441673_jpg.rf.a992b808c15f40d0d6d695944d3a4a69_with_roboflow_yolo.jpg

==================================================
![c37303b8a9ad467eb6847ada01441673_jpg.rf.a992b808c15f40d0d6d695944d3a4a69_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c37303b8a9ad467eb6847ada01441673_jpg.rf.a992b808c15f40d0d6d695944d3a4a69_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.84秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 189 张图片: c3f38540d18b4bada1d0236b4c5bfef7_jpg.rf.7263f11f0141c756e9b2f01c8fdf3703_with_roboflow_yolo.jpg

==================================================
![c3f38540d18b4bada1d0236b4c5bfef7_jpg.rf.7263f11f0141c756e9b2f01c8fdf3703_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c3f38540d18b4bada1d0236b4c5bfef7_jpg.rf.7263f11f0141c756e9b2f01c8fdf3703_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 190 张图片: c40fb18e9072419091c4f8ef7f78740f_jpg.rf.417216508e0c78b1d77c95a6ef282f11_with_roboflow_yolo.jpg

==================================================
![c40fb18e9072419091c4f8ef7f78740f_jpg.rf.417216508e0c78b1d77c95a6ef282f11_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c40fb18e9072419091c4f8ef7f78740f_jpg.rf.417216508e0c78b1d77c95a6ef282f11_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.10秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 191 张图片: c4b70db630ce4c72bb3fd188c954d15c_jpg.rf.ad674133baf6a165b4e4cbd382e7003c_with_roboflow_yolo.jpg

==================================================
![c4b70db630ce4c72bb3fd188c954d15c_jpg.rf.ad674133baf6a165b4e4cbd382e7003c_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c4b70db630ce4c72bb3fd188c954d15c_jpg.rf.ad674133baf6a165b4e4cbd382e7003c_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：3.07秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 192 张图片: c597bef7758b4eeeab06973b520c240d_jpg.rf.ebe5d597bb30dd8e36d7d8f013e2e336_with_roboflow_yolo.jpg

==================================================
![c597bef7758b4eeeab06973b520c240d_jpg.rf.ebe5d597bb30dd8e36d7d8f013e2e336_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c597bef7758b4eeeab06973b520c240d_jpg.rf.ebe5d597bb30dd8e36d7d8f013e2e336_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.94秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 193 张图片: c7667f1fae934e77a706c193cfc4b393_jpg.rf.5e75177d5d2d3d49c31c896f7463727b_with_roboflow_yolo.jpg

==================================================
![c7667f1fae934e77a706c193cfc4b393_jpg.rf.5e75177d5d2d3d49c31c896f7463727b_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c7667f1fae934e77a706c193cfc4b393_jpg.rf.5e75177d5d2d3d49c31c896f7463727b_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.98秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 194 张图片: c82909f8e1844cd986872291bb3b67a6_jpg.rf.b792344ac3f93bae6db300cb85b5c291_with_roboflow_yolo.jpg

==================================================
![c82909f8e1844cd986872291bb3b67a6_jpg.rf.b792344ac3f93bae6db300cb85b5c291_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/c82909f8e1844cd986872291bb3b67a6_jpg.rf.b792344ac3f93bae6db300cb85b5c291_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.18秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 195 张图片: ca3b2c39bf234947bddbd1009fde4dbb_jpg.rf.697c643be7c16daaddbeadec19e0d637_with_roboflow_yolo.jpg

==================================================
![ca3b2c39bf234947bddbd1009fde4dbb_jpg.rf.697c643be7c16daaddbeadec19e0d637_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/ca3b2c39bf234947bddbd1009fde4dbb_jpg.rf.697c643be7c16daaddbeadec19e0d637_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 196 张图片: cbcd463994fe4bc8a8d5aaf5c799b64d_jpg.rf.d3b359323216d10cab8696bbfd6b8bdc_with_roboflow_yolo.jpg

==================================================
![cbcd463994fe4bc8a8d5aaf5c799b64d_jpg.rf.d3b359323216d10cab8696bbfd6b8bdc_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/cbcd463994fe4bc8a8d5aaf5c799b64d_jpg.rf.d3b359323216d10cab8696bbfd6b8bdc_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 197 张图片: cd50790ba23546cba2425cc5366a4376_jpg.rf.cb1f92536b7a2109acadd85bea7be897_with_roboflow_yolo.jpg

==================================================
![cd50790ba23546cba2425cc5366a4376_jpg.rf.cb1f92536b7a2109acadd85bea7be897_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/cd50790ba23546cba2425cc5366a4376_jpg.rf.cb1f92536b7a2109acadd85bea7be897_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 198 张图片: cf2fceba70c441a9a8a4baa2b55d0c97_jpg.rf.a5bdc4ab61ffb76ad785652923c69a04_with_roboflow_yolo.jpg

==================================================
![cf2fceba70c441a9a8a4baa2b55d0c97_jpg.rf.a5bdc4ab61ffb76ad785652923c69a04_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/cf2fceba70c441a9a8a4baa2b55d0c97_jpg.rf.a5bdc4ab61ffb76ad785652923c69a04_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.28秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 199 张图片: d06f2d45b1c442628251da4e71e2fc3d_jpg.rf.29763621348ea698a608dd9fdf01dcf8_with_roboflow_yolo.jpg

==================================================
![d06f2d45b1c442628251da4e71e2fc3d_jpg.rf.29763621348ea698a608dd9fdf01dcf8_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d06f2d45b1c442628251da4e71e2fc3d_jpg.rf.29763621348ea698a608dd9fdf01dcf8_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.94秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 200 张图片: d1727a358cdb450b850b547835f59daa_jpg.rf.30d8797d8016c60c8f31810ca4b30b95_with_roboflow_yolo.jpg

==================================================
![d1727a358cdb450b850b547835f59daa_jpg.rf.30d8797d8016c60c8f31810ca4b30b95_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d1727a358cdb450b850b547835f59daa_jpg.rf.30d8797d8016c60c8f31810ca4b30b95_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 201 张图片: d30ee6a359a746f690b4d0aea0cce3f7_jpg.rf.b831b7c5342e908e35d7337ab9290172_with_roboflow_yolo.jpg

==================================================
![d30ee6a359a746f690b4d0aea0cce3f7_jpg.rf.b831b7c5342e908e35d7337ab9290172_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d30ee6a359a746f690b4d0aea0cce3f7_jpg.rf.b831b7c5342e908e35d7337ab9290172_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.35秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 202 张图片: d3654db8a59f46a1a5b47e88ea626ba1_jpg.rf.a099b47c52e75aef3fa5237d1055701d_with_roboflow_yolo.jpg

==================================================
![d3654db8a59f46a1a5b47e88ea626ba1_jpg.rf.a099b47c52e75aef3fa5237d1055701d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d3654db8a59f46a1a5b47e88ea626ba1_jpg.rf.a099b47c52e75aef3fa5237d1055701d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.36秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 203 张图片: d56a81d04d9e46c78e209896c0847c54_jpg.rf.e4afd67a71f4ba978d275089df1c8ae5_with_roboflow_yolo.jpg

==================================================
![d56a81d04d9e46c78e209896c0847c54_jpg.rf.e4afd67a71f4ba978d275089df1c8ae5_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d56a81d04d9e46c78e209896c0847c54_jpg.rf.e4afd67a71f4ba978d275089df1c8ae5_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.11秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 204 张图片: d585b6e98b084e7f87bcff315277b040_jpg.rf.f373d1cbea8c9d8d7d65b674852336a4_with_roboflow_yolo.jpg

==================================================
![d585b6e98b084e7f87bcff315277b040_jpg.rf.f373d1cbea8c9d8d7d65b674852336a4_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d585b6e98b084e7f87bcff315277b040_jpg.rf.f373d1cbea8c9d8d7d65b674852336a4_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 205 张图片: d6f4819bf8d14be7a6e04a994ec98761_jpg.rf.c91b4e34d47762b80e34a587ec7205ce_with_roboflow_yolo.jpg

==================================================
![d6f4819bf8d14be7a6e04a994ec98761_jpg.rf.c91b4e34d47762b80e34a587ec7205ce_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d6f4819bf8d14be7a6e04a994ec98761_jpg.rf.c91b4e34d47762b80e34a587ec7205ce_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：2.03秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 206 张图片: d926aa3d916847d49c488e592b7b7d8c_jpg.rf.1f06ec70f48694d1cca070d0f64db593_with_roboflow_yolo.jpg

==================================================
![d926aa3d916847d49c488e592b7b7d8c_jpg.rf.1f06ec70f48694d1cca070d0f64db593_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d926aa3d916847d49c488e592b7b7d8c_jpg.rf.1f06ec70f48694d1cca070d0f64db593_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]"}
```
### 响应时间：1.76秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 207 张图片: d97ae67c08f142f8807aeb50874d6942_jpg.rf.e3cc5cfcc708500b559c1bacc7d586d5_with_roboflow_yolo.jpg

==================================================
![d97ae67c08f142f8807aeb50874d6942_jpg.rf.e3cc5cfcc708500b559c1bacc7d586d5_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/d97ae67c08f142f8807aeb50874d6942_jpg.rf.e3cc5cfcc708500b559c1bacc7d586d5_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 208 张图片: dbf6b2e22d34429ea1e2e7c838bdb969_jpg.rf.6376bba9e03968c9227580b05e035e11_with_roboflow_yolo.jpg

==================================================
![dbf6b2e22d34429ea1e2e7c838bdb969_jpg.rf.6376bba9e03968c9227580b05e035e11_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/dbf6b2e22d34429ea1e2e7c838bdb969_jpg.rf.6376bba9e03968c9227580b05e035e11_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.98秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 209 张图片: dc1441bfb3a04ab9bcc5fb06ee167cbe_jpg.rf.fbaf9fe0e4512cb3f403b9ccc6301c43_with_roboflow_yolo.jpg

==================================================
![dc1441bfb3a04ab9bcc5fb06ee167cbe_jpg.rf.fbaf9fe0e4512cb3f403b9ccc6301c43_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/dc1441bfb3a04ab9bcc5fb06ee167cbe_jpg.rf.fbaf9fe0e4512cb3f403b9ccc6301c43_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.33秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 210 张图片: dd9c0143fc2c48b5997ae57946c97c2a_jpg.rf.8cff3ec1bf09a6007b7b7692de7de4db_with_roboflow_yolo.jpg

==================================================
![dd9c0143fc2c48b5997ae57946c97c2a_jpg.rf.8cff3ec1bf09a6007b7b7692de7de4db_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/dd9c0143fc2c48b5997ae57946c97c2a_jpg.rf.8cff3ec1bf09a6007b7b7692de7de4db_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：2.47秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 211 张图片: de24cb1509714585b32ff48041088b8b_jpg.rf.3217c8d566cc046baeb9807f610b9bef_with_roboflow_yolo.jpg

==================================================
![de24cb1509714585b32ff48041088b8b_jpg.rf.3217c8d566cc046baeb9807f610b9bef_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/de24cb1509714585b32ff48041088b8b_jpg.rf.3217c8d566cc046baeb9807f610b9bef_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.30秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 212 张图片: df346bc21b154840980ca292bebc0656_jpg.rf.27b163a1d13e2a91d959bcb74ff9644f_with_roboflow_yolo.jpg

==================================================
![df346bc21b154840980ca292bebc0656_jpg.rf.27b163a1d13e2a91d959bcb74ff9644f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/df346bc21b154840980ca292bebc0656_jpg.rf.27b163a1d13e2a91d959bcb74ff9644f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.94秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 213 张图片: e265b77e5c864fc295902d057476076e_jpg.rf.2470155180311b97ed1924367751171f_with_roboflow_yolo.jpg

==================================================
![e265b77e5c864fc295902d057476076e_jpg.rf.2470155180311b97ed1924367751171f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e265b77e5c864fc295902d057476076e_jpg.rf.2470155180311b97ed1924367751171f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.94秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 214 张图片: e3090ef553ae4026ac99c85f618a60d9_jpg.rf.2784747b781ef2323eb86aca84c0e703_with_roboflow_yolo.jpg

==================================================
![e3090ef553ae4026ac99c85f618a60d9_jpg.rf.2784747b781ef2323eb86aca84c0e703_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e3090ef553ae4026ac99c85f618a60d9_jpg.rf.2784747b781ef2323eb86aca84c0e703_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][×]"}
```
### 响应时间：1.21秒
### token用量
- total_tokens: 538
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 215 张图片: e6369060ae91473bbe185cb36566be6c_jpg.rf.577716665bf71db9d725ed7383aece97_with_roboflow_yolo.jpg

==================================================
![e6369060ae91473bbe185cb36566be6c_jpg.rf.577716665bf71db9d725ed7383aece97_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e6369060ae91473bbe185cb36566be6c_jpg.rf.577716665bf71db9d725ed7383aece97_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 216 张图片: e6441e3c088944739e821fc6dc093f61_jpg.rf.53034f31e866f29a06162c79f4676f46_with_roboflow_yolo.jpg

==================================================
![e6441e3c088944739e821fc6dc093f61_jpg.rf.53034f31e866f29a06162c79f4676f46_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e6441e3c088944739e821fc6dc093f61_jpg.rf.53034f31e866f29a06162c79f4676f46_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 217 张图片: e82194163ede4bb5a595cb372a13fb68_jpg.rf.1b432b93a146f643b6c3fd962257209d_with_roboflow_yolo.jpg

==================================================
![e82194163ede4bb5a595cb372a13fb68_jpg.rf.1b432b93a146f643b6c3fd962257209d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e82194163ede4bb5a595cb372a13fb68_jpg.rf.1b432b93a146f643b6c3fd962257209d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.78秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 218 张图片: e824e1f8c94c45a5a24bea1cf28edb1b_jpg.rf.0dc20c42c93e9dd14c2d3df345d9a6d6_with_roboflow_yolo.jpg

==================================================
![e824e1f8c94c45a5a24bea1cf28edb1b_jpg.rf.0dc20c42c93e9dd14c2d3df345d9a6d6_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e824e1f8c94c45a5a24bea1cf28edb1b_jpg.rf.0dc20c42c93e9dd14c2d3df345d9a6d6_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 219 张图片: e836ca7cf8824ffd9442bf9d4c84a996_jpg.rf.b6cad845b77cf8f05ab18e8d487de66a_with_roboflow_yolo.jpg

==================================================
![e836ca7cf8824ffd9442bf9d4c84a996_jpg.rf.b6cad845b77cf8f05ab18e8d487de66a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e836ca7cf8824ffd9442bf9d4c84a996_jpg.rf.b6cad845b77cf8f05ab18e8d487de66a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：1.40秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 220 张图片: e9758cb1ae2c447ba09091f422af50dc_jpg.rf.bdc5a62c03a3ded40807f761045e782d_with_roboflow_yolo.jpg

==================================================
![e9758cb1ae2c447ba09091f422af50dc_jpg.rf.bdc5a62c03a3ded40807f761045e782d_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/e9758cb1ae2c447ba09091f422af50dc_jpg.rf.bdc5a62c03a3ded40807f761045e782d_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 221 张图片: eaaefc703dd84b95984e9fbe4e45b8d8_jpg.rf.f35d3774e49f8f5bedac5d146769a5ac_with_roboflow_yolo.jpg

==================================================
![eaaefc703dd84b95984e9fbe4e45b8d8_jpg.rf.f35d3774e49f8f5bedac5d146769a5ac_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/eaaefc703dd84b95984e9fbe4e45b8d8_jpg.rf.f35d3774e49f8f5bedac5d146769a5ac_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.81秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 222 张图片: ecfcfeb796d542019173ffd2ff7358af_jpg.rf.b00edec552169b4ab272cd6ba68a45c1_with_roboflow_yolo.jpg

==================================================
![ecfcfeb796d542019173ffd2ff7358af_jpg.rf.b00edec552169b4ab272cd6ba68a45c1_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/ecfcfeb796d542019173ffd2ff7358af_jpg.rf.b00edec552169b4ab272cd6ba68a45c1_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：2.63秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 223 张图片: ed1c116f185d48e987f7acf5c835d292_jpg.rf.441ebd3068dc343a343ea7dab336911a_with_roboflow_yolo.jpg

==================================================
![ed1c116f185d48e987f7acf5c835d292_jpg.rf.441ebd3068dc343a343ea7dab336911a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/ed1c116f185d48e987f7acf5c835d292_jpg.rf.441ebd3068dc343a343ea7dab336911a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.21秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 224 张图片: ef1a9158db754e4b9f22909e02d9e632_jpg.rf.7a6b35fe2ee9186a9ee7fa28bb0152fc_with_roboflow_yolo.jpg

==================================================
![ef1a9158db754e4b9f22909e02d9e632_jpg.rf.7a6b35fe2ee9186a9ee7fa28bb0152fc_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/ef1a9158db754e4b9f22909e02d9e632_jpg.rf.7a6b35fe2ee9186a9ee7fa28bb0152fc_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：2.52秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 225 张图片: f0dfcaeeeae34cf8aef575a4877ab0e5_jpg.rf.e50cef6a7267af871d83e99523915541_with_roboflow_yolo.jpg

==================================================
![f0dfcaeeeae34cf8aef575a4877ab0e5_jpg.rf.e50cef6a7267af871d83e99523915541_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f0dfcaeeeae34cf8aef575a4877ab0e5_jpg.rf.e50cef6a7267af871d83e99523915541_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.73秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 226 张图片: f2dd3f13bef0458ba6c336b1fe3c58aa_jpg.rf.36056355144ae42ec63985d7ce77ab61_with_roboflow_yolo.jpg

==================================================
![f2dd3f13bef0458ba6c336b1fe3c58aa_jpg.rf.36056355144ae42ec63985d7ce77ab61_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f2dd3f13bef0458ba6c336b1fe3c58aa_jpg.rf.36056355144ae42ec63985d7ce77ab61_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.11秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 227 张图片: f477625f451f4d09a161df7e896ea521_jpg.rf.7a5b3e6c92f4d4efdaed362f3b2026e6_with_roboflow_yolo.jpg

==================================================
![f477625f451f4d09a161df7e896ea521_jpg.rf.7a5b3e6c92f4d4efdaed362f3b2026e6_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f477625f451f4d09a161df7e896ea521_jpg.rf.7a5b3e6c92f4d4efdaed362f3b2026e6_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 228 张图片: f4f12c741d7f4c5f98cd285ca2536762_jpg.rf.3bb2b56c568a01ed905a2c82c22f43fa_with_roboflow_yolo.jpg

==================================================
![f4f12c741d7f4c5f98cd285ca2536762_jpg.rf.3bb2b56c568a01ed905a2c82c22f43fa_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f4f12c741d7f4c5f98cd285ca2536762_jpg.rf.3bb2b56c568a01ed905a2c82c22f43fa_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.35秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 229 张图片: f62c139aa6144017bfc2389ab53a973c_jpg.rf.dd37b77fbdcfdcf42dbba86647e0f81a_with_roboflow_yolo.jpg

==================================================
![f62c139aa6144017bfc2389ab53a973c_jpg.rf.dd37b77fbdcfdcf42dbba86647e0f81a_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f62c139aa6144017bfc2389ab53a973c_jpg.rf.dd37b77fbdcfdcf42dbba86647e0f81a_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.38秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 230 张图片: f69e518db39b4ed2be196663669c2fe0_jpg.rf.851be0eb92251cc9dc1e7af1de514daf_with_roboflow_yolo.jpg

==================================================
![f69e518db39b4ed2be196663669c2fe0_jpg.rf.851be0eb92251cc9dc1e7af1de514daf_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f69e518db39b4ed2be196663669c2fe0_jpg.rf.851be0eb92251cc9dc1e7af1de514daf_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[■][×]"}
```
### 响应时间：1.68秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 231 张图片: f6c40175f47143a19da3d5904c6ca72b_jpg.rf.2869946fb4c07314701e6b6678fb4a8b_with_roboflow_yolo.jpg

==================================================
![f6c40175f47143a19da3d5904c6ca72b_jpg.rf.2869946fb4c07314701e6b6678fb4a8b_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f6c40175f47143a19da3d5904c6ca72b_jpg.rf.2869946fb4c07314701e6b6678fb4a8b_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[√][■]", "题目5": "[√][■]"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 1341
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 232 张图片: f9c0d9d0fe994ac592d97a8a74079ec7_jpg.rf.c1dbb3f3db8e76d71bfc527e8ee4a336_with_roboflow_yolo.jpg

==================================================
![f9c0d9d0fe994ac592d97a8a74079ec7_jpg.rf.c1dbb3f3db8e76d71bfc527e8ee4a336_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/f9c0d9d0fe994ac592d97a8a74079ec7_jpg.rf.c1dbb3f3db8e76d71bfc527e8ee4a336_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]"}
```
### 响应时间：1.64秒
### token用量
- total_tokens: 574
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 233 张图片: fa61be0e745146c5a9427f804d0783f9_jpg.rf.b9ffffd287fcdba576ae39adfd03ffa6_with_roboflow_yolo.jpg

==================================================
![fa61be0e745146c5a9427f804d0783f9_jpg.rf.b9ffffd287fcdba576ae39adfd03ffa6_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/fa61be0e745146c5a9427f804d0783f9_jpg.rf.b9ffffd287fcdba576ae39adfd03ffa6_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.84秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 234 张图片: fa780c61000144f38d0dda40708c8797_jpg.rf.92679e06a925f79323efecd3c214ae30_with_roboflow_yolo.jpg

==================================================
![fa780c61000144f38d0dda40708c8797_jpg.rf.92679e06a925f79323efecd3c214ae30_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/fa780c61000144f38d0dda40708c8797_jpg.rf.92679e06a925f79323efecd3c214ae30_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 573
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 235 张图片: fbdaec22a9e04640ad903be222f249fe_jpg.rf.dad6226b7696caf747792df9792723d3_with_roboflow_yolo.jpg

==================================================
![fbdaec22a9e04640ad903be222f249fe_jpg.rf.dad6226b7696caf747792df9792723d3_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/fbdaec22a9e04640ad903be222f249fe_jpg.rf.dad6226b7696caf747792df9792723d3_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[■][×]", "题目5": "[√][■]", "题目6": "[■][×]"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 236 张图片: fe03c63a619a468286cebe3526be8cde_jpg.rf.91357e38bb9f3494d509dd7f91c554ed_with_roboflow_yolo.jpg

==================================================
![fe03c63a619a468286cebe3526be8cde_jpg.rf.91357e38bb9f3494d509dd7f91c554ed_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/fe03c63a619a468286cebe3526be8cde_jpg.rf.91357e38bb9f3494d509dd7f91c554ed_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[√][■]"}
```
### 响应时间：1.80秒
### token用量
- total_tokens: 995
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 237 张图片: fe5a82fcb45f4c3b8ea0e9bdd37ea7fc_jpg.rf.6cefcf17183cd55aecbdbeb177af19a2_with_roboflow_yolo.jpg

==================================================
![fe5a82fcb45f4c3b8ea0e9bdd37ea7fc_jpg.rf.6cefcf17183cd55aecbdbeb177af19a2_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/fe5a82fcb45f4c3b8ea0e9bdd37ea7fc_jpg.rf.6cefcf17183cd55aecbdbeb177af19a2_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]", "题目5": "[√][■]"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 615
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 238 张图片: fe5bdb3920cf4fd682090cab959387e3_jpg.rf.1dd827417bd2634a54dc6821f1b5809f_with_roboflow_yolo.jpg

==================================================
![fe5bdb3920cf4fd682090cab959387e3_jpg.rf.1dd827417bd2634a54dc6821f1b5809f_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/fe5bdb3920cf4fd682090cab959387e3_jpg.rf.1dd827417bd2634a54dc6821f1b5809f_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[√][■]", "题目2": "[√][■]", "题目3": "[■][×]", "题目4": "[√][■]", "题目5": "[■][×]", "题目6": "[■][×]"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 608
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 239 张图片: fee31d59d6ca48eea1cedf242d9ec9bc_jpg.rf.2b5df214fc623386b07015341772bd12_with_roboflow_yolo.jpg

==================================================
![fee31d59d6ca48eea1cedf242d9ec9bc_jpg.rf.2b5df214fc623386b07015341772bd12_with_roboflow_yolo.jpg](../roboflow_yolo_result/images_2025-07-30_13-38-44/fee31d59d6ca48eea1cedf242d9ec9bc_jpg.rf.2b5df214fc623386b07015341772bd12_with_roboflow_yolo.jpg)
### 响应内容：
```json
{"题目1": "[■][×]", "题目2": "[■][×]", "题目3": "[√][■]", "题目4": "[■][×]"}
```
### 响应时间：1.77秒
### token用量
- total_tokens: 772
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
所有图片处理完成！

==================================================
