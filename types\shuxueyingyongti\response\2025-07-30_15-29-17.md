**使用模型ID：** doubao-1-5-vision-pro-32k-250115

**使用图片文件夹：** images

## 错题
- 第 1 张图片: 01e61d24110b4e34b0f955a6b27b08a6.jpg
- 第 2 张图片: 022871a0eb524040acd1c907c74f739e.jpg
- 第 3 张图片: 033dd78b6b3e4848aebe97bea07b46fc.jpg
- 第 4 张图片: 03668c25d40d40c7bfea64cd539aeb0c.jpg
- 第 5 张图片: 060bcc078c164f4f8855e19abc179158.jpg
- 第 6 张图片: 061dbeddfab54fc4a2e3461f16e8119b.jpg
- 第 7 张图片: 07a4c504a07d4b1c9cb50337049da4a2.jpg
- 第 8 张图片: 07cc6f92d9304f209c7a2e78930007cb.jpg
- 第 9 张图片: 080782ddca0b45288d20a1a9ae82a9a0.jpg
- 第 10 张图片: 088296a05f77471888551bdc18a7d369.jpg
- 第 11 张图片: 08c656cd9e9642a5b921621e1c69c077.jpg
- 第 12 张图片: 0ad90c875277476a9af079e77f524c38.jpg
- 第 13 张图片: 0b757547403a4d3fb627ed84bc067af6.jpg
- 第 14 张图片: 0d8e5683132e4e6384f712a7b09d7a72.jpg
- 第 15 张图片: 0dc84776ea21421397132e7f91d712d2.jpg
- 第 16 张图片: 0f953db82d6f41afadbb9529da148929.jpg
- 第 17 张图片: 105a4154dd424f0b9e3161cd8b479158.jpg
- 第 18 张图片: 124621b361c944c1b875570c7e325bfb.jpg
- 第 19 张图片: 141c4cc0b777479d82fdaddbfda47292.jpg
- 第 20 张图片: 1557835b855f4bf3ad5f343b24d18cbd.jpg
- 第 21 张图片: 17519893b229410bb9c677be178b4f6e.jpg
- 第 22 张图片: 175a0d317acf44f09616f05e7cea5ff9.jpg
- 第 23 张图片: 189be043c6264e98b95c915c62b3d735.jpg
- 第 24 张图片: 1a34a26eaab4479293602df89c749c0e.jpg
- 第 25 张图片: 1bcda20534724ec2a3ccd6b246460a6d.jpg
- 第 26 张图片: 1caef643f8ed47ce8ef4058571d7569f.jpg
- 第 27 张图片: 1d89661388b840ab8362392b475c1bbc.jpg
- 第 28 张图片: 1ee00c0ae6b642268303c6f57cc5e8f3.jpg
- 第 29 张图片: 1fce2f5805b649a18d290d4336ca4d25.jpg
- 第 30 张图片: 20ace064918f4e05ae740743a1f7d711.jpg
- 第 31 张图片: 21385c2ed588427cb29ac9af83126168.jpg
- 第 32 张图片: 217197c1d4374375b9c5d1db01ad369e.jpg
- 第 34 张图片: 2332ce46b0dd47ca9019d93458248b00.jpg
- 第 35 张图片: 245e0f7f9ed84719b353296a72b3d5d5.jpg
- 第 36 张图片: 24bd91538df241108766a3fe9ee1b4f5.jpg
- 第 37 张图片: 262d47075393400c8915bbee89d0c91d.jpg
- 第 38 张图片: 267c8e66fab34768a7d696808d8b5a55.jpg
- 第 39 张图片: 27c1dddb328e44fcabcd7c0eb58ee499.jpg
- 第 40 张图片: 295d0795346b4278a43e52a9e533b6e2.jpg
- 第 41 张图片: 29d63c8041804dabb20c0db0d3dea6f9.jpg
- 第 42 张图片: 2cbafe365d2040848110299b152abb82.jpg
- 第 43 张图片: 2f31186109f34ca3a9c0b0d1aa8e6600.jpg
- 第 44 张图片: 322f6360b06041bf800adce30610bae2.jpg
- 第 45 张图片: 331ef99f60d6439a998df73b850427d7.jpg
- 第 46 张图片: 334b3d2eb72e4bb6b52c6e48ffcf8129.jpg
- 第 47 张图片: 33e939136f9d42f98fa32817e7fd8ba0.jpg
- 第 48 张图片: 34d636576e894c1291aa8cd2717ed60f.jpg
- 第 49 张图片: 36201fb10e1048aa8ab1efb18aeef992.jpg
- 第 50 张图片: 36c5ec0ea7554cc59e9b529e82f238b5.jpg
- 第 51 张图片: 37994667a92c4b0083a6b952099f218b.jpg
- 第 52 张图片: 3c405e93109f46508267913b06ddeef0.jpg
- 第 53 张图片: 3ca4c63fa5b4411ea16b413977ca46be.jpg
- 第 54 张图片: 3e476b7eb9a846c6b567bd3f5beef5b7.jpg
- 第 56 张图片: 419863383e6546df89d1ea0d381d6d0a.jpg
- 第 57 张图片: 41d61d95fe524503a5e79aada6810bab.jpg
- 第 58 张图片: 42460b9850d4496095309aeaed97628b.jpg
- 第 59 张图片: 42d44c3f341b444aa875da2bdc23ab9f.jpg
- 第 60 张图片: 42e7a353809b46a2bd53a4c5c4229de9.jpg
- 第 61 张图片: 4373bd4cb473453a8a0ec2d2b5a15f71.jpg
- 第 62 张图片: 443d5224c3c045ac9eabde38fa46f202.jpg
- 第 63 张图片: 45172bb8e08a472fa1210cf0ef92c274.jpg
- 第 64 张图片: 46f950a79bf3489ca60e43c5d888b4b4.jpg
- 第 65 张图片: 47ae497167a745ed97b7b6d2488406d3.jpg
- 第 66 张图片: 48ccb43529864857a1614cd50e1f7ea5.jpg
- 第 67 张图片: 4a80f74708634735bdbcff37fd0417f9.jpg
- 第 68 张图片: 4b94117a218e4b08b930d2aa87b4714b.jpg
- 第 69 张图片: 4c0acaa4cb6e4b078bff8ae38bf6869b.jpg
- 第 70 张图片: 4c4a6888066d4e57a4e646a0a4040899.jpg
- 第 71 张图片: 4cac45bba09e40de92005e0fd42ebfd1.jpg
- 第 72 张图片: 4e71265ae4be45cea6c5720faeff8ae3.jpg
- 第 73 张图片: 4f3ed696560c45c59dd6fbf03480bf83.jpg
- 第 74 张图片: 4f555c23145b4340a0214b3607b9b27e.jpg
- 第 75 张图片: 519ab0c9d9524ff0b9ac81a0cf598384.jpg
- 第 76 张图片: 523ba46a85544d43bfd759fdb41482ee.jpg
- 第 78 张图片: 53c5585522cc45e8bbdc209daa309415.jpg
- 第 79 张图片: 54558b45a61c43d88a55062b1867f5c6.jpg
- 第 80 张图片: 560212c34f974127a9979d39bf238324.jpg
- 第 81 张图片: 5602caf1b4fa49d5a940c9e503458bae.jpg
- 第 82 张图片: 56b18105cdd24abaa5999cb6c027f755.jpg
- 第 83 张图片: 57a448c20d0d4f31b0974f78b4758a67.jpg
- 第 84 张图片: 589c333a2312442eba7938fae330ab27.jpg
- 第 85 张图片: 5b8b8bb2865b484d8a489afad55b4b65.jpg
- 第 86 张图片: 5bf557b1913d4f43a1e17d106ed7645f.jpg
- 第 87 张图片: 5cad0ee71cfe4b9bb6da151215454687.jpg
- 第 88 张图片: 5d0b3cd5c97747bdabd1e96dedd77919.jpg
- 第 89 张图片: 5d0f9530b79c4e37882dadd83c8730e0.jpg
- 第 90 张图片: 5f6f2d9dfa3e4f56a3d55056b5bf28c6.jpg
- 第 91 张图片: 61d61a083bcb4bf9959002054c7e9c59.jpg
- 第 92 张图片: 62ce36f065ca438090d6a550d577d08f.jpg
- 第 93 张图片: 65fd8d16b44f4d15b100f4dfef75fa95.jpg
- 第 94 张图片: 6864af96eb4142fc83ace034f41a91c8.jpg
- 第 95 张图片: 69258f143d5f4db09332474cc4a3303d.jpg
- 第 96 张图片: 6bc7fb8170384d2aa087f9830d30c698.jpg
- 第 97 张图片: 6bcc7e34aa2e486d973892deaa90fd35.jpg
- 第 98 张图片: 6c58550cb0a4427086c80f2d7dfb280a.jpg
- 第 99 张图片: 6ec6304ce69c41aa9e3d1cb62eac93e9.jpg
- 第 100 张图片: 6ed01242034c451689817c25873093ef.jpg
- 第 101 张图片: 6f8a7831ce534073939e362723bc667d.jpg
- 第 102 张图片: 6fee1745c1a34accb733081aa83a4e62.jpg
- 第 103 张图片: 701affc2354449cf870e67315dbbd61a.jpg
- 第 104 张图片: 72ca30aed54541a2b4200cb6b62bed64.jpg
- 第 105 张图片: 76d484aa1746422fb8887429c468fd9b.jpg
- 第 106 张图片: 78a8674d6f2643718aa52bb09a9fbfed.jpg
- 第 107 张图片: 795178ed2049425cb3f77791f7fa6b53.jpg
- 第 108 张图片: 7a9b357ffd75425d94c83b8aaf9af911.jpg
- 第 109 张图片: 7a9b88a938d646a18a2627b34bcc2e99.jpg
- 第 110 张图片: 7ca34f564ac34a86ab59b4241a38e2ce.jpg
- 第 111 张图片: 7e23c266f8c04f518a29bffe57b58c6f.jpg
- 第 112 张图片: 7f1badde1aa4450f8f81342dd70f43e5.jpg
- 第 113 张图片: 811648e7cc5944d58aebbaade26320a8.jpg
- 第 114 张图片: 82fd7c2a937b4d7cacfbe6ff203a1743.jpg
- 第 115 张图片: 83c2aea0e99e4e65b326aa3006be69cb.jpg
- 第 116 张图片: 8414a3c7a48b4a8587546713d1be4de7.jpg
- 第 117 张图片: 852c1f98d0974e819ad8c8cff833fed4.jpg
- 第 118 张图片: 8622b9fbda6a408e8e977492681b9000.jpg
- 第 119 张图片: 866241cb0a5d4c2ea446357f19fd9527.jpg
- 第 120 张图片: 8747669d9baf4abd89076583eb721851.jpg
- 第 121 张图片: 87ff291e4cfe4dca9d3bf4c9b92236ef.jpg
- 第 122 张图片: 884fb4481c954cf8946768f83b9e71a9.jpg
- 第 123 张图片: 8a9e78ddac8a42d79190963c0aa60d88.jpg
- 第 124 张图片: 8db20d2be4354628bcc186c7b1c09b87.jpg
- 第 125 张图片: 8e31c018e48a4c1d841c9e68ba4175ef.jpg
- 第 126 张图片: 8e60ee07606042a99ec368f275ba9955.jpg
- 第 127 张图片: 8efb4c2319a54615975db5b2daf06322.jpg
- 第 128 张图片: 8f3b9e5a42d94b15b9c2bcfea9adfc79.jpg
- 第 129 张图片: 909f11127bf84c1eb3a0fcbc2444416f.jpg
- 第 130 张图片: 916b2d19fc134cfcb4188a09f3f39c91.jpg
- 第 131 张图片: 91ede973e4574ed98b7327f6bc97c82d.jpg
- 第 132 张图片: 929ae9c7d52e4544a850d10d64b9eb66.jpg
- 第 133 张图片: 9365597907834c30b22ed57739e7ae40.jpg
- 第 134 张图片: 94174957c26446d2886ee99d93e1c180.jpg
- 第 135 张图片: 9423221d7e894451bcc04ae043c35336.jpg
- 第 136 张图片: 942674d78b034640a555846856c998bf.jpg
- 第 137 张图片: 94c1bfa3c6fc41c3b2e561ff3a81efe6.jpg
- 第 138 张图片: 950b17fca98048a4a7b783d6034ff854.jpg
- 第 139 张图片: 954a43c3742943d5adc1ee5801123747.jpg
- 第 140 张图片: 956ba34653764928816b2ad0ce149d7f.jpg
- 第 141 张图片: 9919147a77574eae82dea0f2d5685201.jpg
- 第 142 张图片: 99874f83919c424aa9dfceb8462915e0.jpg
- 第 143 张图片: 9a0963909ea04654a3afe5d50f1b7615.jpg
- 第 144 张图片: 9afedf06949f41718eb165b18e0ed0fb.jpg
- 第 145 张图片: 9b28fa077d7346b58f873d8926ef41a6.jpg
- 第 146 张图片: 9b499f7c27de4b948e4e4e5fc234d338.jpg
- 第 147 张图片: 9c0ee5afc90b476aae7ed75f3faf1451.jpg
- 第 148 张图片: 9c6101fa0b0c4deaaa09a549494b0f86.jpg
- 第 149 张图片: a038b48d9a6f42c185420aebe91a54d5.jpg
- 第 150 张图片: a0479727ffe04f04b3803bf455c10528.jpg
- 第 151 张图片: a0d3235880b9432184d64c47689b76fd.jpg
- 第 152 张图片: a19122789ad140e18f141fa3e5c853b5.jpg
- 第 153 张图片: a1b67fbd1e554656a105d85cf419a157.jpg
- 第 154 张图片: a257d263622147a0b4ecfb3c690893c7.jpg
- 第 155 张图片: a2d5f09bc5ce4090bb693a28f3276934.jpg
- 第 156 张图片: a4719a75e2174b62977e0f1bf7c6d133.jpg
- 第 157 张图片: a4e9642d03fb4da3969dcfed935acac4.jpg
- 第 158 张图片: a5464c6391354d41b4d584e8cd4d186a.jpg
- 第 159 张图片: a67ecf878f93408e80ed18e8d726b722.jpg
- 第 160 张图片: a6b98210e3c04a608d6a08d0bca348c2.jpg
- 第 161 张图片: a725b9de926c401b89be310de7e0c131.jpg
- 第 162 张图片: a7559ef804ef42d494348869b4f625c6.jpg
- 第 163 张图片: a793237b72884136874afaaa0d6a4ced.jpg
- 第 164 张图片: aa4242739fd746b8aecef91dc621bb4f.jpg
- 第 165 张图片: ab0b77b31625487c82db63a3cd12add9.jpg
- 第 166 张图片: ab3ee8f0b39d46769fa44e50dd239150.jpg
- 第 167 张图片: ab78c0731e034dd297ccc362726f58fa.jpg
- 第 168 张图片: acf8bb8e49f84a14b227b45506a0f975.jpg
- 第 169 张图片: af25a2d303534cb88beccb1e4311c72a.jpg
- 第 170 张图片: b0f76e1e122949feb9c3b5b6b4e0109d.jpg
- 第 171 张图片: b195031a8298438c94b6777396d06ca7.jpg
- 第 172 张图片: b2e282b3eb6b497b916bdc81ae7c540a.jpg
- 第 173 张图片: b740b345eb7742b9b8814788d7b2a379.jpg
- 第 174 张图片: b7923b2dd024478fb38a6e2272002604.jpg
- 第 175 张图片: b7ae5c1b43cc4a61899f7396d07a078f.jpg
- 第 176 张图片: b7b2178981f34030a0f3c14d70aea385.jpg
- 第 177 张图片: bb02a190ca4943d09a71f243fd5c2ffc.jpg
- 第 178 张图片: bb12241589af4f1ba4f951b5e871f686.jpg
- 第 179 张图片: bbc6008549df4037a276506fbf75b4c3.jpg
- 第 180 张图片: bd01bfe7e4ae4be5bc6003dad1a0c78b.jpg
- 第 181 张图片: be484976ee5d4391801c5db31fbb7862.jpg
- 第 182 张图片: bf718bb0b6544d3f807f9f453e3fce0a.jpg
- 第 183 张图片: bf99332d982740579c52f1512110d33a.jpg
- 第 184 张图片: c0b4dbebc5414689b4f9bd00a55c9e6d.jpg
- 第 185 张图片: c13df5864ad64900965a94ff4dc67e25.jpg
- 第 186 张图片: c1454de9b204405b871d0f25427830e5.jpg
- 第 187 张图片: c1a6b28474384aafbd96583aa7bec7cb.jpg
- 第 188 张图片: c1b79cddb98c49dfb9e0feb27bed1ec4.jpg
- 第 189 张图片: c1f7e24e349a4c78b24abb07eed62cf9.jpg
- 第 190 张图片: c30be5eba7e042b49319f695ca3de1d8.jpg
- 第 191 张图片: c31a05becaee434a9d9aec381efdcfc9.jpg
- 第 192 张图片: c490d94e188e4492b29859d9a33eab11.jpg
- 第 193 张图片: c8311ca0e96947a094630c2d976d58be.jpg
- 第 194 张图片: c8c08fb68ccb4e5dbaa9981e2bf2f770.jpg
- 第 195 张图片: ca73ba16447549dda1ec6d9e905bd153.jpg
- 第 196 张图片: cca96ff43d23410b92f92839a868c511.jpg
- 第 197 张图片: cf37667e26d644ffac3184c4bdf73cc6.jpg
- 第 198 张图片: cfa992a0a8c24552aa665734aef92568.jpg
- 第 199 张图片: d0a8e68d325f476a83990dca2175e038.jpg
- 第 200 张图片: d0e95ae547ea467a8fc469f332ce4418.jpg
- 第 201 张图片: d364e488ca5e4ce983bd53b054cbe88e.jpg
- 第 202 张图片: d4593c1b340548cd9a815f02faecabfd.jpg
- 第 203 张图片: d667f6bec36c4e24979c3ec986fa5439.jpg
- 第 204 张图片: d736943e30614a8281f75344e2669c37.jpg
- 第 205 张图片: d76947e1ae834d2eadec973b358ea5d2.jpg
- 第 206 张图片: d77a21eef7c4445ab0cb4230214dda68.jpg
- 第 207 张图片: d81b41440848418183a4cdbdcacebe00.jpg
- 第 208 张图片: d8440545f6bd4da8ae5d47f13d1d605b.jpg
- 第 209 张图片: d86ed97c5d6e41cf99c7bbe4ca6446ab.jpg
- 第 210 张图片: d92c361afe9c4843b1ca5d72e5004f93.jpg
- 第 212 张图片: db09644f56eb42fd85f3120693cd7ff0.jpg
- 第 213 张图片: db126b609b5747bc88c60ea23c41227b.jpg
- 第 214 张图片: db44e162dcdb4a9aad070a720c6ce12b.jpg
- 第 215 张图片: dc4ed800746d45e1a74fdf32e50f7af5.jpg
- 第 216 张图片: de46d2b212fb494d99438d62eeb33d32.jpg
- 第 217 张图片: debe18caf1a94f369e3bc83436f53d5f.jpg
- 第 218 张图片: e0ca351fbf1e461eaa068f066dbc7d8a.jpg
- 第 220 张图片: e48e28f69fe54e69845748fd0b4e55df.jpg
- 第 221 张图片: e5bdc408699b44fb8a0b6ce84d3e5821.jpg
- 第 222 张图片: e5d7d8ec61234ba1ba91261aa7cc57dd.jpg
- 第 223 张图片: e6345b5ca7184b369bbf7b7b8359e10a.jpg
- 第 224 张图片: e634f3460c7e43f7a55898feb67565e7.jpg
- 第 225 张图片: e66445359c8b49029da3b35e5c531ec7.jpg
- 第 226 张图片: e6ae35a5b7604740a5b0937f18bb179c.jpg
- 第 227 张图片: e8bc848c374941899f27bf34c8b01511.jpg
- 第 228 张图片: e8eb42f0ecd1483cb53d3eadd698b94a.jpg
- 第 229 张图片: eaa6bcb3a1014b8aa5a60752a11b6790.jpg
- 第 230 张图片: ec521200e636430e9dbdaf6510ddebd9.jpg
- 第 231 张图片: ee194ecb8ba847479c8df4ed64732e9b.jpg
- 第 232 张图片: f12d67b2251e494aad7fe60cf97f2950.jpg
- 第 233 张图片: f162055451674e86aad76ea4ce46056f.jpg
- 第 234 张图片: f2835062578c4f69b2a5091137aae9fc.jpg
- 第 235 张图片: f3aa4880b0784f17b2fec5823093294d.jpg
- 第 236 张图片: f3c8da7f9c384c52a565b2c00537eb00.jpg
- 第 237 张图片: f7b975a709a44539bd5e2b22d70e7acd.jpg
- 第 238 张图片: fa3d1035af134585b5a25bc2c95d29cb.jpg
- 第 239 张图片: fb79b851e72c46eb8f16c04b13b13750.jpg
- 第 240 张图片: fbaddaebde144d8e9e5468822939a160.jpg
- 第 241 张图片: fc26dbaf7deb4845a92444ec41676f49.jpg
- 第 242 张图片: fcb037c618e44c3cafa85afe38a53750.jpg
- 第 243 张图片: fdc434c57a544bb7a0c58cdc9bfd605d.jpg
- 第 244 张图片: fe879fda4abc422a8f083bca7b077130.jpg
- 第 245 张图片: fee822dde47b40a9b4c8c9c87a1049e6.jpg

## 准确率：2.04%  （(245 - 240) / 245）

# 运行时间: 2025-07-30_15-29-17

## 使用模型ID: doubao-1-5-vision-pro-32k-250115

## 使用图片文件夹: images

## 图片放大倍数: 1.0

找到 245 张图片，开始逐个处理...
使用的提示词: 你的任务是计算一张图片里一系列数学应用题的结果。请仔细查看这张图片里的数学应用题，并按照指示输出结果。
以下是数学应用题：

{{MATH_APPLICATION_PROBLEMS}}

在计算结果时，请遵循以下指南：
1. 只需要关注应用题的最终结果，不需要关注解题步骤。
2. 必须以 JSON 格式输出，请参考如下格式返回：{"题目 1": "答案内容 1", "题目 2": "答案内容 2", "题目 3": "答案内容 3"} ，返回的 JSON 题号必须始终从 “题目 1” 开始，依次递增。
3. 如果计算结果无法得出，则返回“NAN”。
请直接返回一个 JSON 作为结果。

==================================================
处理第 1 张图片: 01e61d24110b4e34b0f955a6b27b08a6.jpg

==================================================
![01e61d24110b4e34b0f955a6b27b08a6.jpg](../images/01e61d24110b4e34b0f955a6b27b08a6.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.60秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 2 张图片: 022871a0eb524040acd1c907c74f739e.jpg

==================================================
![022871a0eb524040acd1c907c74f739e.jpg](../images/022871a0eb524040acd1c907c74f739e.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.20秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 3 张图片: 033dd78b6b3e4848aebe97bea07b46fc.jpg

==================================================
![033dd78b6b3e4848aebe97bea07b46fc.jpg](../images/033dd78b6b3e4848aebe97bea07b46fc.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 4 张图片: 03668c25d40d40c7bfea64cd539aeb0c.jpg

==================================================
![03668c25d40d40c7bfea64cd539aeb0c.jpg](../images/03668c25d40d40c7bfea64cd539aeb0c.jpg)
### 响应内容：
```json
{"题目1": "207", "题目2": "够"}
```
### 响应时间：1.56秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 5 张图片: 060bcc078c164f4f8855e19abc179158.jpg

==================================================
![060bcc078c164f4f8855e19abc179158.jpg](../images/060bcc078c164f4f8855e19abc179158.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 6 张图片: 061dbeddfab54fc4a2e3461f16e8119b.jpg

==================================================
![061dbeddfab54fc4a2e3461f16e8119b.jpg](../images/061dbeddfab54fc4a2e3461f16e8119b.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：3.05秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 7 张图片: 07a4c504a07d4b1c9cb50337049da4a2.jpg

==================================================
![07a4c504a07d4b1c9cb50337049da4a2.jpg](../images/07a4c504a07d4b1c9cb50337049da4a2.jpg)
### 响应内容：
```json
{"题目 1": "参加科技类的学生有 25 人，参加艺术类的学生有 12 人"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 1161
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 8 张图片: 07cc6f92d9304f209c7a2e78930007cb.jpg

==================================================
![07cc6f92d9304f209c7a2e78930007cb.jpg](../images/07cc6f92d9304f209c7a2e78930007cb.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.70秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 9 张图片: 080782ddca0b45288d20a1a9ae82a9a0.jpg

==================================================
![080782ddca0b45288d20a1a9ae82a9a0.jpg](../images/080782ddca0b45288d20a1a9ae82a9a0.jpg)
### 响应内容：
```json
{"题目 1": "NAN"}
```
### 响应时间：2.74秒
### token用量
- total_tokens: 1140
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 10 张图片: 088296a05f77471888551bdc18a7d369.jpg

==================================================
![088296a05f77471888551bdc18a7d369.jpg](../images/088296a05f77471888551bdc18a7d369.jpg)
### 响应内容：
```json
{"题目1": "249", "题目2": "够"}
```
### 响应时间：2.50秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 11 张图片: 08c656cd9e9642a5b921621e1c69c077.jpg

==================================================
![08c656cd9e9642a5b921621e1c69c077.jpg](../images/08c656cd9e9642a5b921621e1c69c077.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.97秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 12 张图片: 0ad90c875277476a9af079e77f524c38.jpg

==================================================
![0ad90c875277476a9af079e77f524c38.jpg](../images/0ad90c875277476a9af079e77f524c38.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：3.05秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 13 张图片: 0b757547403a4d3fb627ed84bc067af6.jpg

==================================================
![0b757547403a4d3fb627ed84bc067af6.jpg](../images/0b757547403a4d3fb627ed84bc067af6.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.40秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 14 张图片: 0d8e5683132e4e6384f712a7b09d7a72.jpg

==================================================
![0d8e5683132e4e6384f712a7b09d7a72.jpg](../images/0d8e5683132e4e6384f712a7b09d7a72.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.40秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 15 张图片: 0dc84776ea21421397132e7f91d712d2.jpg

==================================================
![0dc84776ea21421397132e7f91d712d2.jpg](../images/0dc84776ea21421397132e7f91d712d2.jpg)
### 响应内容：
```json
{"题目1": "6.00元", "题目2": "1300人"}
```
### 响应时间：2.57秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 16 张图片: 0f953db82d6f41afadbb9529da148929.jpg

==================================================
![0f953db82d6f41afadbb9529da148929.jpg](../images/0f953db82d6f41afadbb9529da148929.jpg)
### 响应内容：
```json
{"题目 1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：1.59秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 17 张图片: 105a4154dd424f0b9e3161cd8b479158.jpg

==================================================
![105a4154dd424f0b9e3161cd8b479158.jpg](../images/105a4154dd424f0b9e3161cd8b479158.jpg)
### 响应内容：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```
### 响应时间：3.26秒
### token用量
- total_tokens: 3239
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 18 张图片: 124621b361c944c1b875570c7e325bfb.jpg

==================================================
![124621b361c944c1b875570c7e325bfb.jpg](../images/124621b361c944c1b875570c7e325bfb.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.05秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 19 张图片: 141c4cc0b777479d82fdaddbfda47292.jpg

==================================================
![141c4cc0b777479d82fdaddbfda47292.jpg](../images/141c4cc0b777479d82fdaddbfda47292.jpg)
### 响应内容：
```json
{"题目1": "218", "题目2": "够"}
```
### 响应时间：2.38秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 20 张图片: 1557835b855f4bf3ad5f343b24d18cbd.jpg

==================================================
![1557835b855f4bf3ad5f343b24d18cbd.jpg](../images/1557835b855f4bf3ad5f343b24d18cbd.jpg)
### 响应内容：
```json
{"题目 1": "320", "题目 2": "3456"}
```
### 响应时间：2.21秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 21 张图片: 17519893b229410bb9c677be178b4f6e.jpg

==================================================
![17519893b229410bb9c677be178b4f6e.jpg](../images/17519893b229410bb9c677be178b4f6e.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.76秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 22 张图片: 175a0d317acf44f09616f05e7cea5ff9.jpg

==================================================
![175a0d317acf44f09616f05e7cea5ff9.jpg](../images/175a0d317acf44f09616f05e7cea5ff9.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 23 张图片: 189be043c6264e98b95c915c62b3d735.jpg

==================================================
![189be043c6264e98b95c915c62b3d735.jpg](../images/189be043c6264e98b95c915c62b3d735.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.73秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 24 张图片: 1a34a26eaab4479293602df89c749c0e.jpg

==================================================
![1a34a26eaab4479293602df89c749c0e.jpg](../images/1a34a26eaab4479293602df89c749c0e.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.52秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 25 张图片: 1bcda20534724ec2a3ccd6b246460a6d.jpg

==================================================
![1bcda20534724ec2a3ccd6b246460a6d.jpg](../images/1bcda20534724ec2a3ccd6b246460a6d.jpg)
### 响应内容：
```json
{"题目 1": "198", "题目 2": "够"}
```
### 响应时间：2.49秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 26 张图片: 1caef643f8ed47ce8ef4058571d7569f.jpg

==================================================
![1caef643f8ed47ce8ef4058571d7569f.jpg](../images/1caef643f8ed47ce8ef4058571d7569f.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.41秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 27 张图片: 1d89661388b840ab8362392b475c1bbc.jpg

==================================================
![1d89661388b840ab8362392b475c1bbc.jpg](../images/1d89661388b840ab8362392b475c1bbc.jpg)
### 响应内容：
```json
{"题目 1": "278", "题目 2": "够"}
```
### 响应时间：1.49秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 28 张图片: 1ee00c0ae6b642268303c6f57cc5e8f3.jpg

==================================================
![1ee00c0ae6b642268303c6f57cc5e8f3.jpg](../images/1ee00c0ae6b642268303c6f57cc5e8f3.jpg)
### 响应内容：
```json
{
    "题目1": "320",
    "题目2": "3456"
}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 2207
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 29 张图片: 1fce2f5805b649a18d290d4336ca4d25.jpg

==================================================
![1fce2f5805b649a18d290d4336ca4d25.jpg](../images/1fce2f5805b649a18d290d4336ca4d25.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 30 张图片: 20ace064918f4e05ae740743a1f7d711.jpg

==================================================
![20ace064918f4e05ae740743a1f7d711.jpg](../images/20ace064918f4e05ae740743a1f7d711.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 31 张图片: 21385c2ed588427cb29ac9af83126168.jpg

==================================================
![21385c2ed588427cb29ac9af83126168.jpg](../images/21385c2ed588427cb29ac9af83126168.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 32 张图片: 217197c1d4374375b9c5d1db01ad369e.jpg

==================================================
![217197c1d4374375b9c5d1db01ad369e.jpg](../images/217197c1d4374375b9c5d1db01ad369e.jpg)
### 响应内容：
```json
{"题目1": "238", "题目2": "够"}
```
### 响应时间：1.52秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 33 张图片: 230f98b4e02546219ba10230ab936a61.jpg

==================================================
![230f98b4e02546219ba10230ab936a61.jpg](../images/230f98b4e02546219ba10230ab936a61.jpg)
### 响应内容：
```json
{"题目 1": "NAN"}
```
### 响应时间：2.61秒
### token用量
- total_tokens: 1219
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 34 张图片: 2332ce46b0dd47ca9019d93458248b00.jpg

==================================================
![2332ce46b0dd47ca9019d93458248b00.jpg](../images/2332ce46b0dd47ca9019d93458248b00.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：2.33秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 35 张图片: 245e0f7f9ed84719b353296a72b3d5d5.jpg

==================================================
![245e0f7f9ed84719b353296a72b3d5d5.jpg](../images/245e0f7f9ed84719b353296a72b3d5d5.jpg)
### 响应内容：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```
### 响应时间：2.91秒
### token用量
- total_tokens: 3239
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 36 张图片: 24bd91538df241108766a3fe9ee1b4f5.jpg

==================================================
![24bd91538df241108766a3fe9ee1b4f5.jpg](../images/24bd91538df241108766a3fe9ee1b4f5.jpg)
### 响应内容：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```
### 响应时间：2.37秒
### token用量
- total_tokens: 3077
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 37 张图片: 262d47075393400c8915bbee89d0c91d.jpg

==================================================
![262d47075393400c8915bbee89d0c91d.jpg](../images/262d47075393400c8915bbee89d0c91d.jpg)
### 响应内容：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```
### 响应时间：3.28秒
### token用量
- total_tokens: 3239
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 38 张图片: 267c8e66fab34768a7d696808d8b5a55.jpg

==================================================
![267c8e66fab34768a7d696808d8b5a55.jpg](../images/267c8e66fab34768a7d696808d8b5a55.jpg)
### 响应内容：
```json
{"题目1": "307", "题目2": "够"}
```
### 响应时间：2.26秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 39 张图片: 27c1dddb328e44fcabcd7c0eb58ee499.jpg

==================================================
![27c1dddb328e44fcabcd7c0eb58ee499.jpg](../images/27c1dddb328e44fcabcd7c0eb58ee499.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.81秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 40 张图片: 295d0795346b4278a43e52a9e533b6e2.jpg

==================================================
![295d0795346b4278a43e52a9e533b6e2.jpg](../images/295d0795346b4278a43e52a9e533b6e2.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：3.13秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 41 张图片: 29d63c8041804dabb20c0db0d3dea6f9.jpg

==================================================
![29d63c8041804dabb20c0db0d3dea6f9.jpg](../images/29d63c8041804dabb20c0db0d3dea6f9.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.60秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 42 张图片: 2cbafe365d2040848110299b152abb82.jpg

==================================================
![2cbafe365d2040848110299b152abb82.jpg](../images/2cbafe365d2040848110299b152abb82.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.95秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 43 张图片: 2f31186109f34ca3a9c0b0d1aa8e6600.jpg

==================================================
![2f31186109f34ca3a9c0b0d1aa8e6600.jpg](../images/2f31186109f34ca3a9c0b0d1aa8e6600.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.67秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 44 张图片: 322f6360b06041bf800adce30610bae2.jpg

==================================================
![322f6360b06041bf800adce30610bae2.jpg](../images/322f6360b06041bf800adce30610bae2.jpg)
### 响应内容：
```json
{"题目1": "207元", "题目2": "够"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 1693
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 45 张图片: 331ef99f60d6439a998df73b850427d7.jpg

==================================================
![331ef99f60d6439a998df73b850427d7.jpg](../images/331ef99f60d6439a998df73b850427d7.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.35秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 46 张图片: 334b3d2eb72e4bb6b52c6e48ffcf8129.jpg

==================================================
![334b3d2eb72e4bb6b52c6e48ffcf8129.jpg](../images/334b3d2eb72e4bb6b52c6e48ffcf8129.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和2辆小客车最省钱"}
```
### 响应时间：2.33秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 47 张图片: 33e939136f9d42f98fa32817e7fd8ba0.jpg

==================================================
![33e939136f9d42f98fa32817e7fd8ba0.jpg](../images/33e939136f9d42f98fa32817e7fd8ba0.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.55秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 48 张图片: 34d636576e894c1291aa8cd2717ed60f.jpg

==================================================
![34d636576e894c1291aa8cd2717ed60f.jpg](../images/34d636576e894c1291aa8cd2717ed60f.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.55秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 49 张图片: 36201fb10e1048aa8ab1efb18aeef992.jpg

==================================================
![36201fb10e1048aa8ab1efb18aeef992.jpg](../images/36201fb10e1048aa8ab1efb18aeef992.jpg)
### 响应内容：
```json
{"题目1": "租2辆小客车和1辆大客车最省钱"}
```
### 响应时间：2.55秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 50 张图片: 36c5ec0ea7554cc59e9b529e82f238b5.jpg

==================================================
![36c5ec0ea7554cc59e9b529e82f238b5.jpg](../images/36c5ec0ea7554cc59e9b529e82f238b5.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.41秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 51 张图片: 37994667a92c4b0083a6b952099f218b.jpg

==================================================
![37994667a92c4b0083a6b952099f218b.jpg](../images/37994667a92c4b0083a6b952099f218b.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.53秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 52 张图片: 3c405e93109f46508267913b06ddeef0.jpg

==================================================
![3c405e93109f46508267913b06ddeef0.jpg](../images/3c405e93109f46508267913b06ddeef0.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：3.18秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 53 张图片: 3ca4c63fa5b4411ea16b413977ca46be.jpg

==================================================
![3ca4c63fa5b4411ea16b413977ca46be.jpg](../images/3ca4c63fa5b4411ea16b413977ca46be.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.65秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 54 张图片: 3e476b7eb9a846c6b567bd3f5beef5b7.jpg

==================================================
![3e476b7eb9a846c6b567bd3f5beef5b7.jpg](../images/3e476b7eb9a846c6b567bd3f5beef5b7.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.86秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 55 张图片: 412027482f004f39b1c2f412192300ce.jpg

==================================================
![412027482f004f39b1c2f412192300ce.jpg](../images/412027482f004f39b1c2f412192300ce.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 56 张图片: 419863383e6546df89d1ea0d381d6d0a.jpg

==================================================
![419863383e6546df89d1ea0d381d6d0a.jpg](../images/419863383e6546df89d1ea0d381d6d0a.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：1.73秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 57 张图片: 41d61d95fe524503a5e79aada6810bab.jpg

==================================================
![41d61d95fe524503a5e79aada6810bab.jpg](../images/41d61d95fe524503a5e79aada6810bab.jpg)
### 响应内容：
```json
{"题目 1": "178", "题目 2": "够"}
```
### 响应时间：2.63秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 58 张图片: 42460b9850d4496095309aeaed97628b.jpg

==================================================
![42460b9850d4496095309aeaed97628b.jpg](../images/42460b9850d4496095309aeaed97628b.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.66秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 59 张图片: 42d44c3f341b444aa875da2bdc23ab9f.jpg

==================================================
![42d44c3f341b444aa875da2bdc23ab9f.jpg](../images/42d44c3f341b444aa875da2bdc23ab9f.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.35秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 60 张图片: 42e7a353809b46a2bd53a4c5c4229de9.jpg

==================================================
![42e7a353809b46a2bd53a4c5c4229de9.jpg](../images/42e7a353809b46a2bd53a4c5c4229de9.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：1.79秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 61 张图片: 4373bd4cb473453a8a0ec2d2b5a15f71.jpg

==================================================
![4373bd4cb473453a8a0ec2d2b5a15f71.jpg](../images/4373bd4cb473453a8a0ec2d2b5a15f71.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.79秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 62 张图片: 443d5224c3c045ac9eabde38fa46f202.jpg

==================================================
![443d5224c3c045ac9eabde38fa46f202.jpg](../images/443d5224c3c045ac9eabde38fa46f202.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.51秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 63 张图片: 45172bb8e08a472fa1210cf0ef92c274.jpg

==================================================
![45172bb8e08a472fa1210cf0ef92c274.jpg](../images/45172bb8e08a472fa1210cf0ef92c274.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.18秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 64 张图片: 46f950a79bf3489ca60e43c5d888b4b4.jpg

==================================================
![46f950a79bf3489ca60e43c5d888b4b4.jpg](../images/46f950a79bf3489ca60e43c5d888b4b4.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.38秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 65 张图片: 47ae497167a745ed97b7b6d2488406d3.jpg

==================================================
![47ae497167a745ed97b7b6d2488406d3.jpg](../images/47ae497167a745ed97b7b6d2488406d3.jpg)
### 响应内容：
```json
{"题目1": "246", "题目2": "够"}
```
### 响应时间：2.10秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 66 张图片: 48ccb43529864857a1614cd50e1f7ea5.jpg

==================================================
![48ccb43529864857a1614cd50e1f7ea5.jpg](../images/48ccb43529864857a1614cd50e1f7ea5.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.70秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 67 张图片: 4a80f74708634735bdbcff37fd0417f9.jpg

==================================================
![4a80f74708634735bdbcff37fd0417f9.jpg](../images/4a80f74708634735bdbcff37fd0417f9.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.64秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 68 张图片: 4b94117a218e4b08b930d2aa87b4714b.jpg

==================================================
![4b94117a218e4b08b930d2aa87b4714b.jpg](../images/4b94117a218e4b08b930d2aa87b4714b.jpg)
### 响应内容：
```json
{"题目1": "249元", "题目2": "够"}
```
### 响应时间：2.42秒
### token用量
- total_tokens: 1693
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 69 张图片: 4c0acaa4cb6e4b078bff8ae38bf6869b.jpg

==================================================
![4c0acaa4cb6e4b078bff8ae38bf6869b.jpg](../images/4c0acaa4cb6e4b078bff8ae38bf6869b.jpg)
### 响应内容：
```json
{"题目1": "科技类25人，艺术类12人"}
```
### 响应时间：1.51秒
### token用量
- total_tokens: 1147
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 70 张图片: 4c4a6888066d4e57a4e646a0a4040899.jpg

==================================================
![4c4a6888066d4e57a4e646a0a4040899.jpg](../images/4c4a6888066d4e57a4e646a0a4040899.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.95秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 71 张图片: 4cac45bba09e40de92005e0fd42ebfd1.jpg

==================================================
![4cac45bba09e40de92005e0fd42ebfd1.jpg](../images/4cac45bba09e40de92005e0fd42ebfd1.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.40秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 72 张图片: 4e71265ae4be45cea6c5720faeff8ae3.jpg

==================================================
![4e71265ae4be45cea6c5720faeff8ae3.jpg](../images/4e71265ae4be45cea6c5720faeff8ae3.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 73 张图片: 4f3ed696560c45c59dd6fbf03480bf83.jpg

==================================================
![4f3ed696560c45c59dd6fbf03480bf83.jpg](../images/4f3ed696560c45c59dd6fbf03480bf83.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：1.93秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 74 张图片: 4f555c23145b4340a0214b3607b9b27e.jpg

==================================================
![4f555c23145b4340a0214b3607b9b27e.jpg](../images/4f555c23145b4340a0214b3607b9b27e.jpg)
### 响应内容：
```json
{
    "题目1": "16平方分米",
    "题目2": "36页",
    "题目3": "144元"
}
```
### 响应时间：2.58秒
### token用量
- total_tokens: 3082
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 75 张图片: 519ab0c9d9524ff0b9ac81a0cf598384.jpg

==================================================
![519ab0c9d9524ff0b9ac81a0cf598384.jpg](../images/519ab0c9d9524ff0b9ac81a0cf598384.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 76 张图片: 523ba46a85544d43bfd759fdb41482ee.jpg

==================================================
![523ba46a85544d43bfd759fdb41482ee.jpg](../images/523ba46a85544d43bfd759fdb41482ee.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 77 张图片: 524faf331817403ea11f5f167fce9e30.jpg

==================================================
![524faf331817403ea11f5f167fce9e30.jpg](../images/524faf331817403ea11f5f167fce9e30.jpg)
### 响应内容：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```
### 响应时间：2.35秒
### token用量
- total_tokens: 3239
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 78 张图片: 53c5585522cc45e8bbdc209daa309415.jpg

==================================================
![53c5585522cc45e8bbdc209daa309415.jpg](../images/53c5585522cc45e8bbdc209daa309415.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 79 张图片: 54558b45a61c43d88a55062b1867f5c6.jpg

==================================================
![54558b45a61c43d88a55062b1867f5c6.jpg](../images/54558b45a61c43d88a55062b1867f5c6.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.45秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 80 张图片: 560212c34f974127a9979d39bf238324.jpg

==================================================
![560212c34f974127a9979d39bf238324.jpg](../images/560212c34f974127a9979d39bf238324.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.20秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 81 张图片: 5602caf1b4fa49d5a940c9e503458bae.jpg

==================================================
![5602caf1b4fa49d5a940c9e503458bae.jpg](../images/5602caf1b4fa49d5a940c9e503458bae.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.42秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 82 张图片: 56b18105cdd24abaa5999cb6c027f755.jpg

==================================================
![56b18105cdd24abaa5999cb6c027f755.jpg](../images/56b18105cdd24abaa5999cb6c027f755.jpg)
### 响应内容：
```json
{"题目1": "9.06", "题目2": "1300"}
```
### 响应时间：2.76秒
### token用量
- total_tokens: 3235
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 83 张图片: 57a448c20d0d4f31b0974f78b4758a67.jpg

==================================================
![57a448c20d0d4f31b0974f78b4758a67.jpg](../images/57a448c20d0d4f31b0974f78b4758a67.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.42秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 84 张图片: 589c333a2312442eba7938fae330ab27.jpg

==================================================
![589c333a2312442eba7938fae330ab27.jpg](../images/589c333a2312442eba7938fae330ab27.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 85 张图片: 5b8b8bb2865b484d8a489afad55b4b65.jpg

==================================================
![5b8b8bb2865b484d8a489afad55b4b65.jpg](../images/5b8b8bb2865b484d8a489afad55b4b65.jpg)
### 响应内容：
```json
{"题目1": "8.84元", "题目2": "1300人"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 86 张图片: 5bf557b1913d4f43a1e17d106ed7645f.jpg

==================================================
![5bf557b1913d4f43a1e17d106ed7645f.jpg](../images/5bf557b1913d4f43a1e17d106ed7645f.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：1.93秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 87 张图片: 5cad0ee71cfe4b9bb6da151215454687.jpg

==================================================
![5cad0ee71cfe4b9bb6da151215454687.jpg](../images/5cad0ee71cfe4b9bb6da151215454687.jpg)
### 响应内容：
```json
{"题目1": "400", "题目2": "3456"}
```
### 响应时间：2.46秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 88 张图片: 5d0b3cd5c97747bdabd1e96dedd77919.jpg

==================================================
![5d0b3cd5c97747bdabd1e96dedd77919.jpg](../images/5d0b3cd5c97747bdabd1e96dedd77919.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：1.81秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 89 张图片: 5d0f9530b79c4e37882dadd83c8730e0.jpg

==================================================
![5d0f9530b79c4e37882dadd83c8730e0.jpg](../images/5d0f9530b79c4e37882dadd83c8730e0.jpg)
### 响应内容：
```json
{"题目 1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 1157
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 90 张图片: 5f6f2d9dfa3e4f56a3d55056b5bf28c6.jpg

==================================================
![5f6f2d9dfa3e4f56a3d55056b5bf28c6.jpg](../images/5f6f2d9dfa3e4f56a3d55056b5bf28c6.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.06秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 91 张图片: 61d61a083bcb4bf9959002054c7e9c59.jpg

==================================================
![61d61a083bcb4bf9959002054c7e9c59.jpg](../images/61d61a083bcb4bf9959002054c7e9c59.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 92 张图片: 62ce36f065ca438090d6a550d577d08f.jpg

==================================================
![62ce36f065ca438090d6a550d577d08f.jpg](../images/62ce36f065ca438090d6a550d577d08f.jpg)
### 响应内容：
```json
{"题目1": "120", "题目2": "够"}
```
### 响应时间：1.52秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 93 张图片: 65fd8d16b44f4d15b100f4dfef75fa95.jpg

==================================================
![65fd8d16b44f4d15b100f4dfef75fa95.jpg](../images/65fd8d16b44f4d15b100f4dfef75fa95.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 94 张图片: 6864af96eb4142fc83ace034f41a91c8.jpg

==================================================
![6864af96eb4142fc83ace034f41a91c8.jpg](../images/6864af96eb4142fc83ace034f41a91c8.jpg)
### 响应内容：
```json
{"题目1": "1454美元", "题目2": "1.79米"}
```
### 响应时间：2.59秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 95 张图片: 69258f143d5f4db09332474cc4a3303d.jpg

==================================================
![69258f143d5f4db09332474cc4a3303d.jpg](../images/69258f143d5f4db09332474cc4a3303d.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.90秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 96 张图片: 6bc7fb8170384d2aa087f9830d30c698.jpg

==================================================
![6bc7fb8170384d2aa087f9830d30c698.jpg](../images/6bc7fb8170384d2aa087f9830d30c698.jpg)
### 响应内容：
```json
{"题目1": "249元", "题目2": "够"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 1693
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 97 张图片: 6bcc7e34aa2e486d973892deaa90fd35.jpg

==================================================
![6bcc7e34aa2e486d973892deaa90fd35.jpg](../images/6bcc7e34aa2e486d973892deaa90fd35.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱，花费990元"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 1235
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 98 张图片: 6c58550cb0a4427086c80f2d7dfb280a.jpg

==================================================
![6c58550cb0a4427086c80f2d7dfb280a.jpg](../images/6c58550cb0a4427086c80f2d7dfb280a.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.92秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 99 张图片: 6ec6304ce69c41aa9e3d1cb62eac93e9.jpg

==================================================
![6ec6304ce69c41aa9e3d1cb62eac93e9.jpg](../images/6ec6304ce69c41aa9e3d1cb62eac93e9.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.20秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 100 张图片: 6ed01242034c451689817c25873093ef.jpg

==================================================
![6ed01242034c451689817c25873093ef.jpg](../images/6ed01242034c451689817c25873093ef.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1400人"}
```
### 响应时间：3.20秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 101 张图片: 6f8a7831ce534073939e362723bc667d.jpg

==================================================
![6f8a7831ce534073939e362723bc667d.jpg](../images/6f8a7831ce534073939e362723bc667d.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.65秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 102 张图片: 6fee1745c1a34accb733081aa83a4e62.jpg

==================================================
![6fee1745c1a34accb733081aa83a4e62.jpg](../images/6fee1745c1a34accb733081aa83a4e62.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.75秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 103 张图片: 701affc2354449cf870e67315dbbd61a.jpg

==================================================
![701affc2354449cf870e67315dbbd61a.jpg](../images/701affc2354449cf870e67315dbbd61a.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.19秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 104 张图片: 72ca30aed54541a2b4200cb6b62bed64.jpg

==================================================
![72ca30aed54541a2b4200cb6b62bed64.jpg](../images/72ca30aed54541a2b4200cb6b62bed64.jpg)
### 响应内容：
```json
{"题目1": "400千米", "题目2": "3456棵"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 105 张图片: 76d484aa1746422fb8887429c468fd9b.jpg

==================================================
![76d484aa1746422fb8887429c468fd9b.jpg](../images/76d484aa1746422fb8887429c468fd9b.jpg)
### 响应内容：
```json
{"题目1": "178元", "题目2": "够"}
```
### 响应时间：1.82秒
### token用量
- total_tokens: 1693
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 106 张图片: 78a8674d6f2643718aa52bb09a9fbfed.jpg

==================================================
![78a8674d6f2643718aa52bb09a9fbfed.jpg](../images/78a8674d6f2643718aa52bb09a9fbfed.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.96秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 107 张图片: 795178ed2049425cb3f77791f7fa6b53.jpg

==================================================
![795178ed2049425cb3f77791f7fa6b53.jpg](../images/795178ed2049425cb3f77791f7fa6b53.jpg)
### 响应内容：
```json
{"题目 1": "9.6元", "题目 2": "1300人"}
```
### 响应时间：2.46秒
### token用量
- total_tokens: 3238
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 108 张图片: 7a9b357ffd75425d94c83b8aaf9af911.jpg

==================================================
![7a9b357ffd75425d94c83b8aaf9af911.jpg](../images/7a9b357ffd75425d94c83b8aaf9af911.jpg)
### 响应内容：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```
### 响应时间：2.67秒
### token用量
- total_tokens: 3239
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 109 张图片: 7a9b88a938d646a18a2627b34bcc2e99.jpg

==================================================
![7a9b88a938d646a18a2627b34bcc2e99.jpg](../images/7a9b88a938d646a18a2627b34bcc2e99.jpg)
### 响应内容：
```json
{"题目1": "154", "题目2": "够"}
```
### 响应时间：1.60秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 110 张图片: 7ca34f564ac34a86ab59b4241a38e2ce.jpg

==================================================
![7ca34f564ac34a86ab59b4241a38e2ce.jpg](../images/7ca34f564ac34a86ab59b4241a38e2ce.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 111 张图片: 7e23c266f8c04f518a29bffe57b58c6f.jpg

==================================================
![7e23c266f8c04f518a29bffe57b58c6f.jpg](../images/7e23c266f8c04f518a29bffe57b58c6f.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：3.00秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 112 张图片: 7f1badde1aa4450f8f81342dd70f43e5.jpg

==================================================
![7f1badde1aa4450f8f81342dd70f43e5.jpg](../images/7f1badde1aa4450f8f81342dd70f43e5.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.50秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 113 张图片: 811648e7cc5944d58aebbaade26320a8.jpg

==================================================
![811648e7cc5944d58aebbaade26320a8.jpg](../images/811648e7cc5944d58aebbaade26320a8.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.40秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 114 张图片: 82fd7c2a937b4d7cacfbe6ff203a1743.jpg

==================================================
![82fd7c2a937b4d7cacfbe6ff203a1743.jpg](../images/82fd7c2a937b4d7cacfbe6ff203a1743.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：1.71秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 115 张图片: 83c2aea0e99e4e65b326aa3006be69cb.jpg

==================================================
![83c2aea0e99e4e65b326aa3006be69cb.jpg](../images/83c2aea0e99e4e65b326aa3006be69cb.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 116 张图片: 8414a3c7a48b4a8587546713d1be4de7.jpg

==================================================
![8414a3c7a48b4a8587546713d1be4de7.jpg](../images/8414a3c7a48b4a8587546713d1be4de7.jpg)
### 响应内容：
```json
{"题目1": "1452", "题目2": "1.79"}
```
### 响应时间：2.05秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 117 张图片: 852c1f98d0974e819ad8c8cff833fed4.jpg

==================================================
![852c1f98d0974e819ad8c8cff833fed4.jpg](../images/852c1f98d0974e819ad8c8cff833fed4.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 118 张图片: 8622b9fbda6a408e8e977492681b9000.jpg

==================================================
![8622b9fbda6a408e8e977492681b9000.jpg](../images/8622b9fbda6a408e8e977492681b9000.jpg)
### 响应内容：
```json
{"题目1": "租1辆大客车和2辆小客车最省钱"}
```
### 响应时间：1.56秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 119 张图片: 866241cb0a5d4c2ea446357f19fd9527.jpg

==================================================
![866241cb0a5d4c2ea446357f19fd9527.jpg](../images/866241cb0a5d4c2ea446357f19fd9527.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.02秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 120 张图片: 8747669d9baf4abd89076583eb721851.jpg

==================================================
![8747669d9baf4abd89076583eb721851.jpg](../images/8747669d9baf4abd89076583eb721851.jpg)
### 响应内容：
```json
{"题目 1": "16平方分米", "题目 2": "36", "题目 3": "144"}
```
### 响应时间：2.56秒
### token用量
- total_tokens: 3075
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 121 张图片: 87ff291e4cfe4dca9d3bf4c9b92236ef.jpg

==================================================
![87ff291e4cfe4dca9d3bf4c9b92236ef.jpg](../images/87ff291e4cfe4dca9d3bf4c9b92236ef.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.36秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 122 张图片: 884fb4481c954cf8946768f83b9e71a9.jpg

==================================================
![884fb4481c954cf8946768f83b9e71a9.jpg](../images/884fb4481c954cf8946768f83b9e71a9.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 123 张图片: 8a9e78ddac8a42d79190963c0aa60d88.jpg

==================================================
![8a9e78ddac8a42d79190963c0aa60d88.jpg](../images/8a9e78ddac8a42d79190963c0aa60d88.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.09秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 124 张图片: 8db20d2be4354628bcc186c7b1c09b87.jpg

==================================================
![8db20d2be4354628bcc186c7b1c09b87.jpg](../images/8db20d2be4354628bcc186c7b1c09b87.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.70秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 125 张图片: 8e31c018e48a4c1d841c9e68ba4175ef.jpg

==================================================
![8e31c018e48a4c1d841c9e68ba4175ef.jpg](../images/8e31c018e48a4c1d841c9e68ba4175ef.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 126 张图片: 8e60ee07606042a99ec368f275ba9955.jpg

==================================================
![8e60ee07606042a99ec368f275ba9955.jpg](../images/8e60ee07606042a99ec368f275ba9955.jpg)
### 响应内容：
```json
{"题目1": "9.06", "题目2": "1300"}
```
### 响应时间：2.66秒
### token用量
- total_tokens: 3235
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 127 张图片: 8efb4c2319a54615975db5b2daf06322.jpg

==================================================
![8efb4c2319a54615975db5b2daf06322.jpg](../images/8efb4c2319a54615975db5b2daf06322.jpg)
### 响应内容：
```json
{"题目 1": "32平方分米", "题目 2": "36", "题目 3": "144元"}
```
### 响应时间：2.33秒
### token用量
- total_tokens: 3075
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 128 张图片: 8f3b9e5a42d94b15b9c2bcfea9adfc79.jpg

==================================================
![8f3b9e5a42d94b15b9c2bcfea9adfc79.jpg](../images/8f3b9e5a42d94b15b9c2bcfea9adfc79.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.51秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 129 张图片: 909f11127bf84c1eb3a0fcbc2444416f.jpg

==================================================
![909f11127bf84c1eb3a0fcbc2444416f.jpg](../images/909f11127bf84c1eb3a0fcbc2444416f.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.46秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 130 张图片: 916b2d19fc134cfcb4188a09f3f39c91.jpg

==================================================
![916b2d19fc134cfcb4188a09f3f39c91.jpg](../images/916b2d19fc134cfcb4188a09f3f39c91.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 131 张图片: 91ede973e4574ed98b7327f6bc97c82d.jpg

==================================================
![91ede973e4574ed98b7327f6bc97c82d.jpg](../images/91ede973e4574ed98b7327f6bc97c82d.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.43秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 132 张图片: 929ae9c7d52e4544a850d10d64b9eb66.jpg

==================================================
![929ae9c7d52e4544a850d10d64b9eb66.jpg](../images/929ae9c7d52e4544a850d10d64b9eb66.jpg)
### 响应内容：
```json
{"题目1": "租1辆大客车和2辆小客车最省钱"}
```
### 响应时间：1.68秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 133 张图片: 9365597907834c30b22ed57739e7ae40.jpg

==================================================
![9365597907834c30b22ed57739e7ae40.jpg](../images/9365597907834c30b22ed57739e7ae40.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 134 张图片: 94174957c26446d2886ee99d93e1c180.jpg

==================================================
![94174957c26446d2886ee99d93e1c180.jpg](../images/94174957c26446d2886ee99d93e1c180.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：1.62秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 135 张图片: 9423221d7e894451bcc04ae043c35336.jpg

==================================================
![9423221d7e894451bcc04ae043c35336.jpg](../images/9423221d7e894451bcc04ae043c35336.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.46秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 136 张图片: 942674d78b034640a555846856c998bf.jpg

==================================================
![942674d78b034640a555846856c998bf.jpg](../images/942674d78b034640a555846856c998bf.jpg)
### 响应内容：
```json
{"题目1": "16 dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.45秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 137 张图片: 94c1bfa3c6fc41c3b2e561ff3a81efe6.jpg

==================================================
![94c1bfa3c6fc41c3b2e561ff3a81efe6.jpg](../images/94c1bfa3c6fc41c3b2e561ff3a81efe6.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.70秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 138 张图片: 950b17fca98048a4a7b783d6034ff854.jpg

==================================================
![950b17fca98048a4a7b783d6034ff854.jpg](../images/950b17fca98048a4a7b783d6034ff854.jpg)
### 响应内容：
```json
{"题目 1": "1452", "题目 2": "1.79"}
```
### 响应时间：2.22秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 139 张图片: 954a43c3742943d5adc1ee5801123747.jpg

==================================================
![954a43c3742943d5adc1ee5801123747.jpg](../images/954a43c3742943d5adc1ee5801123747.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.99秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 140 张图片: 956ba34653764928816b2ad0ce149d7f.jpg

==================================================
![956ba34653764928816b2ad0ce149d7f.jpg](../images/956ba34653764928816b2ad0ce149d7f.jpg)
### 响应内容：
```json
{"题目1": "租1辆大客车和2辆小客车最省钱"}
```
### 响应时间：1.68秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 141 张图片: 9919147a77574eae82dea0f2d5685201.jpg

==================================================
![9919147a77574eae82dea0f2d5685201.jpg](../images/9919147a77574eae82dea0f2d5685201.jpg)
### 响应内容：
```json
{"题目1": "科技类25人，艺术类12人"}
```
### 响应时间：2.17秒
### token用量
- total_tokens: 1147
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 142 张图片: 99874f83919c424aa9dfceb8462915e0.jpg

==================================================
![99874f83919c424aa9dfceb8462915e0.jpg](../images/99874f83919c424aa9dfceb8462915e0.jpg)
### 响应内容：
```json
{"题目1": "科技类25人，艺术类12人"}
```
### 响应时间：1.80秒
### token用量
- total_tokens: 1147
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 143 张图片: 9a0963909ea04654a3afe5d50f1b7615.jpg

==================================================
![9a0963909ea04654a3afe5d50f1b7615.jpg](../images/9a0963909ea04654a3afe5d50f1b7615.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 144 张图片: 9afedf06949f41718eb165b18e0ed0fb.jpg

==================================================
![9afedf06949f41718eb165b18e0ed0fb.jpg](../images/9afedf06949f41718eb165b18e0ed0fb.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "NAN"}
```
### 响应时间：2.11秒
### token用量
- total_tokens: 1693
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 145 张图片: 9b28fa077d7346b58f873d8926ef41a6.jpg

==================================================
![9b28fa077d7346b58f873d8926ef41a6.jpg](../images/9b28fa077d7346b58f873d8926ef41a6.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.84秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 146 张图片: 9b499f7c27de4b948e4e4e5fc234d338.jpg

==================================================
![9b499f7c27de4b948e4e4e5fc234d338.jpg](../images/9b499f7c27de4b948e4e4e5fc234d338.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.24秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 147 张图片: 9c0ee5afc90b476aae7ed75f3faf1451.jpg

==================================================
![9c0ee5afc90b476aae7ed75f3faf1451.jpg](../images/9c0ee5afc90b476aae7ed75f3faf1451.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.39秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 148 张图片: 9c6101fa0b0c4deaaa09a549494b0f86.jpg

==================================================
![9c6101fa0b0c4deaaa09a549494b0f86.jpg](../images/9c6101fa0b0c4deaaa09a549494b0f86.jpg)
### 响应内容：
```json
{"题目1": "8.96元", "题目2": "3000人"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 149 张图片: a038b48d9a6f42c185420aebe91a54d5.jpg

==================================================
![a038b48d9a6f42c185420aebe91a54d5.jpg](../images/a038b48d9a6f42c185420aebe91a54d5.jpg)
### 响应内容：
```json
{"题目1": "租1辆大客车和2辆小客车最省钱，共花费900元"}
```
### 响应时间：2.30秒
### token用量
- total_tokens: 1236
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 150 张图片: a0479727ffe04f04b3803bf455c10528.jpg

==================================================
![a0479727ffe04f04b3803bf455c10528.jpg](../images/a0479727ffe04f04b3803bf455c10528.jpg)
### 响应内容：
```json
{"题目1": "349", "题目2": "够"}
```
### 响应时间：1.96秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 151 张图片: a0d3235880b9432184d64c47689b76fd.jpg

==================================================
![a0d3235880b9432184d64c47689b76fd.jpg](../images/a0d3235880b9432184d64c47689b76fd.jpg)
### 响应内容：
```json
{"题目1": "租2辆小客车和1辆大客车最省钱"}
```
### 响应时间：1.72秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 152 张图片: a19122789ad140e18f141fa3e5c853b5.jpg

==================================================
![a19122789ad140e18f141fa3e5c853b5.jpg](../images/a19122789ad140e18f141fa3e5c853b5.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 153 张图片: a1b67fbd1e554656a105d85cf419a157.jpg

==================================================
![a1b67fbd1e554656a105d85cf419a157.jpg](../images/a1b67fbd1e554656a105d85cf419a157.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.75秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 154 张图片: a257d263622147a0b4ecfb3c690893c7.jpg

==================================================
![a257d263622147a0b4ecfb3c690893c7.jpg](../images/a257d263622147a0b4ecfb3c690893c7.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 155 张图片: a2d5f09bc5ce4090bb693a28f3276934.jpg

==================================================
![a2d5f09bc5ce4090bb693a28f3276934.jpg](../images/a2d5f09bc5ce4090bb693a28f3276934.jpg)
### 响应内容：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```
### 响应时间：2.96秒
### token用量
- total_tokens: 3077
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 156 张图片: a4719a75e2174b62977e0f1bf7c6d133.jpg

==================================================
![a4719a75e2174b62977e0f1bf7c6d133.jpg](../images/a4719a75e2174b62977e0f1bf7c6d133.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.88秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 157 张图片: a4e9642d03fb4da3969dcfed935acac4.jpg

==================================================
![a4e9642d03fb4da3969dcfed935acac4.jpg](../images/a4e9642d03fb4da3969dcfed935acac4.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：2.27秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 158 张图片: a5464c6391354d41b4d584e8cd4d186a.jpg

==================================================
![a5464c6391354d41b4d584e8cd4d186a.jpg](../images/a5464c6391354d41b4d584e8cd4d186a.jpg)
### 响应内容：
```json
{"题目 1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.93秒
### token用量
- total_tokens: 1157
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 159 张图片: a67ecf878f93408e80ed18e8d726b722.jpg

==================================================
![a67ecf878f93408e80ed18e8d726b722.jpg](../images/a67ecf878f93408e80ed18e8d726b722.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.70秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 160 张图片: a6b98210e3c04a608d6a08d0bca348c2.jpg

==================================================
![a6b98210e3c04a608d6a08d0bca348c2.jpg](../images/a6b98210e3c04a608d6a08d0bca348c2.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.77秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 161 张图片: a725b9de926c401b89be310de7e0c131.jpg

==================================================
![a725b9de926c401b89be310de7e0c131.jpg](../images/a725b9de926c401b89be310de7e0c131.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 162 张图片: a7559ef804ef42d494348869b4f625c6.jpg

==================================================
![a7559ef804ef42d494348869b4f625c6.jpg](../images/a7559ef804ef42d494348869b4f625c6.jpg)
### 响应内容：
```json
{"题目 1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.39秒
### token用量
- total_tokens: 1157
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 163 张图片: a793237b72884136874afaaa0d6a4ced.jpg

==================================================
![a793237b72884136874afaaa0d6a4ced.jpg](../images/a793237b72884136874afaaa0d6a4ced.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.10秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 164 张图片: aa4242739fd746b8aecef91dc621bb4f.jpg

==================================================
![aa4242739fd746b8aecef91dc621bb4f.jpg](../images/aa4242739fd746b8aecef91dc621bb4f.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 1765
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 165 张图片: ab0b77b31625487c82db63a3cd12add9.jpg

==================================================
![ab0b77b31625487c82db63a3cd12add9.jpg](../images/ab0b77b31625487c82db63a3cd12add9.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.44秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 166 张图片: ab3ee8f0b39d46769fa44e50dd239150.jpg

==================================================
![ab3ee8f0b39d46769fa44e50dd239150.jpg](../images/ab3ee8f0b39d46769fa44e50dd239150.jpg)
### 响应内容：
```json
{"题目1": "16 dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.51秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 167 张图片: ab78c0731e034dd297ccc362726f58fa.jpg

==================================================
![ab78c0731e034dd297ccc362726f58fa.jpg](../images/ab78c0731e034dd297ccc362726f58fa.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：1.77秒
### token用量
- total_tokens: 1767
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 168 张图片: acf8bb8e49f84a14b227b45506a0f975.jpg

==================================================
![acf8bb8e49f84a14b227b45506a0f975.jpg](../images/acf8bb8e49f84a14b227b45506a0f975.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.98秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 169 张图片: af25a2d303534cb88beccb1e4311c72a.jpg

==================================================
![af25a2d303534cb88beccb1e4311c72a.jpg](../images/af25a2d303534cb88beccb1e4311c72a.jpg)
### 响应内容：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```
### 响应时间：1.74秒
### token用量
- total_tokens: 2202
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 170 张图片: b0f76e1e122949feb9c3b5b6b4e0109d.jpg

==================================================
![b0f76e1e122949feb9c3b5b6b4e0109d.jpg](../images/b0f76e1e122949feb9c3b5b6b4e0109d.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 171 张图片: b195031a8298438c94b6777396d06ca7.jpg

==================================================
![b195031a8298438c94b6777396d06ca7.jpg](../images/b195031a8298438c94b6777396d06ca7.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：1.84秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 172 张图片: b2e282b3eb6b497b916bdc81ae7c540a.jpg

==================================================
![b2e282b3eb6b497b916bdc81ae7c540a.jpg](../images/b2e282b3eb6b497b916bdc81ae7c540a.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.58秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 173 张图片: b740b345eb7742b9b8814788d7b2a379.jpg

==================================================
![b740b345eb7742b9b8814788d7b2a379.jpg](../images/b740b345eb7742b9b8814788d7b2a379.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 174 张图片: b7923b2dd024478fb38a6e2272002604.jpg

==================================================
![b7923b2dd024478fb38a6e2272002604.jpg](../images/b7923b2dd024478fb38a6e2272002604.jpg)
### 响应内容：
```json
{"题目1": "9.6元", "题目2": "1300人"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 3236
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 175 张图片: b7ae5c1b43cc4a61899f7396d07a078f.jpg

==================================================
![b7ae5c1b43cc4a61899f7396d07a078f.jpg](../images/b7ae5c1b43cc4a61899f7396d07a078f.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "不够"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 176 张图片: b7b2178981f34030a0f3c14d70aea385.jpg

==================================================
![b7b2178981f34030a0f3c14d70aea385.jpg](../images/b7b2178981f34030a0f3c14d70aea385.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：1.30秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 177 张图片: bb02a190ca4943d09a71f243fd5c2ffc.jpg

==================================================
![bb02a190ca4943d09a71f243fd5c2ffc.jpg](../images/bb02a190ca4943d09a71f243fd5c2ffc.jpg)
### 响应内容：
```json
{"题目 1": "租1辆大客车和2辆小客车最省钱"}
```
### 响应时间：1.60秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 178 张图片: bb12241589af4f1ba4f951b5e871f686.jpg

==================================================
![bb12241589af4f1ba4f951b5e871f686.jpg](../images/bb12241589af4f1ba4f951b5e871f686.jpg)
### 响应内容：
```json
{"题目 1": "249", "题目 2": "够"}
```
### 响应时间：1.59秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 179 张图片: bbc6008549df4037a276506fbf75b4c3.jpg

==================================================
![bbc6008549df4037a276506fbf75b4c3.jpg](../images/bbc6008549df4037a276506fbf75b4c3.jpg)
### 响应内容：
```json
{"题目 1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 1157
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 180 张图片: bd01bfe7e4ae4be5bc6003dad1a0c78b.jpg

==================================================
![bd01bfe7e4ae4be5bc6003dad1a0c78b.jpg](../images/bd01bfe7e4ae4be5bc6003dad1a0c78b.jpg)
### 响应内容：
```json
{"题目1": "188", "题目2": "够"}
```
### 响应时间：2.16秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 181 张图片: be484976ee5d4391801c5db31fbb7862.jpg

==================================================
![be484976ee5d4391801c5db31fbb7862.jpg](../images/be484976ee5d4391801c5db31fbb7862.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：1.30秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 182 张图片: bf718bb0b6544d3f807f9f453e3fce0a.jpg

==================================================
![bf718bb0b6544d3f807f9f453e3fce0a.jpg](../images/bf718bb0b6544d3f807f9f453e3fce0a.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.19秒
### token用量
- total_tokens: 2201
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 183 张图片: bf99332d982740579c52f1512110d33a.jpg

==================================================
![bf99332d982740579c52f1512110d33a.jpg](../images/bf99332d982740579c52f1512110d33a.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 184 张图片: c0b4dbebc5414689b4f9bd00a55c9e6d.jpg

==================================================
![c0b4dbebc5414689b4f9bd00a55c9e6d.jpg](../images/c0b4dbebc5414689b4f9bd00a55c9e6d.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.18秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 185 张图片: c13df5864ad64900965a94ff4dc67e25.jpg

==================================================
![c13df5864ad64900965a94ff4dc67e25.jpg](../images/c13df5864ad64900965a94ff4dc67e25.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.26秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 186 张图片: c1454de9b204405b871d0f25427830e5.jpg

==================================================
![c1454de9b204405b871d0f25427830e5.jpg](../images/c1454de9b204405b871d0f25427830e5.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.97秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 187 张图片: c1a6b28474384aafbd96583aa7bec7cb.jpg

==================================================
![c1a6b28474384aafbd96583aa7bec7cb.jpg](../images/c1a6b28474384aafbd96583aa7bec7cb.jpg)
### 响应内容：
```json
{"题目 1": "188", "题目 2": "够"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 188 张图片: c1b79cddb98c49dfb9e0feb27bed1ec4.jpg

==================================================
![c1b79cddb98c49dfb9e0feb27bed1ec4.jpg](../images/c1b79cddb98c49dfb9e0feb27bed1ec4.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.62秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 189 张图片: c1f7e24e349a4c78b24abb07eed62cf9.jpg

==================================================
![c1f7e24e349a4c78b24abb07eed62cf9.jpg](../images/c1f7e24e349a4c78b24abb07eed62cf9.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 190 张图片: c30be5eba7e042b49319f695ca3de1d8.jpg

==================================================
![c30be5eba7e042b49319f695ca3de1d8.jpg](../images/c30be5eba7e042b49319f695ca3de1d8.jpg)
### 响应内容：
```json
{"题目1": "9.06", "题目2": "1300"}
```
### 响应时间：2.34秒
### token用量
- total_tokens: 3235
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 191 张图片: c31a05becaee434a9d9aec381efdcfc9.jpg

==================================================
![c31a05becaee434a9d9aec381efdcfc9.jpg](../images/c31a05becaee434a9d9aec381efdcfc9.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.91秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 192 张图片: c490d94e188e4492b29859d9a33eab11.jpg

==================================================
![c490d94e188e4492b29859d9a33eab11.jpg](../images/c490d94e188e4492b29859d9a33eab11.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.84秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 193 张图片: c8311ca0e96947a094630c2d976d58be.jpg

==================================================
![c8311ca0e96947a094630c2d976d58be.jpg](../images/c8311ca0e96947a094630c2d976d58be.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 194 张图片: c8c08fb68ccb4e5dbaa9981e2bf2f770.jpg

==================================================
![c8c08fb68ccb4e5dbaa9981e2bf2f770.jpg](../images/c8c08fb68ccb4e5dbaa9981e2bf2f770.jpg)
### 响应内容：
```json
{"题目1": "16 dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.46秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 195 张图片: ca73ba16447549dda1ec6d9e905bd153.jpg

==================================================
![ca73ba16447549dda1ec6d9e905bd153.jpg](../images/ca73ba16447549dda1ec6d9e905bd153.jpg)
### 响应内容：
```json
{"题目 1": "174", "题目 2": "够"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 196 张图片: cca96ff43d23410b92f92839a868c511.jpg

==================================================
![cca96ff43d23410b92f92839a868c511.jpg](../images/cca96ff43d23410b92f92839a868c511.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 197 张图片: cf37667e26d644ffac3184c4bdf73cc6.jpg

==================================================
![cf37667e26d644ffac3184c4bdf73cc6.jpg](../images/cf37667e26d644ffac3184c4bdf73cc6.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 198 张图片: cfa992a0a8c24552aa665734aef92568.jpg

==================================================
![cfa992a0a8c24552aa665734aef92568.jpg](../images/cfa992a0a8c24552aa665734aef92568.jpg)
### 响应内容：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```
### 响应时间：2.36秒
### token用量
- total_tokens: 3239
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 199 张图片: d0a8e68d325f476a83990dca2175e038.jpg

==================================================
![d0a8e68d325f476a83990dca2175e038.jpg](../images/d0a8e68d325f476a83990dca2175e038.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.09秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 200 张图片: d0e95ae547ea467a8fc469f332ce4418.jpg

==================================================
![d0e95ae547ea467a8fc469f332ce4418.jpg](../images/d0e95ae547ea467a8fc469f332ce4418.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：2.02秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 201 张图片: d364e488ca5e4ce983bd53b054cbe88e.jpg

==================================================
![d364e488ca5e4ce983bd53b054cbe88e.jpg](../images/d364e488ca5e4ce983bd53b054cbe88e.jpg)
### 响应内容：
```json
{"题目1": "180", "题目2": "够"}
```
### 响应时间：2.62秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 202 张图片: d4593c1b340548cd9a815f02faecabfd.jpg

==================================================
![d4593c1b340548cd9a815f02faecabfd.jpg](../images/d4593c1b340548cd9a815f02faecabfd.jpg)
### 响应内容：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```
### 响应时间：2.16秒
### token用量
- total_tokens: 2204
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 203 张图片: d667f6bec36c4e24979c3ec986fa5439.jpg

==================================================
![d667f6bec36c4e24979c3ec986fa5439.jpg](../images/d667f6bec36c4e24979c3ec986fa5439.jpg)
### 响应内容：
```json
{"题目1": "租1辆大客车和2辆小客车最省钱"}
```
### 响应时间：1.65秒
### token用量
- total_tokens: 1228
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 204 张图片: d736943e30614a8281f75344e2669c37.jpg

==================================================
![d736943e30614a8281f75344e2669c37.jpg](../images/d736943e30614a8281f75344e2669c37.jpg)
### 响应内容：
```json
{"题目1": "278", "题目2": "够"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 205 张图片: d76947e1ae834d2eadec973b358ea5d2.jpg

==================================================
![d76947e1ae834d2eadec973b358ea5d2.jpg](../images/d76947e1ae834d2eadec973b358ea5d2.jpg)
### 响应内容：
```json
{"题目1": "178元", "题目2": "够"}
```
### 响应时间：3.12秒
### token用量
- total_tokens: 1693
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 206 张图片: d77a21eef7c4445ab0cb4230214dda68.jpg

==================================================
![d77a21eef7c4445ab0cb4230214dda68.jpg](../images/d77a21eef7c4445ab0cb4230214dda68.jpg)
### 响应内容：
```json
{"题目1": "7.96元", "题目2": "1300人"}
```
### 响应时间：2.50秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 207 张图片: d81b41440848418183a4cdbdcacebe00.jpg

==================================================
![d81b41440848418183a4cdbdcacebe00.jpg](../images/d81b41440848418183a4cdbdcacebe00.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：3.32秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 208 张图片: d8440545f6bd4da8ae5d47f13d1d605b.jpg

==================================================
![d8440545f6bd4da8ae5d47f13d1d605b.jpg](../images/d8440545f6bd4da8ae5d47f13d1d605b.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.08秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 209 张图片: d86ed97c5d6e41cf99c7bbe4ca6446ab.jpg

==================================================
![d86ed97c5d6e41cf99c7bbe4ca6446ab.jpg](../images/d86ed97c5d6e41cf99c7bbe4ca6446ab.jpg)
### 响应内容：
```json
{"题目 1": "250", "题目 2": "够"}
```
### 响应时间：2.25秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 210 张图片: d92c361afe9c4843b1ca5d72e5004f93.jpg

==================================================
![d92c361afe9c4843b1ca5d72e5004f93.jpg](../images/d92c361afe9c4843b1ca5d72e5004f93.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 211 张图片: d97e09b64ad64fa2b178bc636e22b2e2.jpg

==================================================
![d97e09b64ad64fa2b178bc636e22b2e2.jpg](../images/d97e09b64ad64fa2b178bc636e22b2e2.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.40秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 212 张图片: db09644f56eb42fd85f3120693cd7ff0.jpg

==================================================
![db09644f56eb42fd85f3120693cd7ff0.jpg](../images/db09644f56eb42fd85f3120693cd7ff0.jpg)
### 响应内容：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```
### 响应时间：2.11秒
### token用量
- total_tokens: 3077
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 213 张图片: db126b609b5747bc88c60ea23c41227b.jpg

==================================================
![db126b609b5747bc88c60ea23c41227b.jpg](../images/db126b609b5747bc88c60ea23c41227b.jpg)
### 响应内容：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```
### 响应时间：3.23秒
### token用量
- total_tokens: 3239
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 214 张图片: db44e162dcdb4a9aad070a720c6ce12b.jpg

==================================================
![db44e162dcdb4a9aad070a720c6ce12b.jpg](../images/db44e162dcdb4a9aad070a720c6ce12b.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 215 张图片: dc4ed800746d45e1a74fdf32e50f7af5.jpg

==================================================
![dc4ed800746d45e1a74fdf32e50f7af5.jpg](../images/dc4ed800746d45e1a74fdf32e50f7af5.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 216 张图片: de46d2b212fb494d99438d62eeb33d32.jpg

==================================================
![de46d2b212fb494d99438d62eeb33d32.jpg](../images/de46d2b212fb494d99438d62eeb33d32.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.59秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 217 张图片: debe18caf1a94f369e3bc83436f53d5f.jpg

==================================================
![debe18caf1a94f369e3bc83436f53d5f.jpg](../images/debe18caf1a94f369e3bc83436f53d5f.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 218 张图片: e0ca351fbf1e461eaa068f066dbc7d8a.jpg

==================================================
![e0ca351fbf1e461eaa068f066dbc7d8a.jpg](../images/e0ca351fbf1e461eaa068f066dbc7d8a.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.77秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 219 张图片: e245a200c3ba4d09bf08080e68fff425.jpg

==================================================
![e245a200c3ba4d09bf08080e68fff425.jpg](../images/e245a200c3ba4d09bf08080e68fff425.jpg)
### 响应内容：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.56秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 220 张图片: e48e28f69fe54e69845748fd0b4e55df.jpg

==================================================
![e48e28f69fe54e69845748fd0b4e55df.jpg](../images/e48e28f69fe54e69845748fd0b4e55df.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.94秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 221 张图片: e5bdc408699b44fb8a0b6ce84d3e5821.jpg

==================================================
![e5bdc408699b44fb8a0b6ce84d3e5821.jpg](../images/e5bdc408699b44fb8a0b6ce84d3e5821.jpg)
### 响应内容：
```json
{"题目 1": "NAN"}
```
### 响应时间：1.79秒
### token用量
- total_tokens: 1140
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 222 张图片: e5d7d8ec61234ba1ba91261aa7cc57dd.jpg

==================================================
![e5d7d8ec61234ba1ba91261aa7cc57dd.jpg](../images/e5d7d8ec61234ba1ba91261aa7cc57dd.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.46秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 223 张图片: e6345b5ca7184b369bbf7b7b8359e10a.jpg

==================================================
![e6345b5ca7184b369bbf7b7b8359e10a.jpg](../images/e6345b5ca7184b369bbf7b7b8359e10a.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.54秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 224 张图片: e634f3460c7e43f7a55898feb67565e7.jpg

==================================================
![e634f3460c7e43f7a55898feb67565e7.jpg](../images/e634f3460c7e43f7a55898feb67565e7.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：2.90秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 225 张图片: e66445359c8b49029da3b35e5c531ec7.jpg

==================================================
![e66445359c8b49029da3b35e5c531ec7.jpg](../images/e66445359c8b49029da3b35e5c531ec7.jpg)
### 响应内容：
```json
{"题目1": "16平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.71秒
### token用量
- total_tokens: 3074
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 226 张图片: e6ae35a5b7604740a5b0937f18bb179c.jpg

==================================================
![e6ae35a5b7604740a5b0937f18bb179c.jpg](../images/e6ae35a5b7604740a5b0937f18bb179c.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.16秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 227 张图片: e8bc848c374941899f27bf34c8b01511.jpg

==================================================
![e8bc848c374941899f27bf34c8b01511.jpg](../images/e8bc848c374941899f27bf34c8b01511.jpg)
### 响应内容：
```json
{"题目1": "178", "题目2": "够"}
```
### 响应时间：2.22秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 228 张图片: e8eb42f0ecd1483cb53d3eadd698b94a.jpg

==================================================
![e8eb42f0ecd1483cb53d3eadd698b94a.jpg](../images/e8eb42f0ecd1483cb53d3eadd698b94a.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.12秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 229 张图片: eaa6bcb3a1014b8aa5a60752a11b6790.jpg

==================================================
![eaa6bcb3a1014b8aa5a60752a11b6790.jpg](../images/eaa6bcb3a1014b8aa5a60752a11b6790.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：1.95秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 230 张图片: ec521200e636430e9dbdaf6510ddebd9.jpg

==================================================
![ec521200e636430e9dbdaf6510ddebd9.jpg](../images/ec521200e636430e9dbdaf6510ddebd9.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.62秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 231 张图片: ee194ecb8ba847479c8df4ed64732e9b.jpg

==================================================
![ee194ecb8ba847479c8df4ed64732e9b.jpg](../images/ee194ecb8ba847479c8df4ed64732e9b.jpg)
### 响应内容：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```
### 响应时间：3.26秒
### token用量
- total_tokens: 3237
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 232 张图片: f12d67b2251e494aad7fe60cf97f2950.jpg

==================================================
![f12d67b2251e494aad7fe60cf97f2950.jpg](../images/f12d67b2251e494aad7fe60cf97f2950.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 233 张图片: f162055451674e86aad76ea4ce46056f.jpg

==================================================
![f162055451674e86aad76ea4ce46056f.jpg](../images/f162055451674e86aad76ea4ce46056f.jpg)
### 响应内容：
```json
{"题目1": "租2辆大客车和1辆小客车最省钱"}
```
### 响应时间：1.41秒
### token用量
- total_tokens: 1229
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 234 张图片: f2835062578c4f69b2a5091137aae9fc.jpg

==================================================
![f2835062578c4f69b2a5091137aae9fc.jpg](../images/f2835062578c4f69b2a5091137aae9fc.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.63秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 235 张图片: f3aa4880b0784f17b2fec5823093294d.jpg

==================================================
![f3aa4880b0784f17b2fec5823093294d.jpg](../images/f3aa4880b0784f17b2fec5823093294d.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 236 张图片: f3c8da7f9c384c52a565b2c00537eb00.jpg

==================================================
![f3c8da7f9c384c52a565b2c00537eb00.jpg](../images/f3c8da7f9c384c52a565b2c00537eb00.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.54秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 237 张图片: f7b975a709a44539bd5e2b22d70e7acd.jpg

==================================================
![f7b975a709a44539bd5e2b22d70e7acd.jpg](../images/f7b975a709a44539bd5e2b22d70e7acd.jpg)
### 响应内容：
```json
{"题目 1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.90秒
### token用量
- total_tokens: 1157
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 238 张图片: fa3d1035af134585b5a25bc2c95d29cb.jpg

==================================================
![fa3d1035af134585b5a25bc2c95d29cb.jpg](../images/fa3d1035af134585b5a25bc2c95d29cb.jpg)
### 响应内容：
```json
{"题目1": "32平方分米", "题目2": "36页", "题目3": "144元"}
```
### 响应时间：2.30秒
### token用量
- total_tokens: 3073
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 239 张图片: fb79b851e72c46eb8f16c04b13b13750.jpg

==================================================
![fb79b851e72c46eb8f16c04b13b13750.jpg](../images/fb79b851e72c46eb8f16c04b13b13750.jpg)
### 响应内容：
```json
{"题目 1": "1454", "题目 2": "1.79"}
```
### 响应时间：1.66秒
### token用量
- total_tokens: 1766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 240 张图片: fbaddaebde144d8e9e5468822939a160.jpg

==================================================
![fbaddaebde144d8e9e5468822939a160.jpg](../images/fbaddaebde144d8e9e5468822939a160.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：1.66秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 241 张图片: fc26dbaf7deb4845a92444ec41676f49.jpg

==================================================
![fc26dbaf7deb4845a92444ec41676f49.jpg](../images/fc26dbaf7deb4845a92444ec41676f49.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：2.03秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 242 张图片: fcb037c618e44c3cafa85afe38a53750.jpg

==================================================
![fcb037c618e44c3cafa85afe38a53750.jpg](../images/fcb037c618e44c3cafa85afe38a53750.jpg)
### 响应内容：
```json
{"题目1": "320", "题目2": "3456"}
```
### 响应时间：2.10秒
### token用量
- total_tokens: 2200
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 243 张图片: fdc434c57a544bb7a0c58cdc9bfd605d.jpg

==================================================
![fdc434c57a544bb7a0c58cdc9bfd605d.jpg](../images/fdc434c57a544bb7a0c58cdc9bfd605d.jpg)
### 响应内容：
```json
{"题目1": "参加科技类的学生有25人，参加艺术类的学生有12人"}
```
### 响应时间：1.25秒
### token用量
- total_tokens: 1156
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 244 张图片: fe879fda4abc422a8f083bca7b077130.jpg

==================================================
![fe879fda4abc422a8f083bca7b077130.jpg](../images/fe879fda4abc422a8f083bca7b077130.jpg)
### 响应内容：
```json
{"题目1": "1454", "题目2": "1.79"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 1764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 245 张图片: fee822dde47b40a9b4c8c9c87a1049e6.jpg

==================================================
![fee822dde47b40a9b4c8c9c87a1049e6.jpg](../images/fee822dde47b40a9b4c8c9c87a1049e6.jpg)
### 响应内容：
```json
{"题目1": "176", "题目2": "够"}
```
### 响应时间：2.06秒
### token用量
- total_tokens: 1692
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
所有图片处理完成！

==================================================
