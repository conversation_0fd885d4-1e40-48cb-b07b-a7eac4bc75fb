**使用模型ID：** doubao-1-5-vision-pro-32k-250115

**使用图片文件夹：** roboflow_yolo_result/images_2025-07-30_11-39-16

## 错题

- 第 1 张图片: 00d48f4de24443e2850e83adc75b30f8_jpg.rf.2a2fdd270eb20c254897d6daac17e5df_with_roboflow_yolo.jpg
- 第 3 张图片: 032f1a9d2ba248c381a1d9609749defa_jpg.rf.71177523d1595e54a02b60ff2ebf6283_with_roboflow_yolo.jpg
- 第 6 张图片: 048c57626b5f40db9b5070cbf35df814_jpg.rf.db870564d907d87314c67a83bc3da1dc_with_roboflow_yolo.jpg
- 第 7 张图片: 05b29381a30043f1b7cea3088b2540d8_jpg.rf.75f279aa470ec142e0a27c96cfe63778_with_roboflow_yolo.jpg
- 第 9 张图片: 05dc746beae84e05a416956868cd8b7b_jpg.rf.c57c9fa4ce36a67bc163b8d0c802bd9f_with_roboflow_yolo.jpg
- 第 11 张图片: 08c8d03749cb400a8e58acc57bb335e7_jpg.rf.49adf22404961bbf511a59493ac44f42_with_roboflow_yolo.jpg
- 第 12 张图片: 0923a83aece0436fa26e2475e610d979_jpg.rf.b220d281c3ee11eee6334f15b3fff5be_with_roboflow_yolo.jpg
- 第 13 张图片: 098d0d2a16994bafa8d537fa455164e9_jpg.rf.95d096079ca09bf76693a13a6220a83a_with_roboflow_yolo.jpg
- 第 14 张图片: 0af998c399404b1f9866d527becaab46_jpg.rf.df3bff649bdacfd253142bef6d15eec5_with_roboflow_yolo.jpg
- 第 15 张图片: 0b7a3f01188944448ead2243005895c6_jpg.rf.6344fdcf6b2bd2999655e23b326c4ba8_with_roboflow_yolo.jpg
- 第 20 张图片: 10135553394148149ced8e0d61d051fd_jpg.rf.b99bb480549b95e2bc8a57940654bba5_with_roboflow_yolo.jpg
- 第 23 张图片: 164d5b9000354b38b9a01929990eb1c7_jpg.rf.35990c1cda53d495563b75c5f5ef9b50_with_roboflow_yolo.jpg
- 第 24 张图片: 1668c5c1ad044acdab05380f58188af3_jpg.rf.d1e313374cc8437d92d6331fae1c8488_with_roboflow_yolo.jpg
- 第 26 张图片: 1936bd1627be45718d42ea2cb7362039_jpg.rf.3b9fde623503b71e2eac8009913ed169_with_roboflow_yolo.jpg
- 第 27 张图片: 1a4c41280e304f3b878868c321953be4_jpg.rf.b953d1838bc2bb357c7c3a8fd1a194f6_with_roboflow_yolo.jpg
- 第 28 张图片: 1ab719e3e6cd4bf1b6611ccb8eb5286f_jpg.rf.6c010e86247a2247149760f8c9e015f1_with_roboflow_yolo.jpg
- 第 29 张图片: 1caa64a8beed4b119dddd6c90e46b5e3_jpg.rf.08c810a2cc236b4bb8daeba7440f83ea_with_roboflow_yolo.jpg
- 第 30 张图片: 1d68e071aa5f4b38b128f4ead6723b15_jpg.rf.bd10825aca1fc367ade1fc31a8be25f0_with_roboflow_yolo.jpg
- 第 31 张图片: 1dca2db5e9124fe3b95c87fde75d0fd0_jpg.rf.99d8f16d26d191f0814a2a3a823f4f86_with_roboflow_yolo.jpg
- 第 32 张图片: 1e51e5fb9ff34b74adec856e815cb0bd_jpg.rf.6e1d87e27a2f7c9f63b29e45e159f12d_with_roboflow_yolo.jpg
- 第 34 张图片: 1f0d28f5c3cc490697bf73cf4b337692_jpg.rf.96dc268694430dd8f7df46ff5417d1f9_with_roboflow_yolo.jpg
- 第 35 张图片: 221cf6fe34fc43ac8bc998e1d5a6d432_jpg.rf.dfbea28412d0ec71655137376303005a_with_roboflow_yolo.jpg
- 第 36 张图片: 2371fb916eb34b3fbc81a595974ce825_jpg.rf.e2a91a5b58eab5322cf16efc668559e1_with_roboflow_yolo.jpg
- 第 40 张图片: 287f05d42b8c49de91fa53bffa3e3874_jpg.rf.b6f9597573d2401245741b63b369f686_with_roboflow_yolo.jpg
- 第 42 张图片: 29651d0ee3ae4461958ae1daa527d85d_jpg.rf.8402349badd9ab45aebd8f9d7a35a532_with_roboflow_yolo.jpg
- 第 45 张图片: 2ca5b5db868d4c95a192de2cf08da8d9_jpg.rf.4165d98c4a65cdb45ce2642811c4ef2d_with_roboflow_yolo.jpg
- 第 46 张图片: 2e256f606ebe4cad9873aa4cc0109086_jpg.rf.f8278953757aefc1d2fc186e65f96fd5_with_roboflow_yolo.jpg
- 第 50 张图片: 30f41a55e8cb41dea41d52c0709483f9_jpg.rf.d975e7cbe1d2d867b398dc535b678e0a_with_roboflow_yolo.jpg
- 第 51 张图片: 31c9e668f03e4fdd949d20ec98475483_jpg.rf.ae0392f4bb232eca9e81b63360e5d41f_with_roboflow_yolo.jpg
- 第 53 张图片: 324d933e808a43e19c64b034ad59dd37_jpg.rf.0522fbc093da731131ad88e1ec0a4a64_with_roboflow_yolo.jpg
- 第 57 张图片: 36f80e6edae440649a199bc69c745d32_jpg.rf.e4af104643ec6691a492bb3c39acd66e_with_roboflow_yolo.jpg
- 第 59 张图片: 39236aeaf1b1444f8b3b33aa72a4cdbd_jpg.rf.f6a841779cd96ecac82cf64a224e8cdb_with_roboflow_yolo.jpg
- 第 60 张图片: 3968ef8d68704c769c20a6d97fe93927_jpg.rf.40810119b9aedf77edf1dbfad54ab075_with_roboflow_yolo.jpg
- 第 64 张图片: 3c6cc211d8d54727be1c02eeb7ce5b0a_jpg.rf.55a421676f6caaba62ec37473cef7cbb_with_roboflow_yolo.jpg
- 第 67 张图片: 3ee53f7b2b8a46f7b1915aacdcf262b5_jpg.rf.7772dce58b54bd20e09558eff3883dc0_with_roboflow_yolo.jpg
- 第 70 张图片: 422412df03164db2abfc4eac955c45c9_jpg.rf.cd6fc48ff9b454281d95da21737ce4d1_with_roboflow_yolo.jpg
- 第 71 张图片: 42321da7346f42a3959ef8ece20ae2b9_jpg.rf.a376ca54fc2b46f6d6a5a4740a7e98fa_with_roboflow_yolo.jpg
- 第 75 张图片: 44c621f7e0bd40a19dfff8be9e3c1a7b_jpg.rf.16986bf66103dbcc7eace35b65bd21e4_with_roboflow_yolo.jpg
- 第 80 张图片: 488a82293a954fdda31117669fa156aa_jpg.rf.a49e1920999604f72c7bf3b581eb2293_with_roboflow_yolo.jpg
- 第 81 张图片: 4a34106f23714598882f8bf3f00e40d9_jpg.rf.474f385dad6df3d53a2a46f84b52bd30_with_roboflow_yolo.jpg
- 第 82 张图片: 4a41447e5e3e479ba7fcec54036e04ec_jpg.rf.f313993d1446d6217f960d6506b894dd_with_roboflow_yolo.jpg
- 第 85 张图片: 4bc75919268e4f91821a8ce1f7736545_jpg.rf.1b45e387b74ea451d506c160eaa8a9cb_with_roboflow_yolo.jpg
- 第 91 张图片: 50a0335b41f9419ab2b350af6775fe02_jpg.rf.d50d97f01d992130be699038832b040d_with_roboflow_yolo.jpg
- 第 93 张图片: 5459d36a8bb241d4a8b94c5af5bdbc02_jpg.rf.9998d58134d9f79a7c637897b75051ed_with_roboflow_yolo.jpg
- 第 97 张图片: 582ba96857834be2a810437b2b2720a2_jpg.rf.ede4dd3296b4babc4eed3cbdd6d2a94e_with_roboflow_yolo.jpg
- 第 98 张图片: 5861d45c230f4d418aa2c422bfbfa2a5_jpg.rf.87b1c1353d01b4fb0692198c8a9f7d58_with_roboflow_yolo.jpg
- 第 105 张图片: 5b47fc0811e343269b0a072aa3715659_jpg.rf.7146b894853abfab981c7490033c66ee_with_roboflow_yolo.jpg
- 第 106 张图片: 5ca6a811afac4ca7991eda31a4c5f38b_jpg.rf.6e97a1e71ee4a55d61c545310fb6caa2_with_roboflow_yolo.jpg
- 第 107 张图片: 5d5fb8da253e4b5b8684dfc77506b0ba_jpg.rf.1822bf7b75899a2520df710208f2d979_with_roboflow_yolo.jpg
- 第 108 张图片: 5e679b36df0948f798882f40adfe2c74_jpg.rf.dbf959e10c1a9506e22e1fad3d7390f6_with_roboflow_yolo.jpg
- 第 109 张图片: 60e69e1f36aa4a78994eef7bf27ab460_jpg.rf.486d48168d3e7aae83d43b20a8f680bd_with_roboflow_yolo.jpg
- 第 113 张图片: 6642d9ce1a43428098a30b44c44f6d10_jpg.rf.4afe07df588b0d1e0eb82031537f964a_with_roboflow_yolo.jpg
- 第 114 张图片: 667358c69315426995762020f45706e6_jpg.rf.2016e70075eb1b044eec31c4a3e8ce3f_with_roboflow_yolo.jpg
- 第 115 张图片: 674a99bbad7f4f91b990c072b3d1e774_jpg.rf.6d28a2ef35eb02fc16e40d25515a0944_with_roboflow_yolo.jpg
- 第 116 张图片: 6837fbb7cad94e508e0866d4229af32e_jpg.rf.283c1fabfe10fa38eebfb414afbdb805_with_roboflow_yolo.jpg
- 第 117 张图片: 684bd0aaa85e46c5ba6f8e16be3425ee_jpg.rf.e19d565e2329f3e5ea413e4d2f265131_with_roboflow_yolo.jpg
- 第 120 张图片: 6a77bcfa9d314ac7b980d37999435c90_jpg.rf.fb813de6121eac84abda165ff4be343f_with_roboflow_yolo.jpg
- 第 122 张图片: 6d7072cc1b8943ccaef703a6958dd14c_jpg.rf.cc00b9ec83ae9c30ad402e118e756719_with_roboflow_yolo.jpg
- 第 123 张图片: 710d075b5a37490ba0c183bb36850ea2_jpg.rf.a985b444354dff5e427e4299edd8c775_with_roboflow_yolo.jpg
- 第 124 张图片: 75f3e6f5e29e4398a13bedfa56d72517_jpg.rf.4966e8d76a12771aa7b7229817ed62eb_with_roboflow_yolo.jpg
- 第 125 张图片: 7692aa8f7a2a4b08acd7770359100d26_jpg.rf.624ef07cfdc6be22594f5b7c72dba598_with_roboflow_yolo.jpg
- 第 127 张图片: 775abad0e9d34f92a0dc6b0aefa48328_jpg.rf.d57983df9ca31fe5b1fe86d7c72b9e27_with_roboflow_yolo.jpg
- 第 129 张图片: 797672d371f64ea2be4d75a3a0f813b3_jpg.rf.dc7e5fdd74aa2a336c5209892f05b460_with_roboflow_yolo.jpg
- 第 131 张图片: 7be8d7e79ee94a7bbdf69b8be0846b31_jpg.rf.8dee7640193382d8bcf0ad98820037be_with_roboflow_yolo.jpg
- 第 132 张图片: 7cffd97650de4ba1970a06bb757a73f8_jpg.rf.2a2ecc481509ef552eaee34928bf8fd5_with_roboflow_yolo.jpg
- 第 133 张图片: 7d1c785ab2a0433baa81e4b948692b12_jpg.rf.b598cb7bb874cdcb72fa56110f9f1977_with_roboflow_yolo.jpg
- 第 135 张图片: 7e131a2a9c8a406c925fab971b032fdb_jpg.rf.e4fcfbfce33f098d7498322466a7a2a2_with_roboflow_yolo.jpg
- 第 136 张图片: 7fbf3b878aae4a7bb86dd414ee73cb3b_jpg.rf.b65e35d35475703efb108f0f62e8fc56_with_roboflow_yolo.jpg
- 第 138 张图片: 81e6464304574fd9a76309ef9706bf0b_jpg.rf.6434ee2379efcdd4a3e5840367da3ae7_with_roboflow_yolo.jpg
- 第 141 张图片: 84794cf5092e43fbb743fdeb1f4b7b6e_jpg.rf.c39e9da8c9afb63f8e181390c513b28e_with_roboflow_yolo.jpg
- 第 143 张图片: 87c24e3f661a43568c5437f662491b93_jpg.rf.84609c48a558e91579afd6562ebebd00_with_roboflow_yolo.jpg
- 第 146 张图片: 8872da4cef4b4013960b204365e2de03_jpg.rf.647faf9395bd0ef028f5d7bc89815205_with_roboflow_yolo.jpg
- 第 147 张图片: 889b4b49c7fd44179eb75e0e3c883a02_jpg.rf.e8fcef3b4bcfa67d3a626b28c9d30584_with_roboflow_yolo.jpg
- 第 149 张图片: 899c218eb25e40efa5fc68c3f4546a89_jpg.rf.6e233d9946e3eedb1e72ff06cc8d8634_with_roboflow_yolo.jpg
- 第 150 张图片: 89c4130264b64f2caeb5976d804253bd_jpg.rf.ba2a00940bd127d1c86169fd40ad625b_with_roboflow_yolo.jpg
- 第 151 张图片: 8a3b522a65324fca9a01a267b8cbe948_jpg.rf.259fdd00c08adfefae86677c97867adf_with_roboflow_yolo.jpg
- 第 153 张图片: 8c48731331044e87a9979576c7935623_jpg.rf.57de946e9d3429e47e6d4aedbef577a3_with_roboflow_yolo.jpg
- 第 154 张图片: 8c5a5283bd0c4ae4b5c8a8bce235ba4c_jpg.rf.691adec9ee542d6f15586cc0ade22fdf_with_roboflow_yolo.jpg
- 第 156 张图片: 8eabc9f3826d48ada1a9b990fcde6e02_jpg.rf.db84f23a06ff3bee599fc44806ed341e_with_roboflow_yolo.jpg
- 第 157 张图片: 91a3c7cf13a240cd8344d28f7e2ffae4_jpg.rf.80324edc4de28827d8088fbaf8e0c82b_with_roboflow_yolo.jpg
- 第 158 张图片: 91cc422a43f74c2286f8156111482398_jpg.rf.791df95a6efe6df2135b4fd917de81e9_with_roboflow_yolo.jpg
- 第 159 张图片: 92de5e2e50ec4c3cac8346816b81cb47_jpg.rf.5dad893c605ebab3c457068a005ff9b9_with_roboflow_yolo.jpg
- 第 160 张图片: 9382c58c96374309b8e2114f4240d3a5_jpg.rf.ca7c58fc5ca7c87fda1b57aeded163d0_with_roboflow_yolo.jpg
- 第 162 张图片: 952c480949b5418aa17214222956228a_jpg.rf.4440303836e33a085d883e7c1dcb8ce8_with_roboflow_yolo.jpg
- 第 164 张图片: 96a659efe50e44e3be318700019e0157_jpg.rf.1d31344d3fdb85b656d9ae25b84cd0d9_with_roboflow_yolo.jpg
- 第 165 张图片: 96b650417d3b41c29b221ae796cfdc11_jpg.rf.f6b9e0af02e57554136a731bb5ef8515_with_roboflow_yolo.jpg
- 第 166 张图片: 978e6cb90e514cda92781410095f1355_jpg.rf.466e0c1afeda7adf6871a4d877bcf14c_with_roboflow_yolo.jpg
- 第 168 张图片: 99b76d5195c4430fac0bba2098d59759_jpg.rf.8a0d087495d4bbc2e8590362ecaf438b_with_roboflow_yolo.jpg
- 第 169 张图片: 9ab81fdd9ba345a89cacf6994f3f64c3_jpg.rf.1a64a46babe36336c316e93fb5eabae0_with_roboflow_yolo.jpg
- 第 171 张图片: 9d18c7536abe48aabecc195dbf9f14a2_jpg.rf.b63bd36ac8c9ea5bd8c2c22a868a8b44_with_roboflow_yolo.jpg
- 第 172 张图片: 9d95e1e189a14fa88b51ef692b6e02b1_jpg.rf.31550c80c4df5e3bc125dc0954bd595f_with_roboflow_yolo.jpg
- 第 176 张图片: 9f77339eec17436397b9277156ff3856_jpg.rf.0a25e44c06efc02be6fb39b5ead4f395_with_roboflow_yolo.jpg
- 第 177 张图片: 9f8f3b7a668c4dd1a530f57d4821ca20_jpg.rf.1b2321d674b46339b4b0ef56d744a30d_with_roboflow_yolo.jpg
- 第 179 张图片: a36fc79a7dd442a6972a542925be9b10_jpg.rf.66623a9e070ff22f1c219d405f27e035_with_roboflow_yolo.jpg
- 第 181 张图片: a46b5f053cfd4b7da6bb54a4e14ade29_jpg.rf.d453b85c646156a05ae9ce3f68e18c8f_with_roboflow_yolo.jpg
- 第 182 张图片: a48efb0ff2394fc9a2ca70dd1620d97f_jpg.rf.61f77ef5f25fe20f49a2962ec1054180_with_roboflow_yolo.jpg
- 第 184 张图片: a6150cb1647a4649bf0b55fdfdfcbe0a_jpg.rf.0084fe0b2fa27ce891fbc4e6ace1c2fc_with_roboflow_yolo.jpg
- 第 185 张图片: a69b3bca7626461a8e6889f737b067b9_jpg.rf.c946c8516bda55291a28dfd761fc4aaf_with_roboflow_yolo.jpg
- 第 186 张图片: a78d7963c6284607958476f1c7c7cdf5_jpg.rf.c92a4edd6faf9160283f0cb31223599d_with_roboflow_yolo.jpg
- 第 191 张图片: ae175a3a16364661b55d0896746cd911_jpg.rf.278567c8c718638a0a2f3576e5b615ea_with_roboflow_yolo.jpg
- 第 192 张图片: b06025e1150d47588a60f0c84e4b1b25_jpg.rf.a83d9701ba57fcd9e9400847897a1978_with_roboflow_yolo.jpg
- 第 195 张图片: b4b4a6678adb467ba95c72e63fd6b98c_jpg.rf.ad742a6fa8d7c2602b0d242656bd004b_with_roboflow_yolo.jpg
- 第 196 张图片: b61ba0d3996e410a8eb79ced33371275_jpg.rf.f8bf461031d1fb0536512b179be6f96e_with_roboflow_yolo.jpg
- 第 198 张图片: b6db2af9e01d41228de313a1ec90d1f1_jpg.rf.10007ddefaedda11e5018aa2d0581f4e_with_roboflow_yolo.jpg
- 第 199 张图片: b732e08f1aa141de894c8f1fefdfea38_jpg.rf.31c9140363a2202b50584f8952d2a1ed_with_roboflow_yolo.jpg
- 第 201 张图片: b8f90b230ebc4459af32ac4c72928202_jpg.rf.e10f713545271781c0371fa05ead9631_with_roboflow_yolo.jpg
- 第 202 张图片: bd4c5b7bd6fa49089c50c4ca4ac61169_jpg.rf.ce8e79fda234714e0f0bca2aae577c0c_with_roboflow_yolo.jpg
- 第 204 张图片: bf2e692bb399419cb8591e6ab5f6b92c_jpg.rf.38202b0bdc094918146cd5b4f828034a_with_roboflow_yolo.jpg
- 第 205 张图片: bf5f99b54af2430bad8c25ac4d2383e6_jpg.rf.750b6904510a75cf59b0e5cffd095942_with_roboflow_yolo.jpg
- 第 207 张图片: c01637f4e812481f9d78a8676ec57053_jpg.rf.56c01fb4437773163f4ff876db5ffeab_with_roboflow_yolo.jpg
- 第 208 张图片: c1dbce9f498242529fb83f4bc14f3485_jpg.rf.8cf84810a1dce2bad2ba31e3ea3dc5b8_with_roboflow_yolo.jpg
- 第 210 张图片: c2a6b2436fc94c469f0ded118b7a0831_jpg.rf.91d229de48ea81d48abd9f95889c5b70_with_roboflow_yolo.jpg
- 第 212 张图片: c3fe34e423714b36b6b2a46f1f9a0107_jpg.rf.fe6b6d906e3e59c521ef791908e473c8_with_roboflow_yolo.jpg
- 第 214 张图片: c6e31135ad564e1797881de5d7e56e67_jpg.rf.959864a363a647fc8ac2b11110b559a0_with_roboflow_yolo.jpg
- 第 215 张图片: c87733c71a0948c3966721a42880cbd3_jpg.rf.b6928f57ceb3de96711400c088f91cb0_with_roboflow_yolo.jpg
- 第 216 张图片: c8fcf9d2900247f19491736f666e0e9d_jpg.rf.2e3afe5a454027fbe67c71c5b4eeaa1a_with_roboflow_yolo.jpg
- 第 217 张图片: cb0c739c98d5443cbff0d95a29cad191_jpg.rf.6fbf96b92911819df97e95026f15f487_with_roboflow_yolo.jpg
- 第 220 张图片: ccb8baccd20c4f57acf47c1f97f812d4_jpg.rf.5cbb71e700d6cbaf9caeb9780bbb54bf_with_roboflow_yolo.jpg
- 第 221 张图片: cd343b2780e84eef868ae60df27b4085_jpg.rf.8c179d88e056feda7557203d83921104_with_roboflow_yolo.jpg
- 第 223 张图片: cea2bf8d011f4559a64a641b306f3b10_jpg.rf.0522b159e8a88d743e3d5f70c3c8b8bb_with_roboflow_yolo.jpg
- 第 224 张图片: cead67d5f5eb4361b9d3f513ee6db779_jpg.rf.e176cb06d80d8e7a1f240d3a458cee00_with_roboflow_yolo.jpg
- 第 225 张图片: ceceac8cda3441ef9199b9fab3cce1e5_jpg.rf.f8975f9d9525e7852de8188c8ec83c59_with_roboflow_yolo.jpg
- 第 226 张图片: cf335ffb4ae144e785436f7f5f3579c7_jpg.rf.6f3303adb6f72295971e6beb92cccd21_with_roboflow_yolo.jpg
- 第 227 张图片: d17104884c9d458789519054309475ee_jpg.rf.519dd9b95ef07cf974dd62f6c95516dd_with_roboflow_yolo.jpg
- 第 230 张图片: d71ed599a15147ae83f95b8bf3edf804_jpg.rf.3b6c4b07a334f75b7de508d8e8f692d1_with_roboflow_yolo.jpg
- 第 231 张图片: d725ba45265f4dc4a963ade4c72c0e53_jpg.rf.1716499b8c0994044c46b79c1a0e54a5_with_roboflow_yolo.jpg
- 第 232 张图片: d8e7eed992f04f18a143c181a5c092ee_jpg.rf.1e70bc20eab2bcf868b37a919f865b4d_with_roboflow_yolo.jpg
- 第 234 张图片: db0f58569f8944ad8e1ccfe63f2545e2_jpg.rf.9dc22d4871121f383ffb62d1ad9cf878_with_roboflow_yolo.jpg
- 第 235 张图片: db17512718524dfdb39d9469216a5cb3_jpg.rf.f59c4ca52ce5553099815988c9ba3011_with_roboflow_yolo.jpg
- 第 237 张图片: db92b172d4b84317a4e95768a42b42bd_jpg.rf.36d8d60df297739e9fb1d53afe51094d_with_roboflow_yolo.jpg
- 第 243 张图片: e067b9281f554f29bb6c03f258fd50f2_jpg.rf.4f318b05fd97054ef035408deb0ff251_with_roboflow_yolo.jpg
- 第 246 张图片: e34a2bb76bd04eed9efa0cac8b127a07_jpg.rf.7f75c0b5ab86d5f3bc74fc088c8bf164_with_roboflow_yolo.jpg
- 第 248 张图片: e56e3767198442a99091b5d35a63993c_jpg.rf.3691dd1883a5e29cca1e9cb4d1b3dea9_with_roboflow_yolo.jpg
- 第 249 张图片: e615935c87e6472087f5f22fe3fcaa99_jpg.rf.b5891d7eb21f48bac4c18d0d33606cd2_with_roboflow_yolo.jpg
- 第 252 张图片: e6d6604e62554b73ace62af2f867ed36_jpg.rf.587579abab86b9d5fdf8b4d5114a0186_with_roboflow_yolo.jpg
- 第 254 张图片: e764a1879bdb423999e01036ef46e378_jpg.rf.7fee60ee68b713a68cf85923fd1c586a_with_roboflow_yolo.jpg
- 第 258 张图片: ed64418a571f496ca3671a1b18e17e38_jpg.rf.255c1a7ba308fb134bfc5933b79ec335_with_roboflow_yolo.jpg
- 第 260 张图片: eef1337993f24572b8165f18dc33c50f_jpg.rf.89e94ed8061722bf76dc610cc9f5b730_with_roboflow_yolo.jpg
- 第 262 张图片: ef5eda474da0438386f4ac64cd5443db_jpg.rf.693444322228070aa06d1574c2ea76fa_with_roboflow_yolo.jpg
- 第 264 张图片: f1df20275a0b4668b1fdb887930eac3d_jpg.rf.3e970929aa8a6d0f83eb2558144a8b2a_with_roboflow_yolo.jpg
- 第 267 张图片: f2d0cbb9707b4090bdb25d49d1b9db1b_jpg.rf.52563ec9793a024d9bda353b474edf93_with_roboflow_yolo.jpg
- 第 268 张图片: f402b869fb6d4b2a87eb5ed2a8619269_jpg.rf.f93b5c360c4bc4dfa126b539090543d6_with_roboflow_yolo.jpg
- 第 271 张图片: f7d7d9290fd54cc79eb51a00d9376038_jpg.rf.79dfb009246764d371ef34e9557516d1_with_roboflow_yolo.jpg
- 第 273 张图片: fb2b1d89d4914a4dbad52f7753e229d9_jpg.rf.25224eceb09b567523c88d8aac290b85_with_roboflow_yolo.jpg
- 第 276 张图片: fd804f075cab4118bd8202b093f469a2_jpg.rf.9a594142d9ea3da2b10ad4e4908bfbf0_with_roboflow_yolo.jpg

## 准确率：47.46%  （(276 - 145) / 276）

# 运行时间: 2025-07-30_11-39-30


==================================================
处理第 1 张图片: 00d48f4de24443e2850e83adc75b30f8_jpg.rf.2a2fdd270eb20c254897d6daac17e5df_with_roboflow_yolo.jpg

==================================================
![00d48f4de24443e2850e83adc75b30f8_jpg.rf.2a2fdd270eb20c254897d6daac17e5df_with_roboflow_yolo.jpg](00d48f4de24443e2850e83adc75b30f8_jpg.rf.2a2fdd270eb20c254897d6daac17e5df_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"14/8","题目 2":"1.1","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "14/8", "题目2": "1.1", "题目3": "4"}
```
### 响应时间：3.21秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 3 张图片: 032f1a9d2ba248c381a1d9609749defa_jpg.rf.71177523d1595e54a02b60ff2ebf6283_with_roboflow_yolo.jpg

==================================================
![032f1a9d2ba248c381a1d9609749defa_jpg.rf.71177523d1595e54a02b60ff2ebf6283_with_roboflow_yolo.jpg](032f1a9d2ba248c381a1d9609749defa_jpg.rf.71177523d1595e54a02b60ff2ebf6283_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"NAN","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：24.21秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 6 张图片: 048c57626b5f40db9b5070cbf35df814_jpg.rf.db870564d907d87314c67a83bc3da1dc_with_roboflow_yolo.jpg

==================================================
![048c57626b5f40db9b5070cbf35df814_jpg.rf.db870564d907d87314c67a83bc3da1dc_with_roboflow_yolo.jpg](048c57626b5f40db9b5070cbf35df814_jpg.rf.db870564d907d87314c67a83bc3da1dc_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"6/10","题目 4":"39/54","题目 5":"9/12","题目 6":"23/10"}
```
### 响应内容：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "39/54", "题目 5": "1", "题目 6": "23/6"}
```
### 响应时间：3.06秒
### token用量
- total_tokens: 766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 7 张图片: 05b29381a30043f1b7cea3088b2540d8_jpg.rf.75f279aa470ec142e0a27c96cfe63778_with_roboflow_yolo.jpg

==================================================
![05b29381a30043f1b7cea3088b2540d8_jpg.rf.75f279aa470ec142e0a27c96cfe63778_with_roboflow_yolo.jpg](05b29381a30043f1b7cea3088b2540d8_jpg.rf.75f279aa470ec142e0a27c96cfe63778_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"7/12","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：9.44秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 9 张图片: 05dc746beae84e05a416956868cd8b7b_jpg.rf.c57c9fa4ce36a67bc163b8d0c802bd9f_with_roboflow_yolo.jpg

==================================================
![05dc746beae84e05a416956868cd8b7b_jpg.rf.c57c9fa4ce36a67bc163b8d0c802bd9f_with_roboflow_yolo.jpg](05dc746beae84e05a416956868cd8b7b_jpg.rf.c57c9fa4ce36a67bc163b8d0c802bd9f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.58秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 11 张图片: 08c8d03749cb400a8e58acc57bb335e7_jpg.rf.49adf22404961bbf511a59493ac44f42_with_roboflow_yolo.jpg

==================================================
![08c8d03749cb400a8e58acc57bb335e7_jpg.rf.49adf22404961bbf511a59493ac44f42_with_roboflow_yolo.jpg](08c8d03749cb400a8e58acc57bb335e7_jpg.rf.49adf22404961bbf511a59493ac44f42_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"7","题目 4":"11 4/2"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN"}
```
### 响应时间：24.85秒
### token用量
- total_tokens: 742
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 12 张图片: 0923a83aece0436fa26e2475e610d979_jpg.rf.b220d281c3ee11eee6334f15b3fff5be_with_roboflow_yolo.jpg

==================================================
![0923a83aece0436fa26e2475e610d979_jpg.rf.b220d281c3ee11eee6334f15b3fff5be_with_roboflow_yolo.jpg](0923a83aece0436fa26e2475e610d979_jpg.rf.b220d281c3ee11eee6334f15b3fff5be_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"3/5","题目 4":"13/18","题目 5":"9/12","题目 6":"1"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "17/20", "题目4": "13/18", "题目5": "3/4", "题目6": "1"}
```
### 响应时间：2.68秒
### token用量
- total_tokens: 760
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 13 张图片: 098d0d2a16994bafa8d537fa455164e9_jpg.rf.95d096079ca09bf76693a13a6220a83a_with_roboflow_yolo.jpg

==================================================
![098d0d2a16994bafa8d537fa455164e9_jpg.rf.95d096079ca09bf76693a13a6220a83a_with_roboflow_yolo.jpg](098d0d2a16994bafa8d537fa455164e9_jpg.rf.95d096079ca09bf76693a13a6220a83a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"1/3","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 14 张图片: 0af998c399404b1f9866d527becaab46_jpg.rf.df3bff649bdacfd253142bef6d15eec5_with_roboflow_yolo.jpg

==================================================
![0af998c399404b1f9866d527becaab46_jpg.rf.df3bff649bdacfd253142bef6d15eec5_with_roboflow_yolo.jpg](0af998c399404b1f9866d527becaab46_jpg.rf.df3bff649bdacfd253142bef6d15eec5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"11/10-1","题目 4":"13/18","题目 5":"9/12","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：3.75秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 15 张图片: 0b7a3f01188944448ead2243005895c6_jpg.rf.6344fdcf6b2bd2999655e23b326c4ba8_with_roboflow_yolo.jpg

==================================================
![0b7a3f01188944448ead2243005895c6_jpg.rf.6344fdcf6b2bd2999655e23b326c4ba8_with_roboflow_yolo.jpg](0b7a3f01188944448ead2243005895c6_jpg.rf.6344fdcf6b2bd2999655e23b326c4ba8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"2 1/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：2.26秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 20 张图片: 10135553394148149ced8e0d61d051fd_jpg.rf.b99bb480549b95e2bc8a57940654bba5_with_roboflow_yolo.jpg

==================================================
![10135553394148149ced8e0d61d051fd_jpg.rf.b99bb480549b95e2bc8a57940654bba5_with_roboflow_yolo.jpg](10135553394148149ced8e0d61d051fd_jpg.rf.b99bb480549b95e2bc8a57940654bba5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"4/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"2 1/8"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "4/3", "题目3": "1/10", "题目4": "1/18", "题目5": "3/4", "题目6": "2 1/8"}
```
### 响应时间：2.73秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 23 张图片: 164d5b9000354b38b9a01929990eb1c7_jpg.rf.35990c1cda53d495563b75c5f5ef9b50_with_roboflow_yolo.jpg

==================================================
![164d5b9000354b38b9a01929990eb1c7_jpg.rf.35990c1cda53d495563b75c5f5ef9b50_with_roboflow_yolo.jpg](164d5b9000354b38b9a01929990eb1c7_jpg.rf.35990c1cda53d495563b75c5f5ef9b50_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 24 张图片: 1668c5c1ad044acdab05380f58188af3_jpg.rf.d1e313374cc8437d92d6331fae1c8488_with_roboflow_yolo.jpg

==================================================
![1668c5c1ad044acdab05380f58188af3_jpg.rf.d1e313374cc8437d92d6331fae1c8488_with_roboflow_yolo.jpg](1668c5c1ad044acdab05380f58188af3_jpg.rf.d1e313374cc8437d92d6331fae1c8488_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"0.1","题目 4":"54","题目 5":"3/4","题目 6":"1 23/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "5/54", "题目5": "3/4", "题目6": "1 3/10"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 26 张图片: 1936bd1627be45718d42ea2cb7362039_jpg.rf.3b9fde623503b71e2eac8009913ed169_with_roboflow_yolo.jpg

==================================================
![1936bd1627be45718d42ea2cb7362039_jpg.rf.3b9fde623503b71e2eac8009913ed169_with_roboflow_yolo.jpg](1936bd1627be45718d42ea2cb7362039_jpg.rf.3b9fde623503b71e2eac8009913ed169_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目 1": "3\\frac{7}{9}", "题目 2": "\\frac{1}{3}", "题目 3": "7", "题目 4": "13"}
```
### 响应时间：3.45秒
### token用量
- total_tokens: 749
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 27 张图片: 1a4c41280e304f3b878868c321953be4_jpg.rf.b953d1838bc2bb357c7c3a8fd1a194f6_with_roboflow_yolo.jpg

==================================================
![1a4c41280e304f3b878868c321953be4_jpg.rf.b953d1838bc2bb357c7c3a8fd1a194f6_with_roboflow_yolo.jpg](1a4c41280e304f3b878868c321953be4_jpg.rf.b953d1838bc2bb357c7c3a8fd1a194f6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 3/7","题目 2":"1 3/9","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "3 1/7", "题目2": "1 3/4", "题目3": "7", "题目4": "13"}
```
### 响应时间：2.20秒
### token用量
- total_tokens: 743
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 28 张图片: 1ab719e3e6cd4bf1b6611ccb8eb5286f_jpg.rf.6c010e86247a2247149760f8c9e015f1_with_roboflow_yolo.jpg

==================================================
![1ab719e3e6cd4bf1b6611ccb8eb5286f_jpg.rf.6c010e86247a2247149760f8c9e015f1_with_roboflow_yolo.jpg](1ab719e3e6cd4bf1b6611ccb8eb5286f_jpg.rf.6c010e86247a2247149760f8c9e015f1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目 1": "3 又 5 / 7", "题目 2": "1 又 1 / 6", "题目 3": "7", "题目 4": "13"}
```
### 响应时间：17.17秒
### token用量
- total_tokens: 753
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 29 张图片: 1caa64a8beed4b119dddd6c90e46b5e3_jpg.rf.08c810a2cc236b4bb8daeba7440f83ea_with_roboflow_yolo.jpg

==================================================
![1caa64a8beed4b119dddd6c90e46b5e3_jpg.rf.08c810a2cc236b4bb8daeba7440f83ea_with_roboflow_yolo.jpg](1caa64a8beed4b119dddd6c90e46b5e3_jpg.rf.08c810a2cc236b4bb8daeba7440f83ea_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"2700","题目 4":"2346","题目 5":"24","题目 6":"112"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "11.2"}
```
### 响应时间：3.50秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 30 张图片: 1d68e071aa5f4b38b128f4ead6723b15_jpg.rf.bd10825aca1fc367ade1fc31a8be25f0_with_roboflow_yolo.jpg

==================================================
![1d68e071aa5f4b38b128f4ead6723b15_jpg.rf.bd10825aca1fc367ade1fc31a8be25f0_with_roboflow_yolo.jpg](1d68e071aa5f4b38b128f4ead6723b15_jpg.rf.bd10825aca1fc367ade1fc31a8be25f0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"21.10","题目 2":"41.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "11.44"}
```
### 响应时间：2.09秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 31 张图片: 1dca2db5e9124fe3b95c87fde75d0fd0_jpg.rf.99d8f16d26d191f0814a2a3a823f4f86_with_roboflow_yolo.jpg

==================================================
![1dca2db5e9124fe3b95c87fde75d0fd0_jpg.rf.99d8f16d26d191f0814a2a3a823f4f86_with_roboflow_yolo.jpg](1dca2db5e9124fe3b95c87fde75d0fd0_jpg.rf.99d8f16d26d191f0814a2a3a823f4f86_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.37秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 32 张图片: 1e51e5fb9ff34b74adec856e815cb0bd_jpg.rf.6e1d87e27a2f7c9f63b29e45e159f12d_with_roboflow_yolo.jpg

==================================================
![1e51e5fb9ff34b74adec856e815cb0bd_jpg.rf.6e1d87e27a2f7c9f63b29e45e159f12d_with_roboflow_yolo.jpg](1e51e5fb9ff34b74adec856e815cb0bd_jpg.rf.6e1d87e27a2f7c9f63b29e45e159f12d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"NAN","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：9.14秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 34 张图片: 1f0d28f5c3cc490697bf73cf4b337692_jpg.rf.96dc268694430dd8f7df46ff5417d1f9_with_roboflow_yolo.jpg

==================================================
![1f0d28f5c3cc490697bf73cf4b337692_jpg.rf.96dc268694430dd8f7df46ff5417d1f9_with_roboflow_yolo.jpg](1f0d28f5c3cc490697bf73cf4b337692_jpg.rf.96dc268694430dd8f7df46ff5417d1f9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1 3/4","题目 2":"2 1/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "11/5", "题目3": "4"}
```
### 响应时间：3.12秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 35 张图片: 221cf6fe34fc43ac8bc998e1d5a6d432_jpg.rf.dfbea28412d0ec71655137376303005a_with_roboflow_yolo.jpg

==================================================
![221cf6fe34fc43ac8bc998e1d5a6d432_jpg.rf.dfbea28412d0ec71655137376303005a_with_roboflow_yolo.jpg](221cf6fe34fc43ac8bc998e1d5a6d432_jpg.rf.dfbea28412d0ec71655137376303005a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1/8","题目 2":"NAN","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "1/8", "题目2": "5/6", "题目3": "1/2"}
```
### 响应时间：2.93秒
### token用量
- total_tokens: 733
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 36 张图片: 2371fb916eb34b3fbc81a595974ce825_jpg.rf.e2a91a5b58eab5322cf16efc668559e1_with_roboflow_yolo.jpg

==================================================
![2371fb916eb34b3fbc81a595974ce825_jpg.rf.e2a91a5b58eab5322cf16efc668559e1_with_roboflow_yolo.jpg](2371fb916eb34b3fbc81a595974ce825_jpg.rf.e2a91a5b58eab5322cf16efc668559e1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"2700","题目 4":"9800","题目 5":"2400","题目 6":"8"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：17.57秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 40 张图片: 287f05d42b8c49de91fa53bffa3e3874_jpg.rf.b6f9597573d2401245741b63b369f686_with_roboflow_yolo.jpg

==================================================
![287f05d42b8c49de91fa53bffa3e3874_jpg.rf.b6f9597573d2401245741b63b369f686_with_roboflow_yolo.jpg](287f05d42b8c49de91fa53bffa3e3874_jpg.rf.b6f9597573d2401245741b63b369f686_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"7/4","题目 2":"2 1/5","题目 3":"4 1/2"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "2+1/5", "题目3": "4+1/2"}
```
### 响应时间：16.78秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 42 张图片: 29651d0ee3ae4461958ae1daa527d85d_jpg.rf.8402349badd9ab45aebd8f9d7a35a532_with_roboflow_yolo.jpg

==================================================
![29651d0ee3ae4461958ae1daa527d85d_jpg.rf.8402349badd9ab45aebd8f9d7a35a532_with_roboflow_yolo.jpg](29651d0ee3ae4461958ae1daa527d85d_jpg.rf.8402349badd9ab45aebd8f9d7a35a532_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"2700","题目 4":"2346","题目 5":"NAN","题目 6":"8"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：3.65秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 45 张图片: 2ca5b5db868d4c95a192de2cf08da8d9_jpg.rf.4165d98c4a65cdb45ce2642811c4ef2d_with_roboflow_yolo.jpg

==================================================
![2ca5b5db868d4c95a192de2cf08da8d9_jpg.rf.4165d98c4a65cdb45ce2642811c4ef2d_with_roboflow_yolo.jpg](2ca5b5db868d4c95a192de2cf08da8d9_jpg.rf.4165d98c4a65cdb45ce2642811c4ef2d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"18/40","题目 2":"1/5","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.71秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 46 张图片: 2e256f606ebe4cad9873aa4cc0109086_jpg.rf.f8278953757aefc1d2fc186e65f96fd5_with_roboflow_yolo.jpg

==================================================
![2e256f606ebe4cad9873aa4cc0109086_jpg.rf.f8278953757aefc1d2fc186e65f96fd5_with_roboflow_yolo.jpg](2e256f606ebe4cad9873aa4cc0109086_jpg.rf.f8278953757aefc1d2fc186e65f96fd5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.74秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 50 张图片: 30f41a55e8cb41dea41d52c0709483f9_jpg.rf.d975e7cbe1d2d867b398dc535b678e0a_with_roboflow_yolo.jpg

==================================================
![30f41a55e8cb41dea41d52c0709483f9_jpg.rf.d975e7cbe1d2d867b398dc535b678e0a_with_roboflow_yolo.jpg](30f41a55e8cb41dea41d52c0709483f9_jpg.rf.d975e7cbe1d2d867b398dc535b678e0a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "13/24", "题目2": "1/6", "题目3": "1/2"}
```
### 响应时间：2.54秒
### token用量
- total_tokens: 734
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 51 张图片: 31c9e668f03e4fdd949d20ec98475483_jpg.rf.ae0392f4bb232eca9e81b63360e5d41f_with_roboflow_yolo.jpg

==================================================
![31c9e668f03e4fdd949d20ec98475483_jpg.rf.ae0392f4bb232eca9e81b63360e5d41f_with_roboflow_yolo.jpg](31c9e668f03e4fdd949d20ec98475483_jpg.rf.ae0392f4bb232eca9e81b63360e5d41f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "11/5", "题目3": "4"}
```
### 响应时间：24.67秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 53 张图片: 324d933e808a43e19c64b034ad59dd37_jpg.rf.0522fbc093da731131ad88e1ec0a4a64_with_roboflow_yolo.jpg

==================================================
![324d933e808a43e19c64b034ad59dd37_jpg.rf.0522fbc093da731131ad88e1ec0a4a64_with_roboflow_yolo.jpg](324d933e808a43e19c64b034ad59dd37_jpg.rf.0522fbc093da731131ad88e1ec0a4a64_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"14/8","题目 2":"2 1/5","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "11/5"}
```
### 响应时间：2.80秒
### token用量
- total_tokens: 725
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 57 张图片: 36f80e6edae440649a199bc69c745d32_jpg.rf.e4af104643ec6691a492bb3c39acd66e_with_roboflow_yolo.jpg

==================================================
![36f80e6edae440649a199bc69c745d32_jpg.rf.e4af104643ec6691a492bb3c39acd66e_with_roboflow_yolo.jpg](36f80e6edae440649a199bc69c745d32_jpg.rf.e4af104643ec6691a492bb3c39acd66e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"2700","题目 4":"2346","题目 5":"14","题目 6":"8"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 59 张图片: 39236aeaf1b1444f8b3b33aa72a4cdbd_jpg.rf.f6a841779cd96ecac82cf64a224e8cdb_with_roboflow_yolo.jpg

==================================================
![39236aeaf1b1444f8b3b33aa72a4cdbd_jpg.rf.f6a841779cd96ecac82cf64a224e8cdb_with_roboflow_yolo.jpg](39236aeaf1b1444f8b3b33aa72a4cdbd_jpg.rf.f6a841779cd96ecac82cf64a224e8cdb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"2/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "2/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "-3/10"}
```
### 响应时间：2.41秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 60 张图片: 3968ef8d68704c769c20a6d97fe93927_jpg.rf.40810119b9aedf77edf1dbfad54ab075_with_roboflow_yolo.jpg

==================================================
![3968ef8d68704c769c20a6d97fe93927_jpg.rf.40810119b9aedf77edf1dbfad54ab075_with_roboflow_yolo.jpg](3968ef8d68704c769c20a6d97fe93927_jpg.rf.40810119b9aedf77edf1dbfad54ab075_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"0","题目 2":"2.2","题目 3":"6"}
```
### 响应内容：
```json
{"题目1": "0", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：17.30秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 64 张图片: 3c6cc211d8d54727be1c02eeb7ce5b0a_jpg.rf.55a421676f6caaba62ec37473cef7cbb_with_roboflow_yolo.jpg

==================================================
![3c6cc211d8d54727be1c02eeb7ce5b0a_jpg.rf.55a421676f6caaba62ec37473cef7cbb_with_roboflow_yolo.jpg](3c6cc211d8d54727be1c02eeb7ce5b0a_jpg.rf.55a421676f6caaba62ec37473cef7cbb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"11","题目 4":"2346","题目 5":"24","题目 6":"8"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：8.96秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 67 张图片: 3ee53f7b2b8a46f7b1915aacdcf262b5_jpg.rf.7772dce58b54bd20e09558eff3883dc0_with_roboflow_yolo.jpg

==================================================
![3ee53f7b2b8a46f7b1915aacdcf262b5_jpg.rf.7772dce58b54bd20e09558eff3883dc0_with_roboflow_yolo.jpg](3ee53f7b2b8a46f7b1915aacdcf262b5_jpg.rf.7772dce58b54bd20e09558eff3883dc0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "0.1", "题目 4": "5/18", "题目 5": "1/4", "题目 6": "3/10"}
```
### 响应时间：10.29秒
### token用量
- total_tokens: 766
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 70 张图片: 422412df03164db2abfc4eac955c45c9_jpg.rf.cd6fc48ff9b454281d95da21737ce4d1_with_roboflow_yolo.jpg

==================================================
![422412df03164db2abfc4eac955c45c9_jpg.rf.cd6fc48ff9b454281d95da21737ce4d1_with_roboflow_yolo.jpg](422412df03164db2abfc4eac955c45c9_jpg.rf.cd6fc48ff9b454281d95da21737ce4d1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"NAN","题目 5":"1 1/12","题目 6":"NAN"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/10", "题目4": "7/18", "题目5": "13/12", "题目6": "23/10"}
```
### 响应时间：3.77秒
### token用量
- total_tokens: 764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 71 张图片: 42321da7346f42a3959ef8ece20ae2b9_jpg.rf.a376ca54fc2b46f6d6a5a4740a7e98fa_with_roboflow_yolo.jpg

==================================================
![42321da7346f42a3959ef8ece20ae2b9_jpg.rf.a376ca54fc2b46f6d6a5a4740a7e98fa_with_roboflow_yolo.jpg](42321da7346f42a3959ef8ece20ae2b9_jpg.rf.a376ca54fc2b46f6d6a5a4740a7e98fa_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.10秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 75 张图片: 44c621f7e0bd40a19dfff8be9e3c1a7b_jpg.rf.16986bf66103dbcc7eace35b65bd21e4_with_roboflow_yolo.jpg

==================================================
![44c621f7e0bd40a19dfff8be9e3c1a7b_jpg.rf.16986bf66103dbcc7eace35b65bd21e4_with_roboflow_yolo.jpg](44c621f7e0bd40a19dfff8be9e3c1a7b_jpg.rf.16986bf66103dbcc7eace35b65bd21e4_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"1/6"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：8.95秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 80 张图片: 488a82293a954fdda31117669fa156aa_jpg.rf.a49e1920999604f72c7bf3b581eb2293_with_roboflow_yolo.jpg

==================================================
![488a82293a954fdda31117669fa156aa_jpg.rf.a49e1920999604f72c7bf3b581eb2293_with_roboflow_yolo.jpg](488a82293a954fdda31117669fa156aa_jpg.rf.a49e1920999604f72c7bf3b581eb2293_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 81 张图片: 4a34106f23714598882f8bf3f00e40d9_jpg.rf.474f385dad6df3d53a2a46f84b52bd30_with_roboflow_yolo.jpg

==================================================
![4a34106f23714598882f8bf3f00e40d9_jpg.rf.474f385dad6df3d53a2a46f84b52bd30_with_roboflow_yolo.jpg](4a34106f23714598882f8bf3f00e40d9_jpg.rf.474f385dad6df3d53a2a46f84b52bd30_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/28","题目 2":"1 1/6","题目 3":"5/10"}
```
### 响应内容：
```json
{"题目1": "13/24", "题目2": "17/6", "题目3": "3/10"}
```
### 响应时间：2.33秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 82 张图片: 4a41447e5e3e479ba7fcec54036e04ec_jpg.rf.f313993d1446d6217f960d6506b894dd_with_roboflow_yolo.jpg

==================================================
![4a41447e5e3e479ba7fcec54036e04ec_jpg.rf.f313993d1446d6217f960d6506b894dd_with_roboflow_yolo.jpg](4a41447e5e3e479ba7fcec54036e04ec_jpg.rf.f313993d1446d6217f960d6506b894dd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"21.1","题目 2":"41.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.00", "题目2": "40.52", "题目3": "27.44"}
```
### 响应时间：2.34秒
### token用量
- total_tokens: 739
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 85 张图片: 4bc75919268e4f91821a8ce1f7736545_jpg.rf.1b45e387b74ea451d506c160eaa8a9cb_with_roboflow_yolo.jpg

==================================================
![4bc75919268e4f91821a8ce1f7736545_jpg.rf.1b45e387b74ea451d506c160eaa8a9cb_with_roboflow_yolo.jpg](4bc75919268e4f91821a8ce1f7736545_jpg.rf.1b45e387b74ea451d506c160eaa8a9cb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"21.1","题目 2":"40.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```
### 响应时间：2.28秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 91 张图片: 50a0335b41f9419ab2b350af6775fe02_jpg.rf.d50d97f01d992130be699038832b040d_with_roboflow_yolo.jpg

==================================================
![50a0335b41f9419ab2b350af6775fe02_jpg.rf.d50d97f01d992130be699038832b040d_with_roboflow_yolo.jpg](50a0335b41f9419ab2b350af6775fe02_jpg.rf.d50d97f01d992130be699038832b040d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"2700","题目 4":"2346","题目 5":"24","题目 6":"8"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "40.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：2.06秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 93 张图片: 5459d36a8bb241d4a8b94c5af5bdbc02_jpg.rf.9998d58134d9f79a7c637897b75051ed_with_roboflow_yolo.jpg

==================================================
![5459d36a8bb241d4a8b94c5af5bdbc02_jpg.rf.9998d58134d9f79a7c637897b75051ed_with_roboflow_yolo.jpg](5459d36a8bb241d4a8b94c5af5bdbc02_jpg.rf.9998d58134d9f79a7c637897b75051ed_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"14/8","题目 2":"2 1/5","题目 3":"6"}
```
### 响应内容：
```json
{"题目1": "14/8", "题目2": "2.5", "题目3": "6"}
```
### 响应时间：1.70秒
### token用量
- total_tokens: 731
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 97 张图片: 582ba96857834be2a810437b2b2720a2_jpg.rf.ede4dd3296b4babc4eed3cbdd6d2a94e_with_roboflow_yolo.jpg

==================================================
![582ba96857834be2a810437b2b2720a2_jpg.rf.ede4dd3296b4babc4eed3cbdd6d2a94e_with_roboflow_yolo.jpg](582ba96857834be2a810437b2b2720a2_jpg.rf.ede4dd3296b4babc4eed3cbdd6d2a94e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"3/9+1","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：9.73秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 98 张图片: 5861d45c230f4d418aa2c422bfbfa2a5_jpg.rf.87b1c1353d01b4fb0692198c8a9f7d58_with_roboflow_yolo.jpg

==================================================
![5861d45c230f4d418aa2c422bfbfa2a5_jpg.rf.87b1c1353d01b4fb0692198c8a9f7d58_with_roboflow_yolo.jpg](5861d45c230f4d418aa2c422bfbfa2a5_jpg.rf.87b1c1353d01b4fb0692198c8a9f7d58_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN","题目 4":"NAN","题目 5":"NAN","题目 6":"NAN"}
```
### 响应内容：
```json
{
    "题目1": "2",
    "题目2": "2 / 3",
    "题目3": "0.1",
    "题目4": "11 / 18",
    "题目5": "11 / 12",
    "题目6": "NAN"
}
```
### 响应时间：2.52秒
### token用量
- total_tokens: 778
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 105 张图片: 5b47fc0811e343269b0a072aa3715659_jpg.rf.7146b894853abfab981c7490033c66ee_with_roboflow_yolo.jpg

==================================================
![5b47fc0811e343269b0a072aa3715659_jpg.rf.7146b894853abfab981c7490033c66ee_with_roboflow_yolo.jpg](5b47fc0811e343269b0a072aa3715659_jpg.rf.7146b894853abfab981c7490033c66ee_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"2700","题目 4":"2346","题目 5":"24","题目 6":"80"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 106 张图片: 5ca6a811afac4ca7991eda31a4c5f38b_jpg.rf.6e97a1e71ee4a55d61c545310fb6caa2_with_roboflow_yolo.jpg

==================================================
![5ca6a811afac4ca7991eda31a4c5f38b_jpg.rf.6e97a1e71ee4a55d61c545310fb6caa2_with_roboflow_yolo.jpg](5ca6a811afac4ca7991eda31a4c5f38b_jpg.rf.6e97a1e71ee4a55d61c545310fb6caa2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"NAN"}
```
### 响应内容：
```json
{"题目 1": "2", "题目 2": "1/2", "题目 3": "0.1", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```
### 响应时间：2.57秒
### token用量
- total_tokens: 767
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 107 张图片: 5d5fb8da253e4b5b8684dfc77506b0ba_jpg.rf.1822bf7b75899a2520df710208f2d979_with_roboflow_yolo.jpg

==================================================
![5d5fb8da253e4b5b8684dfc77506b0ba_jpg.rf.1822bf7b75899a2520df710208f2d979_with_roboflow_yolo.jpg](5d5fb8da253e4b5b8684dfc77506b0ba_jpg.rf.1822bf7b75899a2520df710208f2d979_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：2.62秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 108 张图片: 5e679b36df0948f798882f40adfe2c74_jpg.rf.dbf959e10c1a9506e22e1fad3d7390f6_with_roboflow_yolo.jpg

==================================================
![5e679b36df0948f798882f40adfe2c74_jpg.rf.dbf959e10c1a9506e22e1fad3d7390f6_with_roboflow_yolo.jpg](5e679b36df0948f798882f40adfe2c74_jpg.rf.dbf959e10c1a9506e22e1fad3d7390f6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"21.10","题目 2":"41.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```
### 响应时间：1.97秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 109 张图片: 60e69e1f36aa4a78994eef7bf27ab460_jpg.rf.486d48168d3e7aae83d43b20a8f680bd_with_roboflow_yolo.jpg

==================================================
![60e69e1f36aa4a78994eef7bf27ab460_jpg.rf.486d48168d3e7aae83d43b20a8f680bd_with_roboflow_yolo.jpg](60e69e1f36aa4a78994eef7bf27ab460_jpg.rf.486d48168d3e7aae83d43b20a8f680bd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"21.10","题目 2":"41.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```
### 响应时间：2.85秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 113 张图片: 6642d9ce1a43428098a30b44c44f6d10_jpg.rf.4afe07df588b0d1e0eb82031537f964a_with_roboflow_yolo.jpg

==================================================
![6642d9ce1a43428098a30b44c44f6d10_jpg.rf.4afe07df588b0d1e0eb82031537f964a_with_roboflow_yolo.jpg](6642d9ce1a43428098a30b44c44f6d10_jpg.rf.4afe07df588b0d1e0eb82031537f964a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"7/4","题目 2":"2.2","题目 3":"6"}
```
### 响应内容：
```json
{"题目1": "0.25", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：3.32秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 114 张图片: 667358c69315426995762020f45706e6_jpg.rf.2016e70075eb1b044eec31c4a3e8ce3f_with_roboflow_yolo.jpg

==================================================
![667358c69315426995762020f45706e6_jpg.rf.2016e70075eb1b044eec31c4a3e8ce3f_with_roboflow_yolo.jpg](667358c69315426995762020f45706e6_jpg.rf.2016e70075eb1b044eec31c4a3e8ce3f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"2/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：3.10秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 115 张图片: 674a99bbad7f4f91b990c072b3d1e774_jpg.rf.6d28a2ef35eb02fc16e40d25515a0944_with_roboflow_yolo.jpg

==================================================
![674a99bbad7f4f91b990c072b3d1e774_jpg.rf.6d28a2ef35eb02fc16e40d25515a0944_with_roboflow_yolo.jpg](674a99bbad7f4f91b990c072b3d1e774_jpg.rf.6d28a2ef35eb02fc16e40d25515a0944_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"20/28","题目 2":"3/36","题目 3":"7","题目 4":"11 2/1"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.17秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 116 张图片: 6837fbb7cad94e508e0866d4229af32e_jpg.rf.283c1fabfe10fa38eebfb414afbdb805_with_roboflow_yolo.jpg

==================================================
![6837fbb7cad94e508e0866d4229af32e_jpg.rf.283c1fabfe10fa38eebfb414afbdb805_with_roboflow_yolo.jpg](6837fbb7cad94e508e0866d4229af32e_jpg.rf.283c1fabfe10fa38eebfb414afbdb805_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19","题目 2":"30.1","题目 3":"2700","题目 4":"2346","题目 5":"24","题目 6":"8"}
```
### 响应内容：
```json
{"题目1": "14", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：2.89秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 117 张图片: 684bd0aaa85e46c5ba6f8e16be3425ee_jpg.rf.e19d565e2329f3e5ea413e4d2f265131_with_roboflow_yolo.jpg

==================================================
![684bd0aaa85e46c5ba6f8e16be3425ee_jpg.rf.e19d565e2329f3e5ea413e4d2f265131_with_roboflow_yolo.jpg](684bd0aaa85e46c5ba6f8e16be3425ee_jpg.rf.e19d565e2329f3e5ea413e4d2f265131_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 120 张图片: 6a77bcfa9d314ac7b980d37999435c90_jpg.rf.fb813de6121eac84abda165ff4be343f_with_roboflow_yolo.jpg

==================================================
![6a77bcfa9d314ac7b980d37999435c90_jpg.rf.fb813de6121eac84abda165ff4be343f_with_roboflow_yolo.jpg](6a77bcfa9d314ac7b980d37999435c90_jpg.rf.fb813de6121eac84abda165ff4be343f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"12"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：9.40秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 122 张图片: 6d7072cc1b8943ccaef703a6958dd14c_jpg.rf.cc00b9ec83ae9c30ad402e118e756719_with_roboflow_yolo.jpg

==================================================
![6d7072cc1b8943ccaef703a6958dd14c_jpg.rf.cc00b9ec83ae9c30ad402e118e756719_with_roboflow_yolo.jpg](6d7072cc1b8943ccaef703a6958dd14c_jpg.rf.cc00b9ec83ae9c30ad402e118e756719_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"29/24","题目 2":"5/6","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：1.77秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 123 张图片: 710d075b5a37490ba0c183bb36850ea2_jpg.rf.a985b444354dff5e427e4299edd8c775_with_roboflow_yolo.jpg

==================================================
![710d075b5a37490ba0c183bb36850ea2_jpg.rf.a985b444354dff5e427e4299edd8c775_with_roboflow_yolo.jpg](710d075b5a37490ba0c183bb36850ea2_jpg.rf.a985b444354dff5e427e4299edd8c775_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.16秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 124 张图片: 75f3e6f5e29e4398a13bedfa56d72517_jpg.rf.4966e8d76a12771aa7b7229817ed62eb_with_roboflow_yolo.jpg

==================================================
![75f3e6f5e29e4398a13bedfa56d72517_jpg.rf.4966e8d76a12771aa7b7229817ed62eb_with_roboflow_yolo.jpg](75f3e6f5e29e4398a13bedfa56d72517_jpg.rf.4966e8d76a12771aa7b7229817ed62eb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1 2/8","题目 2":"NAN","题目 3":"1/3"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.22秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 125 张图片: 7692aa8f7a2a4b08acd7770359100d26_jpg.rf.624ef07cfdc6be22594f5b7c72dba598_with_roboflow_yolo.jpg

==================================================
![7692aa8f7a2a4b08acd7770359100d26_jpg.rf.624ef07cfdc6be22594f5b7c72dba598_with_roboflow_yolo.jpg](7692aa8f7a2a4b08acd7770359100d26_jpg.rf.624ef07cfdc6be22594f5b7c72dba598_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"9/12","题目 6":"1 13/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "23/10"}
```
### 响应时间：3.38秒
### token用量
- total_tokens: 763
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 127 张图片: 775abad0e9d34f92a0dc6b0aefa48328_jpg.rf.d57983df9ca31fe5b1fe86d7c72b9e27_with_roboflow_yolo.jpg

==================================================
![775abad0e9d34f92a0dc6b0aefa48328_jpg.rf.d57983df9ca31fe5b1fe86d7c72b9e27_with_roboflow_yolo.jpg](775abad0e9d34f92a0dc6b0aefa48328_jpg.rf.d57983df9ca31fe5b1fe86d7c72b9e27_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1 3/4","题目 2":"2 1/5","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "1.75", "题目2": "2.2", "题目3": "NAN"}
```
### 响应时间：16.75秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 129 张图片: 797672d371f64ea2be4d75a3a0f813b3_jpg.rf.dc7e5fdd74aa2a336c5209892f05b460_with_roboflow_yolo.jpg

==================================================
![797672d371f64ea2be4d75a3a0f813b3_jpg.rf.dc7e5fdd74aa2a336c5209892f05b460_with_roboflow_yolo.jpg](797672d371f64ea2be4d75a3a0f813b3_jpg.rf.dc7e5fdd74aa2a336c5209892f05b460_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"2.2","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 131 张图片: 7be8d7e79ee94a7bbdf69b8be0846b31_jpg.rf.8dee7640193382d8bcf0ad98820037be_with_roboflow_yolo.jpg

==================================================
![7be8d7e79ee94a7bbdf69b8be0846b31_jpg.rf.8dee7640193382d8bcf0ad98820037be_with_roboflow_yolo.jpg](7be8d7e79ee94a7bbdf69b8be0846b31_jpg.rf.8dee7640193382d8bcf0ad98820037be_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"21.10","题目 2":"41.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```
### 响应时间：2.76秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 132 张图片: 7cffd97650de4ba1970a06bb757a73f8_jpg.rf.2a2ecc481509ef552eaee34928bf8fd5_with_roboflow_yolo.jpg

==================================================
![7cffd97650de4ba1970a06bb757a73f8_jpg.rf.2a2ecc481509ef552eaee34928bf8fd5_with_roboflow_yolo.jpg](7cffd97650de4ba1970a06bb757a73f8_jpg.rf.2a2ecc481509ef552eaee34928bf8fd5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"7/4","题目 2":"2 1/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "1/4", "题目2": "11/5", "题目3": "4"}
```
### 响应时间：2.29秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 133 张图片: 7d1c785ab2a0433baa81e4b948692b12_jpg.rf.b598cb7bb874cdcb72fa56110f9f1977_with_roboflow_yolo.jpg

==================================================
![7d1c785ab2a0433baa81e4b948692b12_jpg.rf.b598cb7bb874cdcb72fa56110f9f1977_with_roboflow_yolo.jpg](7d1c785ab2a0433baa81e4b948692b12_jpg.rf.b598cb7bb874cdcb72fa56110f9f1977_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"7","题目 4":"13.35"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 742
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 135 张图片: 7e131a2a9c8a406c925fab971b032fdb_jpg.rf.e4fcfbfce33f098d7498322466a7a2a2_with_roboflow_yolo.jpg

==================================================
![7e131a2a9c8a406c925fab971b032fdb_jpg.rf.e4fcfbfce33f098d7498322466a7a2a2_with_roboflow_yolo.jpg](7e131a2a9c8a406c925fab971b032fdb_jpg.rf.e4fcfbfce33f098d7498322466a7a2a2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"5/10"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.07秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 136 张图片: 7fbf3b878aae4a7bb86dd414ee73cb3b_jpg.rf.b65e35d35475703efb108f0f62e8fc56_with_roboflow_yolo.jpg

==================================================
![7fbf3b878aae4a7bb86dd414ee73cb3b_jpg.rf.b65e35d35475703efb108f0f62e8fc56_with_roboflow_yolo.jpg](7fbf3b878aae4a7bb86dd414ee73cb3b_jpg.rf.b65e35d35475703efb108f0f62e8fc56_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：9.26秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 138 张图片: 81e6464304574fd9a76309ef9706bf0b_jpg.rf.6434ee2379efcdd4a3e5840367da3ae7_with_roboflow_yolo.jpg

==================================================
![81e6464304574fd9a76309ef9706bf0b_jpg.rf.6434ee2379efcdd4a3e5840367da3ae7_with_roboflow_yolo.jpg](81e6464304574fd9a76309ef9706bf0b_jpg.rf.6434ee2379efcdd4a3e5840367da3ae7_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"7/4","题目 2":"2.2","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "4", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：1.76秒
### token用量
- total_tokens: 728
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 141 张图片: 84794cf5092e43fbb743fdeb1f4b7b6e_jpg.rf.c39e9da8c9afb63f8e181390c513b28e_with_roboflow_yolo.jpg

==================================================
![84794cf5092e43fbb743fdeb1f4b7b6e_jpg.rf.c39e9da8c9afb63f8e181390c513b28e_with_roboflow_yolo.jpg](84794cf5092e43fbb743fdeb1f4b7b6e_jpg.rf.c39e9da8c9afb63f8e181390c513b28e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"14/8","题目 2":"11/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "11/5", "题目3": "4"}
```
### 响应时间：1.87秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 143 张图片: 87c24e3f661a43568c5437f662491b93_jpg.rf.84609c48a558e91579afd6562ebebd00_with_roboflow_yolo.jpg

==================================================
![87c24e3f661a43568c5437f662491b93_jpg.rf.84609c48a558e91579afd6562ebebd00_with_roboflow_yolo.jpg](87c24e3f661a43568c5437f662491b93_jpg.rf.84609c48a558e91579afd6562ebebd00_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"1 1/6","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：2.02秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 146 张图片: 8872da4cef4b4013960b204365e2de03_jpg.rf.647faf9395bd0ef028f5d7bc89815205_with_roboflow_yolo.jpg

==================================================
![8872da4cef4b4013960b204365e2de03_jpg.rf.647faf9395bd0ef028f5d7bc89815205_with_roboflow_yolo.jpg](8872da4cef4b4013960b204365e2de03_jpg.rf.647faf9395bd0ef028f5d7bc89815205_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"29/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：2.40秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 147 张图片: 889b4b49c7fd44179eb75e0e3c883a02_jpg.rf.e8fcef3b4bcfa67d3a626b28c9d30584_with_roboflow_yolo.jpg

==================================================
![889b4b49c7fd44179eb75e0e3c883a02_jpg.rf.e8fcef3b4bcfa67d3a626b28c9d30584_with_roboflow_yolo.jpg](889b4b49c7fd44179eb75e0e3c883a02_jpg.rf.e8fcef3b4bcfa67d3a626b28c9d30584_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.71秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 149 张图片: 899c218eb25e40efa5fc68c3f4546a89_jpg.rf.6e233d9946e3eedb1e72ff06cc8d8634_with_roboflow_yolo.jpg

==================================================
![899c218eb25e40efa5fc68c3f4546a89_jpg.rf.6e233d9946e3eedb1e72ff06cc8d8634_with_roboflow_yolo.jpg](899c218eb25e40efa5fc68c3f4546a89_jpg.rf.6e233d9946e3eedb1e72ff06cc8d8634_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "19/9", "题目3": "NAN"}
```
### 响应时间：1.86秒
### token用量
- total_tokens: 733
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 150 张图片: 89c4130264b64f2caeb5976d804253bd_jpg.rf.ba2a00940bd127d1c86169fd40ad625b_with_roboflow_yolo.jpg

==================================================
![89c4130264b64f2caeb5976d804253bd_jpg.rf.ba2a00940bd127d1c86169fd40ad625b_with_roboflow_yolo.jpg](89c4130264b64f2caeb5976d804253bd_jpg.rf.ba2a00940bd127d1c86169fd40ad625b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.59秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 151 张图片: 8a3b522a65324fca9a01a267b8cbe948_jpg.rf.259fdd00c08adfefae86677c97867adf_with_roboflow_yolo.jpg

==================================================
![8a3b522a65324fca9a01a267b8cbe948_jpg.rf.259fdd00c08adfefae86677c97867adf_with_roboflow_yolo.jpg](8a3b522a65324fca9a01a267b8cbe948_jpg.rf.259fdd00c08adfefae86677c97867adf_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"17/10"}
```
### 响应内容：
```json
{"题目1": "13/24", "题目2": "1/6", "题目3": "17/10"}
```
### 响应时间：2.82秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 153 张图片: 8c48731331044e87a9979576c7935623_jpg.rf.57de946e9d3429e47e6d4aedbef577a3_with_roboflow_yolo.jpg

==================================================
![8c48731331044e87a9979576c7935623_jpg.rf.57de946e9d3429e47e6d4aedbef577a3_with_roboflow_yolo.jpg](8c48731331044e87a9979576c7935623_jpg.rf.57de946e9d3429e47e6d4aedbef577a3_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1 3/4","题目 2":"2 1/5","题目 3":"4 1/2"}
```
### 响应内容：
```json
{"题目1": "1 3/4", "题目2": "2 1/2", "题目3": "4 1/2"}
```
### 响应时间：3.03秒
### token用量
- total_tokens: 739
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 154 张图片: 8c5a5283bd0c4ae4b5c8a8bce235ba4c_jpg.rf.691adec9ee542d6f15586cc0ade22fdf_with_roboflow_yolo.jpg

==================================================
![8c5a5283bd0c4ae4b5c8a8bce235ba4c_jpg.rf.691adec9ee542d6f15586cc0ade22fdf_with_roboflow_yolo.jpg](8c5a5283bd0c4ae4b5c8a8bce235ba4c_jpg.rf.691adec9ee542d6f15586cc0ade22fdf_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1 3/4","题目 2":"2 1/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "11/5", "题目3": "4"}
```
### 响应时间：1.40秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 156 张图片: 8eabc9f3826d48ada1a9b990fcde6e02_jpg.rf.db84f23a06ff3bee599fc44806ed341e_with_roboflow_yolo.jpg

==================================================
![8eabc9f3826d48ada1a9b990fcde6e02_jpg.rf.db84f23a06ff3bee599fc44806ed341e_with_roboflow_yolo.jpg](8eabc9f3826d48ada1a9b990fcde6e02_jpg.rf.db84f23a06ff3bee599fc44806ed341e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"7","题目 4":"11 16/8"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 157 张图片: 91a3c7cf13a240cd8344d28f7e2ffae4_jpg.rf.80324edc4de28827d8088fbaf8e0c82b_with_roboflow_yolo.jpg

==================================================
![91a3c7cf13a240cd8344d28f7e2ffae4_jpg.rf.80324edc4de28827d8088fbaf8e0c82b_with_roboflow_yolo.jpg](91a3c7cf13a240cd8344d28f7e2ffae4_jpg.rf.80324edc4de28827d8088fbaf8e0c82b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"1 1/3","题目 3":"7","题目 4":"12"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.39秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 158 张图片: 91cc422a43f74c2286f8156111482398_jpg.rf.791df95a6efe6df2135b4fd917de81e9_with_roboflow_yolo.jpg

==================================================
![91cc422a43f74c2286f8156111482398_jpg.rf.791df95a6efe6df2135b4fd917de81e9_with_roboflow_yolo.jpg](91cc422a43f74c2286f8156111482398_jpg.rf.791df95a6efe6df2135b4fd917de81e9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"1 7/8"}
```
### 响应内容：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "17/10"}
```
### 响应时间：3.02秒
### token用量
- total_tokens: 769
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 159 张图片: 92de5e2e50ec4c3cac8346816b81cb47_jpg.rf.5dad893c605ebab3c457068a005ff9b9_with_roboflow_yolo.jpg

==================================================
![92de5e2e50ec4c3cac8346816b81cb47_jpg.rf.5dad893c605ebab3c457068a005ff9b9_with_roboflow_yolo.jpg](92de5e2e50ec4c3cac8346816b81cb47_jpg.rf.5dad893c605ebab3c457068a005ff9b9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "x = 1", "题目2": "x = 2.2", "题目3": "y = 4"}
```
### 响应时间：2.17秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 160 张图片: 9382c58c96374309b8e2114f4240d3a5_jpg.rf.ca7c58fc5ca7c87fda1b57aeded163d0_with_roboflow_yolo.jpg

==================================================
![9382c58c96374309b8e2114f4240d3a5_jpg.rf.ca7c58fc5ca7c87fda1b57aeded163d0_with_roboflow_yolo.jpg](9382c58c96374309b8e2114f4240d3a5_jpg.rf.ca7c58fc5ca7c87fda1b57aeded163d0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.36秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 162 张图片: 952c480949b5418aa17214222956228a_jpg.rf.4440303836e33a085d883e7c1dcb8ce8_with_roboflow_yolo.jpg

==================================================
![952c480949b5418aa17214222956228a_jpg.rf.4440303836e33a085d883e7c1dcb8ce8_with_roboflow_yolo.jpg](952c480949b5418aa17214222956228a_jpg.rf.4440303836e33a085d883e7c1dcb8ce8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"35/8","题目 2":"3 4/5","题目 3":"1.2"}
```
### 响应内容：
```json
{"题目1": "35/8", "题目2": "19/5", "题目3": "1.2"}
```
### 响应时间：2.49秒
### token用量
- total_tokens: 735
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 164 张图片: 96a659efe50e44e3be318700019e0157_jpg.rf.1d31344d3fdb85b656d9ae25b84cd0d9_with_roboflow_yolo.jpg

==================================================
![96a659efe50e44e3be318700019e0157_jpg.rf.1d31344d3fdb85b656d9ae25b84cd0d9_with_roboflow_yolo.jpg](96a659efe50e44e3be318700019e0157_jpg.rf.1d31344d3fdb85b656d9ae25b84cd0d9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"7","题目 3":"13/21","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：1.71秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 165 张图片: 96b650417d3b41c29b221ae796cfdc11_jpg.rf.f6b9e0af02e57554136a731bb5ef8515_with_roboflow_yolo.jpg

==================================================
![96b650417d3b41c29b221ae796cfdc11_jpg.rf.f6b9e0af02e57554136a731bb5ef8515_with_roboflow_yolo.jpg](96b650417d3b41c29b221ae796cfdc11_jpg.rf.f6b9e0af02e57554136a731bb5ef8515_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"NAN","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "3\\frac{2}{7}", "题目2": "2\\frac{1}{9}", "题目3": "7", "题目4": "13"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 166 张图片: 978e6cb90e514cda92781410095f1355_jpg.rf.466e0c1afeda7adf6871a4d877bcf14c_with_roboflow_yolo.jpg

==================================================
![978e6cb90e514cda92781410095f1355_jpg.rf.466e0c1afeda7adf6871a4d877bcf14c_with_roboflow_yolo.jpg](978e6cb90e514cda92781410095f1355_jpg.rf.466e0c1afeda7adf6871a4d877bcf14c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/13","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "2/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：17.91秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 168 张图片: 99b76d5195c4430fac0bba2098d59759_jpg.rf.8a0d087495d4bbc2e8590362ecaf438b_with_roboflow_yolo.jpg

==================================================
![99b76d5195c4430fac0bba2098d59759_jpg.rf.8a0d087495d4bbc2e8590362ecaf438b_with_roboflow_yolo.jpg](99b76d5195c4430fac0bba2098d59759_jpg.rf.8a0d087495d4bbc2e8590362ecaf438b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"26/7","题目 2":"1 1/3","题目 3":"7","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN"}
```
### 响应时间：1.55秒
### token用量
- total_tokens: 741
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 169 张图片: 9ab81fdd9ba345a89cacf6994f3f64c3_jpg.rf.1a64a46babe36336c316e93fb5eabae0_with_roboflow_yolo.jpg

==================================================
![9ab81fdd9ba345a89cacf6994f3f64c3_jpg.rf.1a64a46babe36336c316e93fb5eabae0_with_roboflow_yolo.jpg](9ab81fdd9ba345a89cacf6994f3f64c3_jpg.rf.1a64a46babe36336c316e93fb5eabae0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19/24","题目 2":"1 3/6","题目 3":"17/10"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.01秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 171 张图片: 9d18c7536abe48aabecc195dbf9f14a2_jpg.rf.b63bd36ac8c9ea5bd8c2c22a868a8b44_with_roboflow_yolo.jpg

==================================================
![9d18c7536abe48aabecc195dbf9f14a2_jpg.rf.b63bd36ac8c9ea5bd8c2c22a868a8b44_with_roboflow_yolo.jpg](9d18c7536abe48aabecc195dbf9f14a2_jpg.rf.b63bd36ac8c9ea5bd8c2c22a868a8b44_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2.22","题目 2":"NAN","题目 3":"7","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目1": "2.22", "题目2": "2/9 - 2/8", "题目3": "7", "题目4": "1/8 + 11"}
```
### 响应时间：2.85秒
### token用量
- total_tokens: 750
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 172 张图片: 9d95e1e189a14fa88b51ef692b6e02b1_jpg.rf.31550c80c4df5e3bc125dc0954bd595f_with_roboflow_yolo.jpg

==================================================
![9d95e1e189a14fa88b51ef692b6e02b1_jpg.rf.31550c80c4df5e3bc125dc0954bd595f_with_roboflow_yolo.jpg](9d95e1e189a14fa88b51ef692b6e02b1_jpg.rf.31550c80c4df5e3bc125dc0954bd595f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"2 3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "23/10"}
```
### 响应时间：2.30秒
### token用量
- total_tokens: 763
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 176 张图片: 9f77339eec17436397b9277156ff3856_jpg.rf.0a25e44c06efc02be6fb39b5ead4f395_with_roboflow_yolo.jpg

==================================================
![9f77339eec17436397b9277156ff3856_jpg.rf.0a25e44c06efc02be6fb39b5ead4f395_with_roboflow_yolo.jpg](9f77339eec17436397b9277156ff3856_jpg.rf.0a25e44c06efc02be6fb39b5ead4f395_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：2.11秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 177 张图片: 9f8f3b7a668c4dd1a530f57d4821ca20_jpg.rf.1b2321d674b46339b4b0ef56d744a30d_with_roboflow_yolo.jpg

==================================================
![9f8f3b7a668c4dd1a530f57d4821ca20_jpg.rf.1b2321d674b46339b4b0ef56d744a30d_with_roboflow_yolo.jpg](9f8f3b7a668c4dd1a530f57d4821ca20_jpg.rf.1b2321d674b46339b4b0ef56d744a30d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"2/3","题目 3":"7/10"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：3.09秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 179 张图片: a36fc79a7dd442a6972a542925be9b10_jpg.rf.66623a9e070ff22f1c219d405f27e035_with_roboflow_yolo.jpg

==================================================
![a36fc79a7dd442a6972a542925be9b10_jpg.rf.66623a9e070ff22f1c219d405f27e035_with_roboflow_yolo.jpg](a36fc79a7dd442a6972a542925be9b10_jpg.rf.66623a9e070ff22f1c219d405f27e035_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.13秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 181 张图片: a46b5f053cfd4b7da6bb54a4e14ade29_jpg.rf.d453b85c646156a05ae9ce3f68e18c8f_with_roboflow_yolo.jpg

==================================================
![a46b5f053cfd4b7da6bb54a4e14ade29_jpg.rf.d453b85c646156a05ae9ce3f68e18c8f_with_roboflow_yolo.jpg](a46b5f053cfd4b7da6bb54a4e14ade29_jpg.rf.d453b85c646156a05ae9ce3f68e18c8f_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"7/4","题目 2":"2 1/5","题目 3":"6 1/2"}
```
### 响应内容：
```json
{"题目1": "4", "题目2": "2.2", "题目3": "4.8"}
```
### 响应时间：2.20秒
### token用量
- total_tokens: 731
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 182 张图片: a48efb0ff2394fc9a2ca70dd1620d97f_jpg.rf.61f77ef5f25fe20f49a2962ec1054180_with_roboflow_yolo.jpg

==================================================
![a48efb0ff2394fc9a2ca70dd1620d97f_jpg.rf.61f77ef5f25fe20f49a2962ec1054180_with_roboflow_yolo.jpg](a48efb0ff2394fc9a2ca70dd1620d97f_jpg.rf.61f77ef5f25fe20f49a2962ec1054180_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"NAN","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 184 张图片: a6150cb1647a4649bf0b55fdfdfcbe0a_jpg.rf.0084fe0b2fa27ce891fbc4e6ace1c2fc_with_roboflow_yolo.jpg

==================================================
![a6150cb1647a4649bf0b55fdfdfcbe0a_jpg.rf.0084fe0b2fa27ce891fbc4e6ace1c2fc_with_roboflow_yolo.jpg](a6150cb1647a4649bf0b55fdfdfcbe0a_jpg.rf.0084fe0b2fa27ce891fbc4e6ace1c2fc_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1": 13/24", "题目 2": "5/6", "题目 3": "1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.44秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 185 张图片: a69b3bca7626461a8e6889f737b067b9_jpg.rf.c946c8516bda55291a28dfd761fc4aaf_with_roboflow_yolo.jpg

==================================================
![a69b3bca7626461a8e6889f737b067b9_jpg.rf.c946c8516bda55291a28dfd761fc4aaf_with_roboflow_yolo.jpg](a69b3bca7626461a8e6889f737b067b9_jpg.rf.c946c8516bda55291a28dfd761fc4aaf_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"NAN","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：2.35秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 186 张图片: a78d7963c6284607958476f1c7c7cdf5_jpg.rf.c92a4edd6faf9160283f0cb31223599d_with_roboflow_yolo.jpg

==================================================
![a78d7963c6284607958476f1c7c7cdf5_jpg.rf.c92a4edd6faf9160283f0cb31223599d_with_roboflow_yolo.jpg](a78d7963c6284607958476f1c7c7cdf5_jpg.rf.c92a4edd6faf9160283f0cb31223599d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"3 1/4"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：9.34秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 191 张图片: ae175a3a16364661b55d0896746cd911_jpg.rf.278567c8c718638a0a2f3576e5b615ea_with_roboflow_yolo.jpg

==================================================
![ae175a3a16364661b55d0896746cd911_jpg.rf.278567c8c718638a0a2f3576e5b615ea_with_roboflow_yolo.jpg](ae175a3a16364661b55d0896746cd911_jpg.rf.278567c8c718638a0a2f3576e5b615ea_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"0","题目 2":"2 1/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "0", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：1.79秒
### token用量
- total_tokens: 728
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 192 张图片: b06025e1150d47588a60f0c84e4b1b25_jpg.rf.a83d9701ba57fcd9e9400847897a1978_with_roboflow_yolo.jpg

==================================================
![b06025e1150d47588a60f0c84e4b1b25_jpg.rf.a83d9701ba57fcd9e9400847897a1978_with_roboflow_yolo.jpg](b06025e1150d47588a60f0c84e4b1b25_jpg.rf.a83d9701ba57fcd9e9400847897a1978_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"14/8","题目 2":"2 1/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "11/5", "题目3": "4"}
```
### 响应时间：2.31秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 195 张图片: b4b4a6678adb467ba95c72e63fd6b98c_jpg.rf.ad742a6fa8d7c2602b0d242656bd004b_with_roboflow_yolo.jpg

==================================================
![b4b4a6678adb467ba95c72e63fd6b98c_jpg.rf.ad742a6fa8d7c2602b0d242656bd004b_with_roboflow_yolo.jpg](b4b4a6678adb467ba95c72e63fd6b98c_jpg.rf.ad742a6fa8d7c2602b0d242656bd004b_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"7/4","题目 2":"NAN","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "7/4", "题目2": "21/5", "题目3": "4"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 196 张图片: b61ba0d3996e410a8eb79ced33371275_jpg.rf.f8bf461031d1fb0536512b179be6f96e_with_roboflow_yolo.jpg

==================================================
![b61ba0d3996e410a8eb79ced33371275_jpg.rf.f8bf461031d1fb0536512b179be6f96e_with_roboflow_yolo.jpg](b61ba0d3996e410a8eb79ced33371275_jpg.rf.f8bf461031d1fb0536512b179be6f96e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"NAN","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：1.88秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 198 张图片: b6db2af9e01d41228de313a1ec90d1f1_jpg.rf.10007ddefaedda11e5018aa2d0581f4e_with_roboflow_yolo.jpg

==================================================
![b6db2af9e01d41228de313a1ec90d1f1_jpg.rf.10007ddefaedda11e5018aa2d0581f4e_with_roboflow_yolo.jpg](b6db2af9e01d41228de313a1ec90d1f1_jpg.rf.10007ddefaedda11e5018aa2d0581f4e_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"4","题目 2":"4","题目 3":"645","题目 4":"4","题目 5":"110","题目 6":"NAN"}
```
### 响应内容：
```json
{"题目1": "1", "题目2": "2 / 3", "题目3": "0.15", "题目4": "1 / 18", "题目5": "11 / 12", "题目6": "NAN"}
```
### 响应时间：2.73秒
### token用量
- total_tokens: 764
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 199 张图片: b732e08f1aa141de894c8f1fefdfea38_jpg.rf.31c9140363a2202b50584f8952d2a1ed_with_roboflow_yolo.jpg

==================================================
![b732e08f1aa141de894c8f1fefdfea38_jpg.rf.31c9140363a2202b50584f8952d2a1ed_with_roboflow_yolo.jpg](b732e08f1aa141de894c8f1fefdfea38_jpg.rf.31c9140363a2202b50584f8952d2a1ed_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"1"}
```
### 响应内容：
```json
{"题目 1": "2", "题目 2": "1", "题目 3": "0.1", "题目 4": "7/18", "题目 5": "3/4", "题目 6": "1"}
```
### 响应时间：3.03秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 201 张图片: b8f90b230ebc4459af32ac4c72928202_jpg.rf.e10f713545271781c0371fa05ead9631_with_roboflow_yolo.jpg

==================================================
![b8f90b230ebc4459af32ac4c72928202_jpg.rf.e10f713545271781c0371fa05ead9631_with_roboflow_yolo.jpg](b8f90b230ebc4459af32ac4c72928202_jpg.rf.e10f713545271781c0371fa05ead9631_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"29/24","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：8.64秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 202 张图片: bd4c5b7bd6fa49089c50c4ca4ac61169_jpg.rf.ce8e79fda234714e0f0bca2aae577c0c_with_roboflow_yolo.jpg

==================================================
![bd4c5b7bd6fa49089c50c4ca4ac61169_jpg.rf.ce8e79fda234714e0f0bca2aae577c0c_with_roboflow_yolo.jpg](bd4c5b7bd6fa49089c50c4ca4ac61169_jpg.rf.ce8e79fda234714e0f0bca2aae577c0c_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"NAN","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "7/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：3.33秒
### token用量
- total_tokens: 760
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 204 张图片: bf2e692bb399419cb8591e6ab5f6b92c_jpg.rf.38202b0bdc094918146cd5b4f828034a_with_roboflow_yolo.jpg

==================================================
![bf2e692bb399419cb8591e6ab5f6b92c_jpg.rf.38202b0bdc094918146cd5b4f828034a_with_roboflow_yolo.jpg](bf2e692bb399419cb8591e6ab5f6b92c_jpg.rf.38202b0bdc094918146cd5b4f828034a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"21.10","题目 2":"41.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```
### 响应时间：2.48秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 205 张图片: bf5f99b54af2430bad8c25ac4d2383e6_jpg.rf.750b6904510a75cf59b0e5cffd095942_with_roboflow_yolo.jpg

==================================================
![bf5f99b54af2430bad8c25ac4d2383e6_jpg.rf.750b6904510a75cf59b0e5cffd095942_with_roboflow_yolo.jpg](bf5f99b54af2430bad8c25ac4d2383e6_jpg.rf.750b6904510a75cf59b0e5cffd095942_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"7/4","题目 2":"2.2","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "1.75", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：2.06秒
### token用量
- total_tokens: 731
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 207 张图片: c01637f4e812481f9d78a8676ec57053_jpg.rf.56c01fb4437773163f4ff876db5ffeab_with_roboflow_yolo.jpg

==================================================
![c01637f4e812481f9d78a8676ec57053_jpg.rf.56c01fb4437773163f4ff876db5ffeab_with_roboflow_yolo.jpg](c01637f4e812481f9d78a8676ec57053_jpg.rf.56c01fb4437773163f4ff876db5ffeab_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"9/8"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "2/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "9/8"}
```
### 响应时间：3.31秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 208 张图片: c1dbce9f498242529fb83f4bc14f3485_jpg.rf.8cf84810a1dce2bad2ba31e3ea3dc5b8_with_roboflow_yolo.jpg

==================================================
![c1dbce9f498242529fb83f4bc14f3485_jpg.rf.8cf84810a1dce2bad2ba31e3ea3dc5b8_with_roboflow_yolo.jpg](c1dbce9f498242529fb83f4bc14f3485_jpg.rf.8cf84810a1dce2bad2ba31e3ea3dc5b8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1 3/4","题目 2":"2 1/5","题目 3":"4"}
```
### 响应内容：
```json
{"题目1": "1.75", "题目2": "2.2", "题目3": "4"}
```
### 响应时间：2.29秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 210 张图片: c2a6b2436fc94c469f0ded118b7a0831_jpg.rf.91d229de48ea81d48abd9f95889c5b70_with_roboflow_yolo.jpg

==================================================
![c2a6b2436fc94c469f0ded118b7a0831_jpg.rf.91d229de48ea81d48abd9f95889c5b70_with_roboflow_yolo.jpg](c2a6b2436fc94c469f0ded118b7a0831_jpg.rf.91d229de48ea81d48abd9f95889c5b70_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"9/10","题目 4":"13/18","题目 5":"3/4","题目 6":"1 1/8"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "9/20", "题目4": "7/18", "题目5": "3/4", "题目6": "18"}
```
### 响应时间：2.56秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 212 张图片: c3fe34e423714b36b6b2a46f1f9a0107_jpg.rf.fe6b6d906e3e59c521ef791908e473c8_with_roboflow_yolo.jpg

==================================================
![c3fe34e423714b36b6b2a46f1f9a0107_jpg.rf.fe6b6d906e3e59c521ef791908e473c8_with_roboflow_yolo.jpg](c3fe34e423714b36b6b2a46f1f9a0107_jpg.rf.fe6b6d906e3e59c521ef791908e473c8_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"19.00","题目 2":"30.1","题目 3":"2700","题目 4":"2346","题目 5":"24","题目 6":"8"}
```
### 响应内容：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```
### 响应时间：2.22秒
### token用量
- total_tokens: 759
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 214 张图片: c6e31135ad564e1797881de5d7e56e67_jpg.rf.959864a363a647fc8ac2b11110b559a0_with_roboflow_yolo.jpg

==================================================
![c6e31135ad564e1797881de5d7e56e67_jpg.rf.959864a363a647fc8ac2b11110b559a0_with_roboflow_yolo.jpg](c6e31135ad564e1797881de5d7e56e67_jpg.rf.959864a363a647fc8ac2b11110b559a0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1/12","题目 3":"7","题目 4":"3.1"}
```
### 响应内容：
```json
{"题目1": "5 3/4", "题目2": "1/12", "题目3": "7 8/21", "题目4": "13"}
```
### 响应时间：3.02秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 215 张图片: c87733c71a0948c3966721a42880cbd3_jpg.rf.b6928f57ceb3de96711400c088f91cb0_with_roboflow_yolo.jpg

==================================================
![c87733c71a0948c3966721a42880cbd3_jpg.rf.b6928f57ceb3de96711400c088f91cb0_with_roboflow_yolo.jpg](c87733c71a0948c3966721a42880cbd3_jpg.rf.b6928f57ceb3de96711400c088f91cb0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"NAN","题目 4":"13/18","题目 5":"3/4","题目 6":"13/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.3", "题目4": "13/18", "题目5": "3/4", "题目6": "13/10"}
```
### 响应时间：2.52秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 216 张图片: c8fcf9d2900247f19491736f666e0e9d_jpg.rf.2e3afe5a454027fbe67c71c5b4eeaa1a_with_roboflow_yolo.jpg

==================================================
![c8fcf9d2900247f19491736f666e0e9d_jpg.rf.2e3afe5a454027fbe67c71c5b4eeaa1a_with_roboflow_yolo.jpg](c8fcf9d2900247f19491736f666e0e9d_jpg.rf.2e3afe5a454027fbe67c71c5b4eeaa1a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"6"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.12秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 217 张图片: cb0c739c98d5443cbff0d95a29cad191_jpg.rf.6fbf96b92911819df97e95026f15f487_with_roboflow_yolo.jpg

==================================================
![cb0c739c98d5443cbff0d95a29cad191_jpg.rf.6fbf96b92911819df97e95026f15f487_with_roboflow_yolo.jpg](cb0c739c98d5443cbff0d95a29cad191_jpg.rf.6fbf96b92911819df97e95026f15f487_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"2/9","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.04秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 220 张图片: ccb8baccd20c4f57acf47c1f97f812d4_jpg.rf.5cbb71e700d6cbaf9caeb9780bbb54bf_with_roboflow_yolo.jpg

==================================================
![ccb8baccd20c4f57acf47c1f97f812d4_jpg.rf.5cbb71e700d6cbaf9caeb9780bbb54bf_with_roboflow_yolo.jpg](ccb8baccd20c4f57acf47c1f97f812d4_jpg.rf.5cbb71e700d6cbaf9caeb9780bbb54bf_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"NAN","题目 4":"13/16","题目 5":"9/12","题目 6":"1 1/8"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/16", "题目5": "3/4", "题目6": "1 1/8"}
```
### 响应时间：3.30秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 221 张图片: cd343b2780e84eef868ae60df27b4085_jpg.rf.8c179d88e056feda7557203d83921104_with_roboflow_yolo.jpg

==================================================
![cd343b2780e84eef868ae60df27b4085_jpg.rf.8c179d88e056feda7557203d83921104_with_roboflow_yolo.jpg](cd343b2780e84eef868ae60df27b4085_jpg.rf.8c179d88e056feda7557203d83921104_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 19/28","题目 2":"33/12","题目 3":"NAN","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN"}
```
### 响应时间：2.12秒
### token用量
- total_tokens: 742
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 223 张图片: cea2bf8d011f4559a64a641b306f3b10_jpg.rf.0522b159e8a88d743e3d5f70c3c8b8bb_with_roboflow_yolo.jpg

==================================================
![cea2bf8d011f4559a64a641b306f3b10_jpg.rf.0522b159e8a88d743e3d5f70c3c8b8bb_with_roboflow_yolo.jpg](cea2bf8d011f4559a64a641b306f3b10_jpg.rf.0522b159e8a88d743e3d5f70c3c8b8bb_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/5"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：2.65秒
### token用量
- total_tokens: 732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 224 张图片: cead67d5f5eb4361b9d3f513ee6db779_jpg.rf.e176cb06d80d8e7a1f240d3a458cee00_with_roboflow_yolo.jpg

==================================================
![cead67d5f5eb4361b9d3f513ee6db779_jpg.rf.e176cb06d80d8e7a1f240d3a458cee00_with_roboflow_yolo.jpg](cead67d5f5eb4361b9d3f513ee6db779_jpg.rf.e176cb06d80d8e7a1f240d3a458cee00_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN"}
```
### 响应时间：1.90秒
### token用量
- total_tokens: 742
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 225 张图片: ceceac8cda3441ef9199b9fab3cce1e5_jpg.rf.f8975f9d9525e7852de8188c8ec83c59_with_roboflow_yolo.jpg

==================================================
![ceceac8cda3441ef9199b9fab3cce1e5_jpg.rf.f8975f9d9525e7852de8188c8ec83c59_with_roboflow_yolo.jpg](ceceac8cda3441ef9199b9fab3cce1e5_jpg.rf.f8975f9d9525e7852de8188c8ec83c59_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN"}
```
### 响应时间：1.91秒
### token用量
- total_tokens: 733
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 226 张图片: cf335ffb4ae144e785436f7f5f3579c7_jpg.rf.6f3303adb6f72295971e6beb92cccd21_with_roboflow_yolo.jpg

==================================================
![cf335ffb4ae144e785436f7f5f3579c7_jpg.rf.6f3303adb6f72295971e6beb92cccd21_with_roboflow_yolo.jpg](cf335ffb4ae144e785436f7f5f3579c7_jpg.rf.6f3303adb6f72295971e6beb92cccd21_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 227 张图片: d17104884c9d458789519054309475ee_jpg.rf.519dd9b95ef07cf974dd62f6c95516dd_with_roboflow_yolo.jpg

==================================================
![d17104884c9d458789519054309475ee_jpg.rf.519dd9b95ef07cf974dd62f6c95516dd_with_roboflow_yolo.jpg](d17104884c9d458789519054309475ee_jpg.rf.519dd9b95ef07cf974dd62f6c95516dd_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"2/9","题目 5":"3/4","题目 6":"1 23/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/10", "题目4": "2/9", "题目5": "3/4", "题目6": "13/10"}
```
### 响应时间：2.87秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 230 张图片: d71ed599a15147ae83f95b8bf3edf804_jpg.rf.3b6c4b07a334f75b7de508d8e8f692d1_with_roboflow_yolo.jpg

==================================================
![d71ed599a15147ae83f95b8bf3edf804_jpg.rf.3b6c4b07a334f75b7de508d8e8f692d1_with_roboflow_yolo.jpg](d71ed599a15147ae83f95b8bf3edf804_jpg.rf.3b6c4b07a334f75b7de508d8e8f692d1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"5/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：1.84秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 231 张图片: d725ba45265f4dc4a963ade4c72c0e53_jpg.rf.1716499b8c0994044c46b79c1a0e54a5_with_roboflow_yolo.jpg

==================================================
![d725ba45265f4dc4a963ade4c72c0e53_jpg.rf.1716499b8c0994044c46b79c1a0e54a5_with_roboflow_yolo.jpg](d725ba45265f4dc4a963ade4c72c0e53_jpg.rf.1716499b8c0994044c46b79c1a0e54a5_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"7/9","题目 3":"0","题目 4":"11 16/8"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN"}
```
### 响应时间：2.14秒
### token用量
- total_tokens: 741
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 232 张图片: d8e7eed992f04f18a143c181a5c092ee_jpg.rf.1e70bc20eab2bcf868b37a919f865b4d_with_roboflow_yolo.jpg

==================================================
![d8e7eed992f04f18a143c181a5c092ee_jpg.rf.1e70bc20eab2bcf868b37a919f865b4d_with_roboflow_yolo.jpg](d8e7eed992f04f18a143c181a5c092ee_jpg.rf.1e70bc20eab2bcf868b37a919f865b4d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/5", "题目4": "13/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：3.66秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 234 张图片: db0f58569f8944ad8e1ccfe63f2545e2_jpg.rf.9dc22d4871121f383ffb62d1ad9cf878_with_roboflow_yolo.jpg

==================================================
![db0f58569f8944ad8e1ccfe63f2545e2_jpg.rf.9dc22d4871121f383ffb62d1ad9cf878_with_roboflow_yolo.jpg](db0f58569f8944ad8e1ccfe63f2545e2_jpg.rf.9dc22d4871121f383ffb62d1ad9cf878_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.16秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 235 张图片: db17512718524dfdb39d9469216a5cb3_jpg.rf.f59c4ca52ce5553099815988c9ba3011_with_roboflow_yolo.jpg

==================================================
![db17512718524dfdb39d9469216a5cb3_jpg.rf.f59c4ca52ce5553099815988c9ba3011_with_roboflow_yolo.jpg](db17512718524dfdb39d9469216a5cb3_jpg.rf.f59c4ca52ce5553099815988c9ba3011_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 33/56","题目 2":"4/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN"}
```
### 响应时间：1.84秒
### token用量
- total_tokens: 741
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 237 张图片: db92b172d4b84317a4e95768a42b42bd_jpg.rf.36d8d60df297739e9fb1d53afe51094d_with_roboflow_yolo.jpg

==================================================
![db92b172d4b84317a4e95768a42b42bd_jpg.rf.36d8d60df297739e9fb1d53afe51094d_with_roboflow_yolo.jpg](db92b172d4b84317a4e95768a42b42bd_jpg.rf.36d8d60df297739e9fb1d53afe51094d_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：1.85秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 243 张图片: e067b9281f554f29bb6c03f258fd50f2_jpg.rf.4f318b05fd97054ef035408deb0ff251_with_roboflow_yolo.jpg

==================================================
![e067b9281f554f29bb6c03f258fd50f2_jpg.rf.4f318b05fd97054ef035408deb0ff251_with_roboflow_yolo.jpg](e067b9281f554f29bb6c03f258fd50f2_jpg.rf.4f318b05fd97054ef035408deb0ff251_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"1 1/3","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "3\\frac{5}{7}", "题目2": "1\\frac{1}{3}", "题目3": "7", "题目4": "13"}
```
### 响应时间：1.83秒
### token用量
- total_tokens: 747
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 246 张图片: e34a2bb76bd04eed9efa0cac8b127a07_jpg.rf.7f75c0b5ab86d5f3bc74fc088c8bf164_with_roboflow_yolo.jpg

==================================================
![e34a2bb76bd04eed9efa0cac8b127a07_jpg.rf.7f75c0b5ab86d5f3bc74fc088c8bf164_with_roboflow_yolo.jpg](e34a2bb76bd04eed9efa0cac8b127a07_jpg.rf.7f75c0b5ab86d5f3bc74fc088c8bf164_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "2/3", "题目3": "1/10", "题目4": "13/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：3.90秒
### token用量
- total_tokens: 762
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 248 张图片: e56e3767198442a99091b5d35a63993c_jpg.rf.3691dd1883a5e29cca1e9cb4d1b3dea9_with_roboflow_yolo.jpg

==================================================
![e56e3767198442a99091b5d35a63993c_jpg.rf.3691dd1883a5e29cca1e9cb4d1b3dea9_with_roboflow_yolo.jpg](e56e3767198442a99091b5d35a63993c_jpg.rf.3691dd1883a5e29cca1e9cb4d1b3dea9_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "2/3", "题目3": "NAN", "题目4": "13/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：2.24秒
### token用量
- total_tokens: 760
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 249 张图片: e615935c87e6472087f5f22fe3fcaa99_jpg.rf.b5891d7eb21f48bac4c18d0d33606cd2_with_roboflow_yolo.jpg

==================================================
![e615935c87e6472087f5f22fe3fcaa99_jpg.rf.b5891d7eb21f48bac4c18d0d33606cd2_with_roboflow_yolo.jpg](e615935c87e6472087f5f22fe3fcaa99_jpg.rf.b5891d7eb21f48bac4c18d0d33606cd2_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"13/24","题目 2":"1 1/6","题目 3":"1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.15秒
### token用量
- total_tokens: 729
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 252 张图片: e6d6604e62554b73ace62af2f867ed36_jpg.rf.587579abab86b9d5fdf8b4d5114a0186_with_roboflow_yolo.jpg

==================================================
![e6d6604e62554b73ace62af2f867ed36_jpg.rf.587579abab86b9d5fdf8b4d5114a0186_with_roboflow_yolo.jpg](e6d6604e62554b73ace62af2f867ed36_jpg.rf.587579abab86b9d5fdf8b4d5114a0186_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"104/28","题目 2":"3 20/28","题目 3":"7","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.47秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 254 张图片: e764a1879bdb423999e01036ef46e378_jpg.rf.7fee60ee68b713a68cf85923fd1c586a_with_roboflow_yolo.jpg

==================================================
![e764a1879bdb423999e01036ef46e378_jpg.rf.7fee60ee68b713a68cf85923fd1c586a_with_roboflow_yolo.jpg](e764a1879bdb423999e01036ef46e378_jpg.rf.7fee60ee68b713a68cf85923fd1c586a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"1 1/2","题目 2":"NAN","题目 3":"3"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.32秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 258 张图片: ed64418a571f496ca3671a1b18e17e38_jpg.rf.255c1a7ba308fb134bfc5933b79ec335_with_roboflow_yolo.jpg

==================================================
![ed64418a571f496ca3671a1b18e17e38_jpg.rf.255c1a7ba308fb134bfc5933b79ec335_with_roboflow_yolo.jpg](ed64418a571f496ca3671a1b18e17e38_jpg.rf.255c1a7ba308fb134bfc5933b79ec335_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"11.1","题目 2":"41.52","题目 3":"27.44"}
```
### 响应内容：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```
### 响应时间：2.50秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 260 张图片: eef1337993f24572b8165f18dc33c50f_jpg.rf.89e94ed8061722bf76dc610cc9f5b730_with_roboflow_yolo.jpg

==================================================
![eef1337993f24572b8165f18dc33c50f_jpg.rf.89e94ed8061722bf76dc610cc9f5b730_with_roboflow_yolo.jpg](eef1337993f24572b8165f18dc33c50f_jpg.rf.89e94ed8061722bf76dc610cc9f5b730_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"13/18","题目 5":"3/4","题目 6":"1 1/8"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "9/8"}
```
### 响应时间：2.73秒
### token用量
- total_tokens: 758
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 262 张图片: ef5eda474da0438386f4ac64cd5443db_jpg.rf.693444322228070aa06d1574c2ea76fa_with_roboflow_yolo.jpg

==================================================
![ef5eda474da0438386f4ac64cd5443db_jpg.rf.693444322228070aa06d1574c2ea76fa_with_roboflow_yolo.jpg](ef5eda474da0438386f4ac64cd5443db_jpg.rf.693444322228070aa06d1574c2ea76fa_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"NAN","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 响应时间：2.50秒
### token用量
- total_tokens: 737
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 264 张图片: f1df20275a0b4668b1fdb887930eac3d_jpg.rf.3e970929aa8a6d0f83eb2558144a8b2a_with_roboflow_yolo.jpg

==================================================
![f1df20275a0b4668b1fdb887930eac3d_jpg.rf.3e970929aa8a6d0f83eb2558144a8b2a_with_roboflow_yolo.jpg](f1df20275a0b4668b1fdb887930eac3d_jpg.rf.3e970929aa8a6d0f83eb2558144a8b2a_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"35/7","题目 2":"1","题目 3":"7","题目 4":"13"}
```
### 响应内容：
```json
{"题目1": "3.75", "题目2": "1", "题目3": "7", "题目4": "13"}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 738
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 267 张图片: f2d0cbb9707b4090bdb25d49d1b9db1b_jpg.rf.52563ec9793a024d9bda353b474edf93_with_roboflow_yolo.jpg

==================================================
![f2d0cbb9707b4090bdb25d49d1b9db1b_jpg.rf.52563ec9793a024d9bda353b474edf93_with_roboflow_yolo.jpg](f2d0cbb9707b4090bdb25d49d1b9db1b_jpg.rf.52563ec9793a024d9bda353b474edf93_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"1/2","题目 5":"9/12","题目 6":"2 3/10"}
```
### 响应内容：
```json
{"题目1": "22", "题目2": "1/3", "题目3": "1", "题目4": "1/2", "题目5": "1/12", "题目6": "2 3/10"}
```
### 响应时间：3.96秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 268 张图片: f402b869fb6d4b2a87eb5ed2a8619269_jpg.rf.f93b5c360c4bc4dfa126b539090543d6_with_roboflow_yolo.jpg

==================================================
![f402b869fb6d4b2a87eb5ed2a8619269_jpg.rf.f93b5c360c4bc4dfa126b539090543d6_with_roboflow_yolo.jpg](f402b869fb6d4b2a87eb5ed2a8619269_jpg.rf.f93b5c360c4bc4dfa126b539090543d6_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "9/12", "题目 6": "13/10
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0", "题目4": "13/18", "题目5": "3/4", "题目6": "13/10"}
```
### 响应时间：2.22秒
### token用量
- total_tokens: 760
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 271 张图片: f7d7d9290fd54cc79eb51a00d9376038_jpg.rf.79dfb009246764d371ef34e9557516d1_with_roboflow_yolo.jpg

==================================================
![f7d7d9290fd54cc79eb51a00d9376038_jpg.rf.79dfb009246764d371ef34e9557516d1_with_roboflow_yolo.jpg](f7d7d9290fd54cc79eb51a00d9376038_jpg.rf.79dfb009246764d371ef34e9557516d1_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"3 5/7","题目 2":"4/3","题目 3":"7","题目 4":"13.75"}
```
### 响应内容：
```json
{"题目1": "3.25 - \\frac{2}{7} + \\frac{3}{7} = 3.25 + \\frac{1}{7} = \\frac{13}{4}+\\frac{1}{7}=\\frac{91 + 4}{28}=\\frac{95}{28}=3\\frac{11}{28}", "题目2": "(\\frac{7}{9}+\\frac{5}{8})-(\\frac{4}{9}+\\frac{3}{8}) = (\\frac{7}{9}-\\frac{4}{9})+(\\frac{5}{8}-\\frac{3}{8}) = \\frac{1}{3}+\\frac{1}{4}=\\frac{4 + 3}{12}=\\frac{7}{12}", "题目3": "8 - 6\\div21 - \\frac{13}{21}=8-(\\frac{6}{21}+\\frac{13}{21}) = 8 - 1 = 7", "题目4": "\\frac{9}{8}+(6.12+\\frac{7}{8})+4.88=(\\frac{9}{8}+\\frac{7}{8})+(6.12 + 4.88)=2 + 11 = 13"}
```
### 响应时间：10.31秒
### token用量
- total_tokens: 1009
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 273 张图片: fb2b1d89d4914a4dbad52f7753e229d9_jpg.rf.25224eceb09b567523c88d8aac290b85_with_roboflow_yolo.jpg

==================================================
![fb2b1d89d4914a4dbad52f7753e229d9_jpg.rf.25224eceb09b567523c88d8aac290b85_with_roboflow_yolo.jpg](fb2b1d89d4914a4dbad52f7753e229d9_jpg.rf.25224eceb09b567523c88d8aac290b85_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"2","题目 2":"1/3","题目 3":"1/10","题目 4":"NAN","题目 5":"3/4","题目 6":"3/10"}
```
### 响应内容：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "1/10", "题目4": "5/18", "题目5": "3/4", "题目6": "3/10"}
```
### 响应时间：1.85秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 276 张图片: fd804f075cab4118bd8202b093f469a2_jpg.rf.9a594142d9ea3da2b10ad4e4908bfbf0_with_roboflow_yolo.jpg

==================================================
![fd804f075cab4118bd8202b093f469a2_jpg.rf.9a594142d9ea3da2b10ad4e4908bfbf0_with_roboflow_yolo.jpg](fd804f075cab4118bd8202b093f469a2_jpg.rf.9a594142d9ea3da2b10ad4e4908bfbf0_with_roboflow_yolo.jpg)

### response_template答案：
```json
{"题目 1":"14/8","题目 2":"11/5","题目 3":"4 1/2"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN"}
```
### 响应时间：2.23秒
### token用量
- total_tokens: 730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
所有错题处理完成！
==================================================
